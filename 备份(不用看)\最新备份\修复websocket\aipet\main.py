import sys
import os # 需要 os 来访问 environ

# --- BEGIN: Add project root to sys.path ---
# Get the directory of the current script (E:\live2d\aipet)
current_script_dir = os.path.dirname(os.path.abspath(__file__))
# Get the parent directory of the script's directory (E:\live2d)
project_root = os.path.dirname(current_script_dir)
# Insert the project root into sys.path at the beginning
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# --- END: Add project root to sys.path ---

import base64
import mimetypes
import json
import html # 用于HTML实体处理
from urllib.parse import urlparse, urlunparse, quote as url_quote, urljoin # url_quote 用于编码URL中的文本
import re # 用于正则表达式操作
import time
import traceback
import socket # 保留以备底层HTTP客户端调试
import http.client # 保留以备底层HTTP客户端调试
from typing import Optional, List, Dict, Any, Tuple
from PyQt5.QtCore import Qt # 恢复导入
import uuid
from datetime import datetime, timezone
import threading
import queue # <--- 添加此行
import hashlib
import live2d.v3 as live2d_core # 确保 live2d_core 被导入
import requests
from PIL import ImageGrab
import io
import asyncio # 新增导入
import edge_tts # 新增导入
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import urlparse, parse_qs
import tempfile
# 安全导入asr模块，避免因缺少依赖导致整个程序无法启动
from aipet.core.resource_manager import (
    TempFileManager, resource_path, configure_ffmpeg_path,
    USER_DATA_FILE_FROM_ROOT, API_KEY, API_BASE_URL, DEFAULT_API_MODEL_NAME,
    LIVE2D_API_PORT, TRAY_ICON_PATH_FROM_ROOT, MODEL_CONFIG_PATH_FROM_ROOT,
    IMAGE_CACHE_DIR, FIXED_IMAGE_CACHE_DIR
)
try:
    import asr # 导入我们修改后的asr.py
    ASR_AVAILABLE = True
    print("✓ ASR模块导入成功")
except ImportError as e:
    print(f"⚠️ ASR模块导入失败: {e}")
    print("提示: 请安装缺少的依赖: pip install onnxruntime jieba")
    print("语音输入功能将不可用，但程序其他功能可以正常使用")
    asr = None
    ASR_AVAILABLE = False

try:
    from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction
    from PyQt5.QtGui import QIcon, QPixmap
    from PyQt5.QtCore import QPoint, QSize, pyqtSignal, QObject, QUrl, QTimer, QThread
    from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
    INITIAL_WINDOW_POS = QPoint(100, 100)
    INITIAL_WINDOW_SIZE = QSize(400, 550)
except ImportError:
    print("错误:主要依赖 PyQt5,但未找到。程序无法启动。请确保已安装 PyQt5。")
    sys.exit(1)

from aipet.live2d_widget import Live2DWidget, LIVE2D_AVAILABLE
from aipet.chat_window import ChatWindow
from aipet.tts_settings_dialog import TTSSettingsDialog
from aipet.floating_button import FloatingButton
from aipet.api_client import APIClient
from aipet.screen_area_selector import ScreenAreaSelector
from aipet.core.tts_manager import TTSManager # +++
from aipet.tts_config_manager import TTSConfigManager, TTSServiceInfo
from aipet.plugin_manager import PluginManager # +++
from aipet.core.asr_manager import ASRManager # +++ NEW
from aipet.core.data_manager import DataManager # +++ NEW

# 新增：导入增强功能模块
try:
    from aipet.modules.selection_assistant import SelectionAssistant
    from aipet.modules.input_enhancer import InputEnhancer
    ENHANCED_FEATURES_AVAILABLE = True
    print("✓ 增强功能模块导入成功")
except ImportError as e:
    ENHANCED_FEATURES_AVAILABLE = False
    print(f"⚠️ 增强功能模块导入失败: {e}")
    print("程序将以基础功能运行")

class AppController(QObject):
    trigger_live2d_action_signal = pyqtSignal(str, str, int)
    trigger_live2d_expression_signal = pyqtSignal(str)
    set_expression_from_api_signal = pyqtSignal(str, float)

    ai_stream_start_signal = pyqtSignal(str, str)  # 修复：支持2个参数 (message_id, sender_override)
    ai_stream_chunk_received_signal = pyqtSignal(str, str)
    ai_stream_image_display_signal = pyqtSignal(str, str, str, object, object)
    ai_stream_raw_block_signal = pyqtSignal(str, str, str)
    ai_stream_finished_signal = pyqtSignal(str, str, object, object, dict)

    tts_play_audio_signal = pyqtSignal(bytes)
    
    # GAG-TTS 参考设置信号
    speaker_reference_updated_signal = pyqtSignal(str, str) # (audio_path, text)

    def __init__(self):
        super().__init__()
        
        # --- 开始：配置并实例化核心工具 ---
        configure_ffmpeg_path()
        
        # 定义并使用自定义的临时文件目录
        aipet_dir = os.path.dirname(os.path.abspath(__file__))
        custom_temp_dir = os.path.join(aipet_dir, "temp")
        self.temp_file_manager = TempFileManager(max_files=20, temp_dir=custom_temp_dir)
        # --- 结束：配置并实例化 ---

        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)

        self.app.setQuitOnLastWindowClosed(True)
        self.app.aboutToQuit.connect(self._cleanup)

        if not LIVE2D_AVAILABLE:
            print("Live2D 库不可用,应用程序无法启动。")
            sys.exit(1)

        self.live2d_widget: Optional[Live2DWidget] = None
        self.chat_window: Optional[ChatWindow] = None
        self.floating_button: Optional[FloatingButton] = None
        self.api_client: Optional[APIClient] = None
        self.tray_icon: Optional[QSystemTrayIcon] = None

        self.config = {}
        self.user_data_file_abs_path = resource_path(USER_DATA_FILE_FROM_ROOT)
        
        self.available_models = ["deepseek-reasoner", "gemini-2.5-flash-preview-05-20", "grok-3-beta", "deepseek-chat", "gemini-2.0-flash","claude-sonnet-4-20250514-claude-ai","claude-sonnet-4-20250514-claude-ai-thinking","gemini-2.5-pro-preview-05-06","gemini-2.5-pro-preview-06-05"]
        
        # --- 数据管理 ---
        # 旧的属性被移除
        self.data_manager = DataManager(self.user_data_file_abs_path, DEFAULT_API_MODEL_NAME)
        # ---

        self._active_ai_message_id: Optional[str] = None
        self._text_accumulator: str = ""
        self._current_message_raw_parts_for_history: List[Dict[str, Any]] = []

        self.screen_selector_instance: Optional[ScreenAreaSelector] = None
        
        self.last_expression_index = 0
        
        self._load_config()
        # self._load_user_data()  # <-- 现在由 DataManager 在初始化时处理
        self._setup_ui()
        self._setup_tray_icon()
        
        # +++ 初始化 TTS 管理器 +++
        self.tts_manager = TTSManager(self)
        # +++ 初始化 ASR 管理器 +++
        self.asr_manager = ASRManager(self)

        # 配置 FFmpeg 路径用于音频处理
        configure_ffmpeg_path()

        # +++
        # 初始化插件管理器
        self.plugin_manager = PluginManager()
        self.plugin_manager.discover_and_load_plugins()
        # ---

        # +++ 初始化增强功能 +++
        self.selection_assistant = None
        self.input_enhancer = None
        if ENHANCED_FEATURES_AVAILABLE:
            try:
                self.selection_assistant = SelectionAssistant(self)
                print("✓ 划词助手已初始化")
            except Exception as e:
                print(f"⚠️ 划词助手初始化失败: {e}")
        # ---
        
        
        # 初始化模型管理器
        self._initialize_model_manager()
        
        self._connect_signals()

        self._start_http_server()

        # ---- 修改后的逻辑：启动时根据当前助手设置 Live2D 模型 (使用 QTimer.singleShot) ----
        if self.chat_window and self.chat_window.assistant_manager and self.live2d_widget:
            current_assistant = self.chat_window.assistant_manager.get_current_assistant()
            if current_assistant and current_assistant.model_path:
                model_path_to_load = current_assistant.model_path
                assistant_name = current_assistant.name
                # 使用 QTimer.singleShot 将模型切换推迟到事件循环开始后
                QTimer.singleShot(0, lambda: self._initial_assistant_model_switch(assistant_name, model_path_to_load))
            elif current_assistant:
                print(f"DEBUG: AppController.__init__: Current assistant '{{current_assistant.name}}' has no model_path defined.")
            else:
                print("DEBUG: AppController.__init__: No current assistant found by AssistantManager. Live2D model will use its default initial load.")
        # ---- 结束修改后的逻辑 ----
        
        # 确保 chat_window.live2d_widget 指向由 _setup_ui() 创建的 live2d_widget 实例
        if self.chat_window and hasattr(self, 'live2d_widget') and self.live2d_widget:
             self.chat_window.live2d_widget = self.live2d_widget
        
        print(f"DEBUG: AppController.__init__: self.live2d_widget (AppController's instance) is {{self.live2d_widget}}")
        if self.chat_window:
            print(f"DEBUG: AppController.__init__: self.chat_window.live2d_widget (ChatWindow's instance) is {{self.chat_window.live2d_widget}}")
        else:
            print(f"DEBUG: AppController.__init__: self.chat_window is None (this should not happen if _setup_ui was successful)")

        # if self.chat_window: # 已注释掉，以实现启动时默认隐藏聊天窗口
        #     self.chat_window.show_window()
        if self.live2d_widget: 
            self.live2d_widget.show()
        
        self._start_http_server()

        # 在所有初始化完成后，加载当前音色的参考设置
        self.tts_manager._initialize_speaker_references()


    def _initial_assistant_model_switch(self, assistant_name: str, model_path: str):
        if self.live2d_widget: # 再次检查 live2d_widget 是否存在
            print(f"DEBUG: AppController._initial_assistant_model_switch: Attempting to set Live2D model for assistant '{{assistant_name}}' to: {{model_path}}")
            self.live2d_widget.switch_model(model_path)
        else:
            print(f"DEBUG: AppController._initial_assistant_model_switch: Live2DWidget is None, cannot switch model.")

    def _initialize_model_manager(self):
        """初始化模型管理器"""
        try:
            # 导入模型管理器
            from aipet.model_manager import ModelManager
            
            # 初始化模型管理器
            self.model_manager = ModelManager()
            
            # 设置当前模型（如果配置中没有指定，使用默认模型）
            current_model = self.model_manager.get_current_model()
            if not current_model:
                # 尝试设置默认模型
                available_models = self.model_manager.get_all_models()
                if 'linghu' in available_models:
                    self.model_manager.set_current_model('linghu')
                    print("设置默认模型: 灵狐芊芊")
                elif available_models:
                    # 使用第一个可用模型
                    first_model_id = next(iter(available_models.keys()))
                    self.model_manager.set_current_model(first_model_id)
                    print(f"设置默认模型: {available_models[first_model_id].name}")
                else:
                    print("警告: 未找到任何可用模型")
            else:
                print(f"当前模型: {current_model.name}")
            
        except Exception as e:
            print(f"初始化模型管理器失败: {e}")
            import traceback
            traceback.print_exc()

    def _ensure_image_cache_dir(self):
        abs_cache_dir = FIXED_IMAGE_CACHE_DIR
        if not os.path.exists(abs_cache_dir):
            try:
                os.makedirs(abs_cache_dir)
                print(f"AppController: 成功创建固定图片缓存目录: '{abs_cache_dir}'")
            except OSError as e:
                print(f"错误: 无法创建固定图片缓存目录 '{abs_cache_dir}': {e}")

    def _download_and_cache_ai_image(self, image_url: str) -> Optional[str]:
        if not image_url: return None
        try:
            print(f"AppController: 开始下载AI图片: {image_url}")
            
            # 确保缓存目录存在
            self._ensure_image_cache_dir()
            
            url_hash = hashlib.md5(image_url.encode('utf-8')).hexdigest()
            parsed_url = urlparse(image_url)
            path_part = parsed_url.path
            _, ext = os.path.splitext(path_part)
            
            # 支持更多图片格式，包括gif、jpg、jpeg、png、webp等
            if not ext or ext.lower() not in ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp']:
                ext = ".png"  # 默认使用png
            
            cache_filename = f"{url_hash}{ext}"
            abs_cache_dir = FIXED_IMAGE_CACHE_DIR
            local_cache_path = os.path.join(abs_cache_dir, cache_filename)
            
            print(f"AppController: 图片将保存到: {local_cache_path}")

            if os.path.exists(local_cache_path):
                print(f"AppController: 图片已存在缓存中: {local_cache_path}")
                return local_cache_path

            print(f"AppController: 开始下载图片到缓存...")
            response = requests.get(image_url, stream=True, timeout=45)
            response.raise_for_status()
            
            with open(local_cache_path, 'wb') as f:
                for chunk_data in response.iter_content(chunk_size=8192):
                    f.write(chunk_data)
            
            print(f"AppController: 图片下载并缓存成功: {local_cache_path}")
            return local_cache_path
            
        except requests.exceptions.RequestException as e:
            print(f"AppController: 下载图片失败 '{image_url}': {e}")
        except IOError as e:
            print(f"AppController: 保存缓存图片失败 '{image_url}': {e}")
        except Exception as e:
            print(f"AppController: 缓存AI图片时发生未知错误 '{image_url}': {e}")
            import traceback
            traceback.print_exc()
        return None

    def _process_and_cache_images_in_text(self, text: str) -> None:
        """从文本中提取HTML图片标签并下载缓存图片"""
        if not text:
            return
            
        print(f"AppController: 检查文本中的图片: {text[:100]}...")  # 调试日志
        
        # 使用正则表达式匹配HTML图片标签，支持更宽松的格式
        img_pattern = r'<img[^>]*src\s*=\s*["\']?([^"\'>\s]+)["\']?[^>]*>'
        matches = re.findall(img_pattern, text, re.IGNORECASE)
        
        print(f"AppController: 正则匹配结果: {matches}")  # 调试日志
        
        for img_url in matches:
            if img_url.startswith('http'):  # 只处理HTTP/HTTPS链接
                print(f"AppController: 在AI回复中发现图片链接: {img_url}")
                cached_path = self._download_and_cache_ai_image(img_url)
                if cached_path:
                    print(f"AppController: 图片已成功缓存到: {cached_path}")
                else:
                    print(f"AppController: 图片缓存失败: {img_url}")
            else:
                print(f"AppController: 跳过非HTTP链接: {img_url}")

    def _replace_img_urls_with_cache_paths(self, text: str) -> str:
        """将文本中的HTML图片标签URL替换为本地缓存路径（如果存在）"""
        if not text:
            return text
            
        import re
        
        def replace_img_src(match):
            img_tag = match.group(0)
            src_url = match.group(1)
            
            if src_url.startswith('http'):
                # 计算缓存文件路径
                url_hash = hashlib.md5(src_url.encode('utf-8')).hexdigest()
                parsed_url = urlparse(src_url)
                path_part = parsed_url.path
                _, ext = os.path.splitext(path_part)
                
                if not ext or ext.lower() not in ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp']:
                    ext = ".png"
                
                cache_filename = f"{url_hash}{ext}"
                cached_path = os.path.join(FIXED_IMAGE_CACHE_DIR, cache_filename)
                
                # 如果缓存文件存在，替换为本地路径
                if os.path.exists(cached_path):
                    print(f"AppController: 历史记录中使用缓存图片: {cached_path}")
                    return img_tag.replace(src_url, cached_path)
                else:
                    print(f"AppController: 缓存图片不存在，尝试重新下载: {src_url}")
                    # 尝试重新下载
                    cached_path = self._download_and_cache_ai_image(src_url)
                    if cached_path:
                        return img_tag.replace(src_url, cached_path)
            
            return img_tag  # 返回原始标签
        
        # 替换所有img标签中的src
        img_pattern = r'<img[^>]*src\s*=\s*["\']?([^"\'>\s]+)["\']?[^>]*>'
        return re.sub(img_pattern, replace_img_src, text, flags=re.IGNORECASE)

    def _capture_screen_to_temp_file(self) -> Optional[str]:
        try:
            img = ImageGrab.grab()
            if not img:
                print("错误: ImageGrab.grab() 未返回图像。")
                return None
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            screenshot_filename = f"screenshot_{uuid.uuid4().hex}.png"
            
            abs_cache_dir = FIXED_IMAGE_CACHE_DIR
            if not os.path.exists(abs_cache_dir): os.makedirs(abs_cache_dir)
            temp_file_path = os.path.join(abs_cache_dir, screenshot_filename)
            with open(temp_file_path, 'wb') as f: f.write(img_bytes)
            return temp_file_path
        except ImportError:
            print("错误: Pillow (PIL) 库未安装或 ImageGrab 不可用。无法进行截图。")
            if self.chat_window: self.chat_window.display_error_message("Pillow库未安装,截图功能不可用。", True)
            return "CAPTURE_DISABLED"
        except Exception as e:
            print(f"AppController: 捕获屏幕截图时发生错误: {e}")
        return None
    def capture_screen_area(self):
        if self.screen_selector_instance and self.screen_selector_instance.isVisible():
            return
        self.screen_selector_instance = ScreenAreaSelector()
        self.screen_selector_instance.area_selected.connect(self._process_area_screenshot)
        self.screen_selector_instance.capture_cancelled.connect(self._handle_area_capture_cancelled)
        self.screen_selector_instance.show()
        QTimer.singleShot(0, lambda: self._activate_selector(self.screen_selector_instance))

    def _activate_selector(self, selector_instance: Optional[ScreenAreaSelector]):
        if selector_instance and selector_instance.isVisible():
            selector_instance.raise_()
            selector_instance.activateWindow()

    def _process_area_screenshot(self, pixmap: QPixmap):
        # 确保聊天窗口重新可见
        self.show_chat_window()

        if not pixmap.isNull():
            self._ensure_image_cache_dir()
            screenshot_filename = f"screenshot_area_{uuid.uuid4().hex}.png"
            
            abs_cache_dir = FIXED_IMAGE_CACHE_DIR
            if not os.path.exists(abs_cache_dir):
                 try:
                     os.makedirs(abs_cache_dir)
                 except OSError as e:
                     print(f"错误: 无法在 _process_area_screenshot 中创建图片缓存目录 '{abs_cache_dir}': {e}")

            temp_file_path = os.path.join(abs_cache_dir, screenshot_filename)
            print(f"AppController: Attempting to save screenshot to: {temp_file_path}")

            if pixmap.save(temp_file_path, "PNG"):
                print(f"AppController: Screenshot saved successfully to {temp_file_path}")
                if self.chat_window:
                    if hasattr(self.chat_window, 'set_selected_screenshot'):
                        self.chat_window.set_selected_screenshot(temp_file_path)
                    else:
                        print("AppController: Warning - ChatWindow does not have set_selected_screenshot method.")
                        self.chat_window.selected_image_path = temp_file_path
                        if hasattr(self.chat_window.input_area, 'update_preview'):
                            self.chat_window.input_area.update_preview()
            else:
                print(f"AppController: Error saving screenshot to {temp_file_path}")
                if self.chat_window: self.chat_window.display_error_message("错误: 保存区域截图失败。", True)
        else:
            print("AppController: Captured pixmap is null.")
            if self.chat_window: self.chat_window.display_error_message("错误: 捕获的区域截图无效。", True)

        if self.screen_selector_instance:
            self.screen_selector_instance.deleteLater()
            self.screen_selector_instance = None
        
        if self.chat_window and hasattr(self.chat_window, 'enable_capture_button'):
            self.chat_window.enable_capture_button()

    def _handle_area_capture_cancelled(self):
        # 确保聊天窗口重新可见
        self.show_chat_window()

        if self.chat_window:
            self.chat_window.add_system_message("截图已取消。")
            
        if self.screen_selector_instance:
            self.screen_selector_instance.deleteLater()
            self.screen_selector_instance = None

        if self.chat_window and hasattr(self.chat_window, 'enable_capture_button'):
            self.chat_window.enable_capture_button()

    def trigger_screenshot_capture(self) -> None:
        if self.chat_window:
            # 1. 禁用按钮防止重复点击
            if hasattr(self.chat_window, 'disable_capture_button'):
                self.chat_window.disable_capture_button()

            # 2. 隐藏窗口
            self.hide_chat_window()
            # 3. 等待隐藏动画完成 (约300ms) 后再开始截图
            QTimer.singleShot(300, self.capture_screen_area)
        else:
            # Fallback
            self.capture_screen_area()

    def capture_full_screen_and_get_path(self) -> Optional[str]:
        """为游戏伴侣模式捕获全屏截图并返回文件路径"""
        try:
            primary_screen = QApplication.primaryScreen()
            if not primary_screen:
                print("AppController: 无法获取主屏幕用于全屏截图")
                return None
            
            pixmap = primary_screen.grabWindow(0)
            if pixmap.isNull():
                print("AppController: 全屏截图失败 (返回的pixmap为空)")
                return None
            
            abs_cache_dir = FIXED_IMAGE_CACHE_DIR
            
            if not os.path.exists(abs_cache_dir):
                try:
                    os.makedirs(abs_cache_dir)
                except OSError as e:
                    print(f"AppController: 无法创建图片缓存目录 '{abs_cache_dir}': {e}")
                    return None
            
            screenshot_filename = f"fullscreen_auto_{uuid.uuid4().hex}.png"
            temp_file_path = os.path.join(abs_cache_dir, screenshot_filename)
            
            if pixmap.save(temp_file_path, "PNG"):
                print(f"AppController: 全屏截图已保存到 {temp_file_path}")
                return temp_file_path
            else:
                print(f"AppController: 保存全屏截图失败到 {temp_file_path}")
                return None
                
        except Exception as e:
            print(f"AppController: 截取全屏时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _generate_topic_id(self) -> str:
        return uuid.uuid4().hex

    def _create_new_topic(self, title: Optional[str] = None, set_as_active: bool = True) -> str:
        topic_id = self._generate_topic_id()
        now_iso = datetime.now(timezone.utc).isoformat()
        default_title_timestamp = now_iso.split('T')[0] + "_" + now_iso.split('T')[1].split('.')[0].replace(':','-')
        new_topic_data = {
            "id": topic_id,
            "title": title if title else f"话题_{default_title_timestamp}",
            "created_at": now_iso,
            "last_updated_at": now_iso,
            "conversation_history": []
        }
        self.topics_data[topic_id] = new_topic_data
        if set_as_active:
            self.active_topic_id = topic_id
            self.conversation_history = new_topic_data["conversation_history"]
        self.initial_topic_created = True
        return topic_id

    def _create_initial_topic(self):
        if not self.topics_data:
            self._create_new_topic(title="初始对话", set_as_active=True)
        elif not self.active_topic_id or self.active_topic_id not in self.topics_data:
            if self.topics_data:
                try:
                    latest_topic_id = max(self.topics_data.keys(), key=lambda tid: self.topics_data[tid].get('last_updated_at', '1970-01-01T00:00:00Z'))
                    if latest_topic_id:
                        self.active_topic_id = latest_topic_id
                        self.conversation_history = self.topics_data[self.active_topic_id].get("conversation_history", [])
                        self.initial_topic_created = True
                        return
                except ValueError:
                     pass 
            self._create_new_topic(title="新对话", set_as_active=True)

    def _load_user_data(self):
        try:
            if os.path.exists(self.user_data_file_abs_path):
                with open(self.user_data_file_abs_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # self.system_prompt = data.get("system_prompt", self.system_prompt) # Removed
                
                loaded_model_name = data.get("api_model_name", self.config.get('api_model_name', DEFAULT_API_MODEL_NAME))
                if loaded_model_name in self.available_models:
                    self.config['api_model_name'] = loaded_model_name
                else:
                    print(f"警告: 从用户数据加载的模型 '{loaded_model_name}' 不在可用模型列表中,将使用 '{DEFAULT_API_MODEL_NAME}'。")
                    self.config['api_model_name'] = DEFAULT_API_MODEL_NAME
                
                self.topics_data = data.get("topics", {})
                self.active_topic_id = data.get("active_topic_id")

                if self.active_topic_id and self.active_topic_id in self.topics_data:
                    self.conversation_history = self.topics_data[self.active_topic_id].get("conversation_history", [])
                elif self.topics_data: 
                    try:
                        latest_topic_id = max(self.topics_data.keys(), key=lambda tid: self.topics_data[tid].get('last_updated_at', '1970-01-01T00:00:00Z'))
                        if latest_topic_id:
                            self.active_topic_id = latest_topic_id
                            self.conversation_history = self.topics_data[self.active_topic_id].get("conversation_history", [])
                    except ValueError: 
                        self._create_initial_topic()
                else: 
                    self._create_initial_topic()
            else: 
                self._create_initial_topic()
        except json.JSONDecodeError:
            print(f"警告: 用户数据文件 '{self.user_data_file_abs_path}' 解析失败,将创建新的数据。")
            self._create_initial_topic()
        except Exception as e:
            print(f"AppController: 加载用户数据时发生未知错误 '{self.user_data_file_abs_path}': {e},将创建新的数据。")
            self._create_initial_topic()
        
        if 'api_model_name' not in self.config or not self.config['api_model_name']:
            self.config['api_model_name'] = DEFAULT_API_MODEL_NAME
            print(f"AppController: 未找到已保存的模型,使用默认模型: {DEFAULT_API_MODEL_NAME}")

        if not self.active_topic_id and self.topics_data:
            try:
                latest_topic_id = max(self.topics_data.keys(), key=lambda tid: self.topics_data[tid].get('last_updated_at', '1970-01-01T00:00:00Z'))
                if latest_topic_id:
                    self.active_topic_id = latest_topic_id
                    self.conversation_history = self.topics_data[self.active_topic_id].get("conversation_history", [])
            except ValueError:
                self._create_initial_topic() 
        elif not self.active_topic_id and not self.topics_data : 
            self._create_initial_topic()

    def _save_user_data(self):
        if self.active_topic_id and self.active_topic_id in self.topics_data:
            current_topic = self.topics_data[self.active_topic_id]
            current_topic["conversation_history"] = self.conversation_history[-100:] 
            current_topic["last_updated_at"] = datetime.now(timezone.utc).isoformat()
        
        data_to_save = {
            # "system_prompt": self.system_prompt, # Removed
            "api_model_name": self.config.get('api_model_name', DEFAULT_API_MODEL_NAME), 
            "active_topic_id": self.active_topic_id,
            "topics": self.topics_data
        }
        try:
            with open(self.user_data_file_abs_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"AppController: 保存用户数据时发生错误 '{self.user_data_file_abs_path}': {e}")

    def _load_config(self):
        self.config['model_path'] = resource_path(MODEL_CONFIG_PATH_FROM_ROOT) 
        window_width = INITIAL_WINDOW_SIZE.width()
        window_height = INITIAL_WINDOW_SIZE.height()
        screen = self.app.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            margin = 20
            new_x = screen_geometry.left() + margin
            new_y = screen_geometry.top() + margin
            self.config['initial_pos_x'] = new_x
            self.config['initial_pos_y'] = new_y
        else:
            self.config['initial_pos_x'] = INITIAL_WINDOW_POS.x()
            self.config['initial_pos_y'] = INITIAL_WINDOW_POS.y()
        self.config['initial_width'] = window_width
        self.config['initial_height'] = window_height
        self.config['api_base_url'] = API_BASE_URL
        self.config['api_model_name'] = self.data_manager.api_model_name
        self.config['api_key'] = API_KEY

        model_path_to_check = self.config['model_path']
        if not os.path.exists(model_path_to_check): 
            print(f"警告: 找不到模型文件: '{model_path_to_check}'. Live2D 模型可能无法加载。")

    def _setup_ui(self):

        # 获取小部件的初始大小
        initial_width = self.config.get("live2d_initial_width", 400)
        initial_height = self.config.get("live2d_initial_height", 550)
        size = QSize(initial_width, initial_height)

        # 尝试从配置中获取位置
        config_pos_x = self.config.get("live2d_initial_pos_x")
        config_pos_y = self.config.get("live2d_initial_pos_y")

        if config_pos_x is None or config_pos_y is None or \
           (isinstance(config_pos_x, (int, float)) and config_pos_x == 0 and
            isinstance(config_pos_y, (int, float)) and config_pos_y == 0):
            # 如果配置中没有位置，或者位置是 (0,0) (旧的左上角默认), 则计算右下角位置
            app_instance = QApplication.instance() # QApplication 应已在 main() 中创建
            if app_instance and app_instance.primaryScreen():
                screen_geometry = app_instance.primaryScreen().availableGeometry()
                pos_x = max(0, screen_geometry.width() - initial_width)
                pos_y = max(0, screen_geometry.height() - initial_height)
                print(f"DEBUG: Calculating initial Live2D position for bottom-right: ({pos_x}, {pos_y})")
            else: # Fallback if screen info not available
                pos_x = 100 # Fallback to a default if screen info fails
                pos_y = 100
                print("DEBUG WARNING: Could not get screen geometry, defaulting Live2D position to (100,100).")
            pos = QPoint(pos_x, pos_y)
        else:
            # 使用配置文件中指定的位置
            pos = QPoint(int(config_pos_x), int(config_pos_y)) # Ensure int for QPoint
            print(f"DEBUG: Using configured initial Live2D position: ({pos.x()}, {pos.y()})")

        # 使用模型管理器中的当前模型路径
        current_model_path = self.config.get('model_path', resource_path("linghu/芊芊.model3.json"))
        if hasattr(self, 'model_manager'):
            current_model = self.model_manager.get_current_model()
            if current_model:
                # 转换为绝对路径
                if not os.path.isabs(current_model.path):
                    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                    current_model_path = os.path.join(project_root, current_model.path)
                else:
                    current_model_path = current_model.path
                print(f"使用模型管理器中的模型: {current_model.name} ({current_model_path})")
        
        self.live2d_widget = Live2DWidget(model_config_path=current_model_path, initial_pos=pos, initial_size=size)
        self.chat_window = ChatWindow(app_controller=self)
        self.floating_button = FloatingButton()
        api_url = self.config.get('api_base_url')
        api_key = self.config.get('api_key')
        if not api_url or api_url == "YOUR_OPENAI_COMPATIBLE_API_ENDPOINT" or not str(api_key).strip(): 
            if self.chat_window: self.chat_window.display_error_message("AI服务未配置 (URL或API Key缺失),无法连接。", True)
            self.api_client = None
        else:
            self.api_client = APIClient(base_url=api_url, api_key=str(api_key))

        # 设置聊天窗口的初始位置到右上角
        if self.chat_window:
            # 使用 ChatWindow 中定义的默认尺寸或从配置中获取
            # (假设 CHAT_WINDOW_WIDTH 和 CHAT_WINDOW_HEIGHT 是可访问的常量或从配置读取)
            # 从 chat_window.py 的 __init__ 可知 CHAT_WINDOW_WIDTH = 420, CHAT_WINDOW_HEIGHT = 700
            chat_width = self.config.get("chat_window_width", 420)
            chat_height = self.config.get("chat_window_height", 700) # 虽然高度对右上角定位影响不大，但保持一致

            app_instance = QApplication.instance()
            if app_instance and app_instance.primaryScreen():
                screen_geometry = app_instance.primaryScreen().availableGeometry()
                
                # 定义偏移量 (您可以稍后调整这个值)
                offset_pixels = 60 # 大约2cm的估算值

                # 计算基础右上角位置
                base_chat_x = screen_geometry.width() - chat_width
                base_chat_y = screen_geometry.top()

                # 应用偏移量
                chat_x = max(0, base_chat_x - offset_pixels) # 向左移动
                chat_y = base_chat_y + offset_pixels         # 向下移动
                
                # 确保窗口不会因偏移而超出屏幕底部
                chat_y = min(chat_y, screen_geometry.height() - chat_height)
                # 确保窗口不会因偏移而超出屏幕顶部 (虽然向下移动不太可能发生这种情况)
                chat_y = max(screen_geometry.top(), chat_y)


                self.chat_window.move(chat_x, chat_y)
                print(f"DEBUG: Set initial ChatWindow position to top-right: ({chat_x}, {chat_y})")
            else:
                print("DEBUG WARNING: Could not get screen geometry for ChatWindow positioning.")

        # +++ 初始化输入增强器 +++
        if ENHANCED_FEATURES_AVAILABLE and self.chat_window and hasattr(self.chat_window, 'input_area'):
            try:
                self.input_enhancer = InputEnhancer(self.chat_window.input_area, self)
                self.input_enhancer.setup_enhanced_input()
                print("✓ 输入增强器已初始化")
            except Exception as e:
                print(f"⚠️ 输入增强器初始化失败: {e}")
        # ---

    def _setup_tray_icon(self):
        if not QSystemTrayIcon.isSystemTrayAvailable(): return
        self.tray_icon = QSystemTrayIcon(self)
        icon_path = resource_path(TRAY_ICON_PATH_FROM_ROOT) 
        if os.path.exists(icon_path): self.tray_icon.setIcon(QIcon(icon_path))
        else: print(f"警告: 托盘图标文件未找到: {icon_path}。")
        self.tray_icon.setToolTip("AI 桌宠")
        tray_menu = QMenu()
        self.toggle_live2d_action = QAction("显示/隐藏模型", self, checkable=True)
        self.toggle_live2d_action.setChecked(self.live2d_widget.isVisible() if self.live2d_widget else True)
        self.toggle_live2d_action.triggered.connect(self._toggle_live2d_visibility)
        tray_menu.addAction(self.toggle_live2d_action)
        self.toggle_chat_action = QAction("显示/隐藏聊天", self, checkable=True)
        self.toggle_chat_action.setChecked(self.chat_window.isVisible() if self.chat_window else False) 
        self.toggle_chat_action.triggered.connect(self._toggle_chat_components_visibility_from_tray)
        tray_menu.addAction(self.toggle_chat_action)
        tray_menu.addSeparator()
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self._quit_application)
        tray_menu.addAction(quit_action)
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()
        self.tray_icon.activated.connect(self._on_tray_icon_activated)

    def _on_tray_icon_activated(self, reason):
        if reason == QSystemTrayIcon.ActivationReason.Trigger: 
            self._toggle_chat_components_visibility_from_tray()

    def _toggle_live2d_visibility(self):
        if self.live2d_widget:
            is_visible = not self.live2d_widget.isVisible()
            self.live2d_widget.setVisible(is_visible)
            if hasattr(self, 'toggle_live2d_action'): self.toggle_live2d_action.setChecked(is_visible)

    def _toggle_chat_components_visibility_from_tray(self):
        if self.chat_window and self.floating_button:
            # 直接使用动作的勾选状态来决定是显示还是隐藏
            # 这样可以避免程序性地 setChecked 触发信号时导致的逻辑错误
            should_be_visible = self.toggle_chat_action.isChecked()

            if should_be_visible:
                self.show_chat_window()
            else:
                self.hide_chat_window()

    def _connect_signals(self):
        if self.live2d_widget:
            self.live2d_widget.model_loaded_signal.connect(self._on_model_loaded)
            self.live2d_widget.window_dragged_signal.connect(self._on_window_dragged)
            self.live2d_widget.model_clicked_signal.connect(self._on_live2d_model_part_clicked)
            self.live2d_widget.stop_tts_playback_signal.connect(self.tts_manager.stop_playback)
        if self.chat_window:
            self.chat_window.send_composed_message_signal.connect(self._handle_composed_message)
            self.chat_window.system_prompt_changed_signal.connect(self._on_system_prompt_changed)
            self.chat_window.topic_selected_signal.connect(self._switch_active_topic)
            self.chat_window.new_topic_requested_signal.connect(self._handle_new_topic_request)
            self.chat_window.topic_deleted_signal.connect(self.delete_topic)  # 新增：连接删除话题信号
            if hasattr(self.chat_window, 'model_selected_signal'):
                self.chat_window.model_selected_signal.connect(self.change_model)
            
            # Live2D模型切换信号连接
            if hasattr(self.chat_window, 'live2d_model_switch_requested'):
                self.chat_window.live2d_model_switch_requested.connect(self.switch_live2d_model)
        
        # AI -> UI 信号连接
        self.ai_stream_start_signal.connect(self.chat_window.handle_app_ai_stream_start)
        self.ai_stream_chunk_received_signal.connect(self.chat_window.handle_app_ai_stream_chunk)
        self.ai_stream_image_display_signal.connect(self.chat_window.handle_app_ai_image_display)
        self.ai_stream_raw_block_signal.connect(self.chat_window.handle_app_ai_raw_block)
        self.ai_stream_finished_signal.connect(self.chat_window.handle_ai_stream_finished)
        self.ai_stream_finished_signal.connect(self._finalize_ai_response)
        self.chat_window.window_hidden_signal.connect(self._on_chat_window_hidden) # <--- 新增连接

        if self.floating_button:
            self.floating_button.clicked.connect(self._toggle_chat_window_visibility)
        
        self.trigger_live2d_expression_signal.connect(self._execute_live2d_expression_from_signal)
        self.set_expression_from_api_signal.connect(self._handle_set_expression_from_api)
        
        # ASR信号连接
        if hasattr(self, 'asr_manager'):
            self.asr_manager.text_recognized_signal.connect(self._handle_recognized_text_from_asr)
            self.asr_manager.status_changed_signal.connect(self._handle_asr_status_change_for_ui)
            self.asr_manager.error_signal.connect(lambda msg: self.display_error_message(msg, True))

        # GAG-TTS 参考设置信号连接
        if hasattr(self, 'chat_window') and self.chat_window:
             self.speaker_reference_updated_signal.connect(self.chat_window.update_gag_reference_fields)

        # 你的其他信号连接...
        self.chat_window.open_settings_signal.connect(self._open_tts_settings_dialog)

    def _on_chat_window_hidden(self):
        """当聊天窗口被隐藏时调用"""
        if self.floating_button and not self.floating_button.isVisible():
            self.floating_button.show()
            print("AppController: Chat window hidden, showing floating button.")
        # 同时，更新托盘菜单中聊天窗口的勾选状态
        if hasattr(self, 'toggle_chat_action') and self.chat_window:
            self.toggle_chat_action.setChecked(False) # 因为聊天窗口隐藏了

    def _on_model_loaded(self, success: bool):
        if not success: print("AppController: 模型加载失败。")

    def _on_window_dragged(self, new_pos: QPoint):
        self.config['initial_pos_x'] = new_pos.x()
        self.config['initial_pos_y'] = new_pos.y()

    def _on_live2d_model_part_clicked(self, part_id: str):
        if self.live2d_widget and self.live2d_widget.model:
            available_expressions = self.live2d_widget.get_available_expressions()
            if available_expressions:
                self.last_expression_index = (self.last_expression_index + 1) % len(available_expressions)
                self.live2d_widget.set_expression(available_expressions[self.last_expression_index])
            elif self.live2d_widget.available_motion_groups:
                import random
                random_group = random.choice(list(self.live2d_widget.available_motion_groups.keys()))
                if self.live2d_widget.available_motion_groups[random_group]:
                    self.live2d_widget.model.StartRandomMotion(random_group, priority=2)

    def _on_system_prompt_changed(self, new_prompt: str):
        # self.system_prompt = new_prompt.strip() if new_prompt and new_prompt.strip() else None
        self.data_manager.save_data()
        if self.chat_window:
            self.chat_window.display_error_message(f"系统提示词已更新。", is_critical=False) 

    def _initialize_stream_processing_state(self):
        self._active_ai_message_id = f"ai_{uuid.uuid4().hex}"
        self._text_accumulator = ""
        self._current_message_raw_parts_for_history = []
        self.ai_stream_start_signal.emit(self._active_ai_message_id, "AI")

    def _switch_active_topic(self, topic_id: str):
        if self.data_manager.switch_active_topic(topic_id):
            if self.chat_window:
                self.chat_window.clear_chat_display() 
                history = self.data_manager.get_conversation_history()
                self._populate_chat_window_with_history(history)
            self.data_manager.save_data()

    def _handle_new_topic_request(self):
        self.data_manager.create_new_topic()
        if self.chat_window:
            self.chat_window.clear_chat_display()
        self.data_manager.save_data()

    def get_all_topics_for_display(self) -> List[Dict[str, str]]:
        return self.data_manager.get_all_topics_for_display()

    def delete_topic(self, topic_id: str) -> bool:
        """删除指定的话题"""
        if self.data_manager.delete_topic(topic_id):
            self.data_manager.save_data()
            if self.chat_window:
                # 刷新聊天内容
                self.chat_window.clear_chat_display()
                history = self.data_manager.get_conversation_history()
                self._populate_chat_window_with_history(history)
            return True
        return False

    def _populate_chat_window_with_history(self, history_list: List[Dict[str, Any]]):
        if self.chat_window:
            if hasattr(self.chat_window, 'populate_with_history'):
                 self.chat_window.populate_with_history(history_list)
            else: 
                print("ChatWindow does not have populate_with_history, using direct signals (might be slow).")
                self.chat_window.clear_chat_display() 
                for message in history_list:
                    role = message.get("role", "user") 
                    content_parts = message.get("parts", [])
                    
                    sender_for_ui = "User" if role == "user" else "AI"
                    if role == "model_error" or role == "ai_error": sender_for_ui = "AI_Error"
                    elif role == "system_tool": sender_for_ui = "系统工具"

                    history_message_id = f"hist_{uuid.uuid4().hex}"
                    self.ai_stream_start_signal.emit(history_message_id, sender_for_ui)
                    
                    full_text_for_message = ""
                    for part in content_parts:
                        if part.get("type") == "text":
                            text_chunk = part.get("text", "")
                            
                            # 调试：检查历史记录文本内容
                            if "<img" in text_chunk:
                                print(f"AppController: 历史记录包含图片标签: {text_chunk[:100]}...")
                            
                            # 处理历史记录中的HTML图片标签，将URL替换为缓存路径
                            processed_text = self._replace_img_urls_with_cache_paths(text_chunk)
                            
                            if processed_text != text_chunk:
                                print(f"AppController: 文本已处理，原长度: {len(text_chunk)}, 新长度: {len(processed_text)}")
                            
                            self.ai_stream_chunk_received_signal.emit(history_message_id, processed_text)
                            full_text_for_message += processed_text
                        elif part.get("type") == "image_url":
                            img_url = part.get("url")
                            # 下载并缓存AI图片
                            if img_url:
                                cached_img_path = self._download_and_cache_ai_image(img_url)
                                if cached_img_path:
                                    self.ai_stream_chunk_received_signal.emit(history_message_id, f"[图片已缓存: {cached_img_path}]\n")
                                    full_text_for_message += f"[图片已缓存: {cached_img_path}]\n"
                                else:
                                    self.ai_stream_chunk_received_signal.emit(history_message_id, f"[图片下载失败: {img_url}]\n")
                                    full_text_for_message += f"[图片下载失败: {img_url}]\n"
                            else:
                                self.ai_stream_chunk_received_signal.emit(history_message_id, f"[图片: {img_url}]\n")
                                full_text_for_message += f"[图片: {img_url}]\n"
                        elif part.get("type") == "tool_code": 
                            tool_code_content = part.get("code","")
                            self.ai_stream_raw_block_signal.emit(history_message_id, "tool_request", tool_code_content)
                            full_text_for_message += f"[工具请求]\n{tool_code_content}\n"
                        # +++
                        elif part.get("type") == "tool_call":
                             function_name = part.get("function", {}).get("name", "")
                             arguments = part.get("function", {}).get("arguments", "")
                             tool_code_content = f"Function: {function_name}\nArguments: {arguments}"
                             self.ai_stream_raw_block_signal.emit(history_message_id, "tool_request", tool_code_content)
                             full_text_for_message += f"[工具请求]\n{tool_code_content}\n"
                        elif part.get("type") == "tool_response":
                             tool_name = part.get("name", "unknown_tool")
                             tool_response_content = part.get("content", "")
                             self.ai_stream_raw_block_signal.emit(history_message_id, "tool_result", f"Result for {tool_name}:\n{tool_response_content}")
                             full_text_for_message += f"[工具响应]\n{tool_response_content}\n"
                        # ---

                    self.ai_stream_finished_signal.emit(history_message_id, full_text_for_message, None, None, {"is_final_stream_end": True})

    def _handle_composed_message(self, message_text: str,
                           image_path: Optional[str] = None,
                           audio_data_url: Optional[str] = None):
        
        # 新增：发送新消息时，停止当前可能正在播放的TTS
        if self.tts_manager.tts_config_manager.get_global_settings().get("enable_auto_stop_on_new_message", True):
            print("AppController: New message received, stopping current TTS playback.")
            self.tts_manager.stop_playback(triggered_by_user_action=False)
        
        if not self.api_client:
            if self.chat_window: self.chat_window.display_error_message("AI 服务未初始化。", True)
            return

        # 构建用户消息内容
        user_message_parts = []
        if message_text:
            user_message_parts.append({"type": "text", "text": message_text})
        
        # 处理图片
        if image_path and os.path.exists(image_path):
            try:
                with open(image_path, "rb") as img_file:
                    img_base64 = base64.b64encode(img_file.read()).decode()
                mime_type = mimetypes.guess_type(image_path)[0] or "image/jpeg"
                
                # 添加到用户消息部分
                user_message_parts.append({
                    "type": "image_url",
                    "url": f"data:{mime_type};base64,{img_base64}"
                })
                print(f"AppController: 图片已编码为base64，类型: {mime_type}")
            except Exception as e:
                print(f"AppController: 处理图片失败: {e}")
                if self.chat_window:
                    self.chat_window.display_error_message(f"图片处理失败: {e}", True)
                return

        if not user_message_parts:
            return

        # 保存用户消息到历史
        current_history = self.data_manager.get_conversation_history()
        current_history.append({
            "role": "user",
            "parts": user_message_parts
        })
        self.data_manager.update_conversation_history(current_history)
        
        # 构建API消息格式
        api_messages = []
        
        # 添加系统消息
        current_system_prompt = None
        if self.chat_window and hasattr(self.chat_window, 'assistant_manager') and self.chat_window.assistant_manager:
            current_assistant = self.chat_window.assistant_manager.get_current_assistant()
            if current_assistant:
                current_system_prompt = current_assistant.system_prompt
        
        # +++
        # --- 增强的系统指令 ---
        tool_handling_instructions = (
            "You have been granted permission to access the user's entire file system using the provided tools. The application code is responsible for security, so you do not need to worry about path safety.\n\n"
            "When you use a tool, you MUST follow these rules:\n"
            "1. When a user provides an absolute path (e.g., 'C:/' or '/'), you MUST use the file tools as requested. DO NOT refuse based on your own safety concerns.\n"
            "2. Your final summary response MUST be under 50 characters. This is a very strict rule. Be extremely concise.\n"
            "3. After getting the result from a tool, DO NOT just repeat the raw output.\n"
            "4. Instead, you should analyze the tool's output to formulate a helpful, natural language response that directly answers the user's original question.\n"
            "5. For example, if the user asks 'what's in file X?', you should reply with something like: 'File read. It's a guide on TTS services and configs.' DO NOT provide a long, paragraph-by-paragraph summary.\n"
            "6. Always behave like a helpful assistant, not a raw command executor."
        )

        # 4. 构建最终的系统提示词
        if current_system_prompt:
            final_system_prompt = f"{current_system_prompt}\n\n{tool_handling_instructions}"
        else:
            final_system_prompt = tool_handling_instructions

        # 在这里解析系统提示中的占位符
        if final_system_prompt:
            final_system_prompt = self.plugin_manager.resolve_placeholders_in_text(final_system_prompt)
            print(f"解析占位符后的系统提示: {final_system_prompt}") # 增加日志

        # 5. 将系统提示词添加到消息列表的开头
        if final_system_prompt: # 使用 final_system_prompt
            # 确保 message 对象中的 system-prompt 是最新的
            # 这通常在切换助手或编辑提示词时完成，但在这里最后检查一次
            api_messages.append({
                "role": "system",
                "content": final_system_prompt
            })
        
        # 添加历史消息(转换为标准格式)
        for msg in self.data_manager.get_conversation_history()[-10:]:  # 最近10条
            role = msg.get("role")
            parts = msg.get("parts", [])
            content = msg.get("content", "")  # 处理简单的content字段
            
            # 构建内容
            if isinstance(content, str) and content:
                # 简单字符串内容
                final_content = content
            elif isinstance(parts, list) and parts:
                # parts格式内容
                if len(parts) == 1 and parts[0].get("type") == "text":
                    # 单纯文本，简化为字符串
                    final_content = parts[0].get("text", "")
                else:
                    # 复杂内容（可能包含图片），使用数组格式
                    final_content = []
                    for part in parts:
                        if part.get("type") == "text":
                            final_content.append({"type": "text", "text": part.get("text", "")})
                        elif part.get("type") == "image_url":
                            final_content.append({
                                "type": "image_url", 
                                "image_url": {"url": part.get("url", "")}
                            })
            else:
                continue  # 跳过无效消息
            
            if final_content and role in ["user", "assistant", "model"]:
                api_messages.append({
                    "role": "user" if role == "user" else "assistant",
                    "content": final_content
                })
                        # 添加当前用户消息
        if len(user_message_parts) == 1 and user_message_parts[0].get("type") == "text":
            # 纯文本消息，简化格式
            api_messages.append({
                "role": "user",
                "content": user_message_parts[0].get("text", "")
            })
        else:
            # 包含图片或多部分内容
            api_content = []
            for part in user_message_parts:
                if part.get("type") == "text":
                    api_content.append({"type": "text", "text": part.get("text", "")})
                elif part.get("type") == "image_url":
                    api_content.append({
                        "type": "image_url",
                        "image_url": {"url": part.get("url", "")}
                    })
            api_messages.append({
                "role": "user", 
                "content": api_content
            })
        
        self._initialize_stream_processing_state()
        
        # 在线程中发送API请求
        threading.Thread(target=self._run_ai_conversation_flow, args=(api_messages,), daemon=True).start()

    def _run_ai_conversation_flow(self, messages: List[Dict[str, Any]]):
        """
        处理与AI的完整对话流程，包括多轮工具调用。
        """
        try:
            current_messages = list(messages)
            
            while True:  # 循环以支持多轮工具调用
                available_tools = self.plugin_manager.get_all_tool_definitions()

                response_generator = self.api_client.send_chat_message(
                    messages=current_messages,
                    model=self.data_manager.api_model_name,
                    stream=True,
                    tools=available_tools
                )

                accumulated_text = ""
                pending_tool_calls = []
                ai_response_parts_for_history = []
                
                for event_type, event_data in response_generator:
                    if event_type == "ai_delta":
                        text_chunk = event_data.get("content", "")
                        if text_chunk:
                            accumulated_text += text_chunk
                            self.ai_stream_chunk_received_signal.emit(self._active_ai_message_id, text_chunk)
                            ai_response_parts_for_history.append({"type": "text", "text": text_chunk})
                    
                    elif event_type == "tool_call_request":
                        tool_calls = event_data.get("tool_calls", [])
                        pending_tool_calls.extend(tool_calls)
                        # for tool_call in tool_calls:
                        #     function_name = tool_call.get("function", {}).get("name", "")
                        #     arguments = tool_call.get("function", {}).get("arguments", "")
                        #     self.ai_stream_raw_block_signal.emit(
                        #         self._active_ai_message_id, "tool_request", f"Function: {function_name}\nArguments: {arguments}"
                        #     )
                        ai_response_parts_for_history.extend([
                            {"type": "tool_call", "function": tc.get("function"), "id": tc.get("id")} for tc in tool_calls
                        ])

                    elif event_type == "error":
                        error_msg = event_data.get("message", "未知错误")
                        print(f"API 错误: {error_msg}")
                        if self.chat_window: self.chat_window.display_error_message(f"AI 服务错误: {error_msg}", True)
                        self.ai_stream_finished_signal.emit(self._active_ai_message_id, "", None, None, {"is_final_stream_end": True, "is_error": True})
                        return

                    elif event_type == "stream_end_signal":
                        break
                
                # 流结束后处理完整文本中的图片
                if accumulated_text:
                    print(f"AppController: 流结束，处理完整文本中的图片...")
                    self._process_and_cache_images_in_text(accumulated_text)
                
                # 将AI的回复（可能是文本+工具调用）保存到历史记录
                if ai_response_parts_for_history:
                    # +++ 新增：获取当前助手信息 +++
                    current_assistant = self.chat_window.assistant_manager.get_current_assistant()
                    if current_assistant:
                        assistant_id_to_save = current_assistant.id
                        assistant_name_to_save = current_assistant.name
                    else:
                        # 后备措施，以防万一获取不到助手
                        assistant_id_to_save = None
                        assistant_name_to_save = "AI"
                        print("警告: _run_ai_conversation_flow 中未能获取当前助手信息。")

                    # +++ 修改：创建包含助手ID和名称的完整历史条目 +++
                    # 合并连续的文本parts，避免HTML标签被拆分
                    merged_parts = []
                    current_text = ""
                    
                    for part in ai_response_parts_for_history:
                        if part.get("type") == "text":
                            current_text += part.get("text", "")
                        else:
                            # 遇到非文本part，先保存累积的文本
                            if current_text:
                                merged_parts.append({"type": "text", "text": current_text})
                                current_text = ""
                            merged_parts.append(part)
                    
                    # 保存最后的文本
                    if current_text:
                        merged_parts.append({"type": "text", "text": current_text})
                    
                    history_entry = {
                        "role": "assistant",
                        "parts": merged_parts,
                        "assistant_id": assistant_id_to_save,
                        "assistant_name": assistant_name_to_save
                    }
                    
                    # 调试：检查保存的历史记录内容
                    print(f"AppController: 保存历史记录，合并后parts数量: {len(merged_parts)}")
                    for i, part in enumerate(merged_parts):
                        if part.get("type") == "text":
                            text_preview = part.get("text", "")[:100] + "..." if len(part.get("text", "")) > 100 else part.get("text", "")
                            print(f"  Part {i}: type=text, content={text_preview}")
                        else:
                            print(f"  Part {i}: type={part.get('type')}, content={part}")
                    
                    current_history = self.data_manager.get_conversation_history()
                    current_history.append(history_entry)
                    self.data_manager.update_conversation_history(current_history)
                    # --- 修复结束 ---

                if pending_tool_calls:
                    print(f"🛠️ AI请求执行 {len(pending_tool_calls)} 个工具。")
                    
                    # 准备下一轮API调用的消息列表
                    # 必须包含AI的工具调用请求
                    current_messages.append({"role": "assistant", "content": None, "tool_calls": pending_tool_calls})

                    for tool_call in pending_tool_calls:
                        function_name = tool_call.get("function", {}).get("name", "")
                        arguments_str = tool_call.get("function", {}).get("arguments", "{}")
                        tool_call_id = tool_call.get("id")

                        result_str = self.plugin_manager.execute_tool(function_name, arguments_str)
                        
                        # self.ai_stream_raw_block_signal.emit(self._active_ai_message_id, "tool_result", f"Result for {function_name}:\n{result_str}")
                        
                        tool_result_message_for_api = {
                            "role": "tool",
                            "tool_call_id": tool_call_id,
                            "name": function_name,
                            "content": result_str
                        }
                        # 添加到下一轮请求的消息列表中
                        current_messages.append(tool_result_message_for_api)

                        # 同时保存到历史记录（使用我们自己的格式）
                        current_history = self.data_manager.get_conversation_history()
                        current_history.append({
                            "role": "tool_response", 
                            "type": "tool_response",
                            "tool_call_id": tool_call_id,
                            "name": function_name,
                            "content": result_str
                        })
                        self.data_manager.update_conversation_history(current_history)
                    
                    # 继续循环，用工具结果再次调用API
                    continue
                else:
                    # 没有工具调用，是最终的文本回复
                    print("💬 AI已提供最终文本回复。")
                    self.ai_stream_finished_signal.emit(self._active_ai_message_id, accumulated_text, None, None, {"is_final_stream_end": True})
                    break  # 退出循环

        except Exception as e:
            print(f"AI对话流程发生错误: {e}")
            import traceback
            traceback.print_exc()
            if self.chat_window: self.chat_window.display_error_message(f"网络或API错误: {e}", True)
            self.ai_stream_finished_signal.emit(self._active_ai_message_id, "", None, None, {"is_final_stream_end": True, "is_error": True})

    def _finalize_ai_response(self, message_id: str, full_text_for_tts: str,
                              _original_message_obj: Optional[Any] = None,
                              _last_event_data: Optional[Dict[str, Any]] = None,
                              metadata: Optional[Dict[str, Any]] = None):
        metadata = metadata or {}
        is_error = metadata.get("is_error", False)
        
        print(f"AppController: Finalizing AI response for message_id: {message_id}, TTS text: '{full_text_for_tts[:30]}...', IsError: {is_error}")
        
        # --- 开始：替换为新的TTS调用逻辑 ---
        if self.tts_manager.get_tts_enabled() and full_text_for_tts and not is_error:
            self.tts_manager.queue_text_for_speech(full_text_for_tts) # 使用原始文本，因为清理和切分在内部完成
            # TTS播放是异步的，asr_continue_event将在TTS播放完成时（通过新逻辑）设置
        else:
            # 如果没有TTS播放，或者TTS文本为空，或者发生错误，立即允许ASR继续
            print("[AppController Finalize] 无TTS播放或TTS出错，立即允许ASR继续。")
            self.asr_manager.allow_next_listening_cycle()
        # --- 结束：替换 ---
        
        self.data_manager.save_data()
    

    def _synthesize_and_play_tts_threaded(self, text: str):
        if not text.strip(): return
        def tts_thread():
            try:
                audio_data = self._synthesize_speech(text)
                if audio_data:
                    self.tts_play_audio_signal.emit(audio_data) # 恢复信号发射, 移除此前的 print
            except Exception as e:
                print(f"AppController: Error in TTS thread: {e}")
        
        threading.Thread(target=tts_thread, daemon=True).start()

    def _execute_live2d_expression_from_signal(self, expression_name: str):
        if self.live2d_widget:
            self.live2d_widget.set_expression(expression_name)

    def _handle_set_expression_from_api(self, expression_name: str, duration_seconds: float = 0.0):
        if self.live2d_widget:
            self.live2d_widget.set_expression_with_duration(expression_name, duration_seconds)

    def hide_chat_window(self):
        """明确地隐藏聊天窗口并显示悬浮按钮。"""
        if not self.chat_window or not self.chat_window.isVisible():
            return  # 如果不存在或已隐藏，则不执行任何操作
        
        if hasattr(self.chat_window, 'animated_hide'):
            self.chat_window.animated_hide()
        else:
            self.chat_window.hide()
        
        # 隐藏事件会触发 _on_chat_window_hidden，由它来处理悬浮按钮和托盘图标
        
    def show_chat_window(self):
        """明确地显示聊天窗口并隐藏悬浮按钮。"""
        if not self.chat_window or self.chat_window.isVisible():
            return  # 如果不存在或已显示，则不执行任何操作

        if hasattr(self.chat_window, 'show_window'):
            self.chat_window.show_window()
        else:
            self.chat_window.show()
        
        if self.floating_button:
            self.floating_button.hide()
            
        self._populate_initial_chat_display()
        
        if hasattr(self, 'toggle_chat_action'):
            self.toggle_chat_action.setChecked(True)

    def _toggle_chat_window_visibility(self):
        """根据当前状态，切换聊天窗口的可见性。"""
        if not self.chat_window: return
        if self.chat_window.isVisible():
            self.hide_chat_window()
        else:
            self.show_chat_window()

    def toggle_asr_listening(self):
        """切换ASR监听状态，由UI按钮调用。现在委托给ASRManager。"""
        if hasattr(self, 'asr_manager'):
            self.asr_manager.toggle_listening()

    def _handle_recognized_text_from_asr(self, text: str):
        """处理从ASRManager发送过来的识别文本（在主线程执行）"""
        print(f"AppController (主线程): 收到ASR文本: '{text}'")
        # 1. 打断当前TTS (如果需要且正在播放)
        if self.tts_manager.is_playing():
            print("AppController (主线程): ASR识别到文本，停止当前TTS。")
            self.tts_manager.stop_playback(triggered_by_user_action=False) # TTS停止后会自动调用处理函数
        
        self._process_asr_text_after_tts_stop(text)

    def _process_asr_text_after_tts_stop(self, text: str):
        """在TTS停止（或无需停止）后实际处理ASR文本"""
        if "[ASR错误]" in text:
            self.display_error_message(text, True)
            # 错误发生后，也应该设置 continue_event，允许下一轮尝试
            if hasattr(self, 'asr_manager'):
                self.asr_manager.allow_next_listening_cycle()
            return

        # 2. 将文本填充到聊天窗口的输入框
        if self.chat_window and hasattr(self.chat_window, 'input_area') and self.chat_window.input_area:
            current_text = self.chat_window.input_area.input_field.toPlainText()
            # 简单的拼接逻辑，可以优化
            if current_text and not current_text.endswith(" "):
                new_text = current_text + " " + text
            else:
                new_text = current_text + text
            
            self.chat_window.input_area.input_field.setPlainText(new_text)
            cursor = self.chat_window.input_area.input_field.textCursor()
            cursor.movePosition(cursor.End) # 移动到末尾
            self.chat_window.input_area.input_field.setTextCursor(cursor)
            self.chat_window.input_area.input_field.ensureCursorVisible()
            if hasattr(self.chat_window.input_area, 'on_text_changed'):
                self.chat_window.input_area.on_text_changed() # 手动触发一次，确保发送按钮状态更新

            print("AppController (主线程): ASR文本已填充到输入框。")
            
            # 自动发送消息
            try:
                self.chat_window.input_area.send_message()
                print("AppController (主线程): ASR文本已自动发送。")
            except Exception as e:
                print(f"AppController (主线程): 自动发送ASR文本失败: {e}")
            
            # 重要：自动发送完成后，立即允许下一轮ASR识别
            if hasattr(self, 'asr_manager'): 
                self.asr_manager.allow_next_listening_cycle()
                print("AppController (主线程): ASR continue event 已设置，允许下一轮语音识别。")

        else:
            print("AppController (主线程): ChatWindow或输入区不可用，无法填充ASR文本。")
            # 如果UI不可用，也应该允许ASR继续
            if hasattr(self, 'asr_manager'):
                 self.asr_manager.allow_next_listening_cycle()

    def _handle_asr_status_change_for_ui(self, is_active: bool):
        """当ASR状态改变时，更新UI（如按钮状态）"""
        if self.chat_window:
            self.chat_window.update_asr_button_state(is_active)
        

    def display_error_message(self, message: str, is_critical: bool): # 辅助方法
        if self.chat_window:
             if hasattr(self.chat_window, 'display_error_message'):
                 self.chat_window.display_error_message(message, is_critical)
             else:
                 print(f"ChatWindow缺少显示错误消息的方法, 错误: {message}")

    def _populate_initial_chat_display(self):
        if self.chat_window:
            # 如果聊天窗口已经有内容，则不执行任何操作，以防止每次显示时都重新加载
            if self.chat_window.active_message_elements:
                return

            # 否则，从DataManager获取当前活动话题的历史记录并填充
            self.chat_window.clear_chat_display()
            history_to_load = self.data_manager.get_conversation_history()
            if history_to_load:
                self._populate_chat_window_with_history(history_to_load)

    def run(self):
        if self.live2d_widget: self.live2d_widget.show()
        if self.floating_button: self.floating_button.show()
        return self.app.exec_()

    def _quit_application(self):
        print("退出应用程序...")
        # 隐藏并关闭所有窗口，这将触发清理流程和最终的退出
        if self.chat_window:
            self.chat_window.force_close()
        if self.live2d_widget:
            self.live2d_widget.close()
        if self.floating_button:
            self.floating_button.close()
        
        # QApplication.quit() 会在最后一个窗口关闭后被自动调用(如果设置了quitOnLastWindowClosed)
        # 为保险起见，我们也可以在这里直接调用
        QApplication.instance().quit()

    def _cleanup(self):
        print("AppController: Cleaning up resources...")

        # --- 保存用户数据 ---
        self.data_manager.save_data()

        # +++ 清理增强功能 +++
        if hasattr(self, 'selection_assistant') and self.selection_assistant:
            try:
                self.selection_assistant.stop()
                print("✓ 划词助手已清理")
            except Exception as e:
                print(f"⚠️ 清理划词助手失败: {e}")

        if hasattr(self, 'input_enhancer') and self.input_enhancer:
            try:
                self.input_enhancer.cleanup_temp_files()
                print("✓ 输入增强器已清理")
            except Exception as e:
                print(f"⚠️ 清理输入增强器失败: {e}")
        # ---

        # --- 清理 TTS 管理器 ---
        if hasattr(self, 'tts_manager'):
            self.tts_manager.cleanup()

        # --- 清理 ASR 管理器 ---
        if hasattr(self, 'asr_manager'):
            self.asr_manager.cleanup()

        # --- 清理 Live2D (如果需要) ---
        if self.live2d_widget:
            self.live2d_widget.cleanup()

        # --- 清理临时文件 ---
        if self.temp_file_manager:
            self.temp_file_manager.cleanup_all()
            print("临时文件已清理。")
        
        if hasattr(self, 'tts_sentence_queue'):
            self.tts_sentence_queue.put(None) # 发送哨兵值
        if hasattr(self, 'tts_worker_thread') and self.tts_worker_thread.is_alive():
            # self.tts_worker_thread.join() # 等待线程结束 - [FIX] 移除join避免GUI线程死锁
            pass
        # --- 结束：添加 ---
        if hasattr(self, 'asr_manager'):
            self.asr_manager.allow_next_listening_cycle()

        print("清理完成。")

        # --- 开始：添加TTS线程优雅退出逻辑 ---
        if hasattr(self, 'tts_sentence_queue'):
            self.tts_sentence_queue.put(None) # 发送哨兵值
        if hasattr(self, 'tts_worker_thread') and self.tts_worker_thread.is_alive():
            # self.tts_worker_thread.join() # 等待线程结束 - [FIX] 移除join避免GUI线程死锁
            pass
        # --- 结束：添加 ---
        if hasattr(self, 'asr_manager'):
            self.asr_manager.allow_next_listening_cycle()

        print("清理完成。")

        # --- 清理 PyAudio (asr.py中定义的函数) ---
        # if ASR_AVAILABLE:
        #     try:
        #         asr.cleanup_pyaudio()
        #     except Exception as e:
        #         print(f"AppController: 调用asr.cleanup_pyaudio时出错: {e}")

        # 清理 Live2D 小部件 (如果存在)
        if hasattr(self, 'live2d_widget') and self.live2d_widget: # 移到 temp_file_manager 清理后，保持独立
            self.live2d_widget.cleanup()
            
        # HTTP 服务器清理 (如果需要，从之前的版本恢复)
        # if hasattr(self, 'http_server_thread') and self.http_server_thread.is_alive():
        #     if hasattr(self, 'http_server') and self.http_server:
        #         print("AppController: 正在关闭HTTP服务器...")
        #         try:
        #             self.http_server.shutdown()
        #             self.http_server.server_close()
        #         except Exception as e:
        #             print(f"AppController: 关闭HTTP服务器时出错: {e}")
        #     self.http_server_thread.join(timeout=2)
        #     if self.http_server_thread.is_alive():
        #         print("AppController: HTTP服务器线程未能正常关闭。")

        print("清理完成。")

        print("清理完成。")

    def _start_http_server(self):
        app_controller_instance_ref = self 

        class Live2DAPIHandler(BaseHTTPRequestHandler):
            _app_controller_instance_ref = app_controller_instance_ref 

            def _send_json_response(self, status_code, data):
                self.send_response(status_code)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*') 
                self.end_headers()
                self.wfile.write(json.dumps(data).encode('utf-8'))

            def do_OPTIONS(self): 
                self.send_response(200)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type')
                self.end_headers()
            
            def do_GET(self):
                parsed_path = urlparse(self.path)
                query_params = parse_qs(parsed_path.query)
                
                if parsed_path.path == '/set_expression':
                    name = query_params.get('name', [None])[0]
                    duration_str = query_params.get('duration', ["0"])[0]
                    try:
                        duration = float(duration_str)
                    except ValueError:
                        duration = 0.0
                    
                    if name and self._app_controller_instance_ref.live2d_widget:
                        self._app_controller_instance_ref.set_expression_from_api_signal.emit(name, duration)
                        self._send_json_response(200, {"status": "success", "message": f"Expression '{name}' triggered."})
                    else:
                        self._send_json_response(400, {"status": "error", "message": "Missing 'name' parameter or Live2D widget not available."})
                elif parsed_path.path == '/get_config':
                     self._send_json_response(200, {"status": "success", "config": self._app_controller_instance_ref.config})
                else:
                    self._send_json_response(404, {"status": "error", "message": "Endpoint not found."})

        def run_server():
            try:
                server_address = ('', LIVE2D_API_PORT)
                httpd = HTTPServer(server_address, Live2DAPIHandler)
                print(f"Live2D API 服务器正在 http://localhost:{LIVE2D_API_PORT} 上运行...")
                httpd.serve_forever()
            except OSError as e: 
                 print(f"错误: 无法启动 Live2D API 服务器 (端口 {LIVE2D_API_PORT}): {e}")
            except Exception as e:
                print(f"Live2D API 服务器启动时发生未知错误: {e}")

        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

# 步骤4.1 在AppController类中添加TTS服务管理方法

    # 步骤 3.4: 替换现有的 _synthesize_speech 方法


    # 步骤 3.7: 添加网络请求重试机制


    def switch_live2d_model(self, model_id: str) -> bool:
        """
        切换Live2D模型
        
        Args:
            model_id: 要切换到的模型ID
            
        Returns:
            切换是否成功
        """
        try:
            print(f"请求切换Live2D模型到: {model_id}")
            
            # 获取模型信息
            model_info = self.model_manager.get_model_info(model_id)
            if not model_info:
                print(f"未找到模型信息: {model_id}")
                if self.chat_window:
                    self.chat_window.display_error_message(f"未找到模型: {model_id}", False)
                return False
            
            # 转换为绝对路径进行检查
            model_path = model_info.path
            if not os.path.isabs(model_path):
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                absolute_model_path = os.path.join(project_root, model_path)
            else:
                absolute_model_path = model_path
            
            # 检查模型文件是否存在
            if not os.path.exists(absolute_model_path):
                print(f"模型文件不存在: {absolute_model_path}")
                if self.chat_window:
                    self.chat_window.display_error_message(f"模型文件不存在: {model_info.name}", False)
                return False
            
            # 显示切换提示
            if self.chat_window:
                self.chat_window.add_system_message(f"正在切换到模型: {model_info.name}...")
            
            # 执行模型切换
            if self.live2d_widget:
                success = self.live2d_widget.switch_model(model_info.path)
                
                if success:
                    # 更新模型管理器状态
                    self.model_manager.set_current_model(model_id)
                    
                    # 更新应用配置
                    self.config['current_live2d_model'] = model_id
                    self.data_manager.save_data()
                    
                    # 显示成功消息
                    if self.chat_window:
                        self.chat_window.add_system_message(
                            f"✓ 已成功切换到模型: {model_info.name}"
                        )
                    
                    print(f"✓ Live2D模型切换成功: {model_info.name}")
                    return True
                else:
                    # 切换失败，尝试错误恢复
                    self.handle_model_switch_failure(model_id)
                    return False
            else:
                print("Live2D组件未初始化")
                if self.chat_window:
                    self.chat_window.display_error_message("Live2D组件未初始化", True)
                return False
                
        except Exception as e:
            error_msg = f"切换Live2D模型时发生错误: {e}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            
            if self.chat_window:
                self.chat_window.display_error_message("模型切换过程中发生错误", True)
            
            return False

    def handle_model_switch_failure(self, failed_model_id: str):
        """处理模型切换失败"""
        try:
            print(f"模型切换失败，尝试错误恢复: {failed_model_id}")
            
            # 尝试回退到上一个可用的模型
            recent_used = self.model_manager.recent_used
            
            for backup_model_id in recent_used:
                if backup_model_id != failed_model_id:
                    backup_info = self.model_manager.get_model_info(backup_model_id)
                    if backup_info:
                        # 检查备用模型文件是否存在
                        backup_path = backup_info.path
                        if not os.path.isabs(backup_path):
                            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                            backup_path = os.path.join(project_root, backup_path)
                        
                        if os.path.exists(backup_path):
                            print(f"尝试回退到模型: {backup_info.name}")
                            
                            if self.live2d_widget.switch_model(backup_info.path):
                                self.model_manager.set_current_model(backup_model_id)
                                if self.chat_window:
                                    self.chat_window.add_system_message(
                                        f"已回退到模型: {backup_info.name}"
                                    )
                                return True
            
            # 如果所有备选都失败，尝试加载默认模型
            available_models = self.model_manager.get_all_models()
            if 'linghu' in available_models:
                default_info = available_models['linghu']
                default_path = default_info.path
                if not os.path.isabs(default_path):
                    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                    default_path = os.path.join(project_root, default_path)
                
                if os.path.exists(default_path) and self.live2d_widget.switch_model(default_info.path):
                    self.model_manager.set_current_model('linghu')
                    if self.chat_window:
                        self.chat_window.add_system_message(
                            "已回退到默认模型: 灵狐芊芊"
                        )
                    return True
            
            # 完全失败
            if self.chat_window:
                self.chat_window.display_error_message(
                    "所有模型加载失败，请检查模型文件", True
                )
            return False
            
        except Exception as e:
            print(f"错误恢复失败: {e}")
            return False

    def change_model(self, new_model_name: str):
        """
        更改当前使用的AI模型名称并保存。
        """
        if new_model_name in self.available_models:
            print(f"正在切换AI模型到: {new_model_name}")
            self.data_manager.api_model_name = new_model_name
            self.config['api_model_name'] = new_model_name
            self.data_manager.save_data()
            
            if self.chat_window:
                # 可以在聊天窗口显示一个提示
                self.chat_window.add_system_message(f"AI 模型已切换为: {new_model_name}")
        else:
            print(f"错误: 尝试切换到无效模型 '{new_model_name}'")


    def _open_tts_settings_dialog(self):
        """打开TTS设置对话框"""
        parent_window = self.chat_window if self.chat_window else None
        dialog = TTSSettingsDialog(self, parent=parent_window)
        dialog.exec_()

    # +++ 新增：增强功能控制方法 +++
    def enable_selection_assistant(self):
        """启用划词助手"""
        if hasattr(self, 'selection_assistant') and self.selection_assistant:
            try:
                self.selection_assistant.start()
                print("✓ 划词助手已启用")
                if self.chat_window:
                    self.chat_window.add_system_message("✓ 划词助手已启用 - 选择任意文本试试")
                return True
            except Exception as e:
                print(f"⚠️ 启用划词助手失败: {e}")
                if self.chat_window:
                    self.chat_window.display_error_message(f"启用划词助手失败: {str(e)}", False)
        return False

    def disable_selection_assistant(self):
        """禁用划词助手"""
        if hasattr(self, 'selection_assistant') and self.selection_assistant:
            try:
                self.selection_assistant.stop()
                print("✓ 划词助手已禁用")
                if self.chat_window:
                    self.chat_window.add_system_message("✓ 划词助手已禁用")
                return True
            except Exception as e:
                print(f"⚠️ 禁用划词助手失败: {e}")
        return False

    def toggle_selection_assistant(self):
        """切换划词助手状态"""
        if self.is_selection_assistant_enabled():
            return self.disable_selection_assistant()
        else:
            return self.enable_selection_assistant()

    def is_selection_assistant_enabled(self) -> bool:
        """检查划词助手是否已启用"""
        if hasattr(self, 'selection_assistant') and self.selection_assistant:
            return self.selection_assistant.is_enabled()
        return False

    def send_message(self, message: str):
        """供划词助手调用的消息发送方法"""
        if self.chat_window and hasattr(self.chat_window, 'input_area'):
            try:
                # 将消息设置到输入框
                if hasattr(self.chat_window.input_area, 'input_field'):
                    self.chat_window.input_area.input_field.setPlainText(message)

                # 显示聊天窗口
                self.show_chat_window()

                print(f"✓ 消息已设置到输入框: {message[:50]}...")
            except Exception as e:
                print(f"⚠️ 发送消息失败: {e}")
    # ---

if __name__ == "__main__":
    from PyQt5.QtCore import Qt 
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)
    
    controller = AppController()
    exit_code = controller.run()
    sys.exit(exit_code)