import json
from typing import Dict, Optional, Any
from datetime import datetime, timezone
import os # 导入os模块用于文件操作
import uuid #确保导入了uuid
import shutil # 新增导入

# 假设 assistant_config.py 与 assistant_manager.py 在同一目录下
# 如果不在，需要调整导入路径
try:
    from .assistant_config import AssistantConfig
except ImportError: # 处理直接运行脚本时的情况
    from assistant_config import AssistantConfig

AVATAR_SUBDIR = "avatars" # 头像子目录名称

class AssistantManager:
    def __init__(self, config_file="assistants.json", config_dir="aipet"):
        # 确保配置目录存在
        self.config_dir = config_dir
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            print(f"创建目录: {self.config_dir}")

        self.avatar_storage_dir = os.path.join(self.config_dir, AVATAR_SUBDIR) # 定义头像存储完整路径
        self._ensure_avatar_directory_exists() # 创建时确保头像目录存在

        self.config_file = os.path.join(self.config_dir, config_file)
        self.assistants: Dict[str, AssistantConfig] = {}
        self.current_assistant_id: Optional[str] = None
        self.settings: Dict = {} # 用于存储 "settings" 部分
        self.load_config()

    def _ensure_avatar_directory_exists(self):
        """确保头像存储目录存在。"""
        if not os.path.exists(self.avatar_storage_dir):
            try:
                os.makedirs(self.avatar_storage_dir)
                print(f"创建头像目录: {self.avatar_storage_dir}")
            except OSError as e:
                print(f"错误: 无法创建头像目录 {self.avatar_storage_dir}: {e}")

    def _generate_avatar_filename(self, assistant_id: str, original_path: str) -> str:
        """根据助手ID和原始文件扩展名生成头像文件名。"""
        _, ext = os.path.splitext(original_path)
        if not ext.lower() in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']: # 基本的扩展名检查
            ext = '.png' # 默认使用 .png
        return f"{assistant_id}{ext}"

    def _copy_avatar_to_storage(self, original_avatar_path: str, assistant_id: str) -> Optional[str]:
        """将用户选择的头像复制到应用存储，并返回存储的文件名。"""
        if not original_avatar_path or not os.path.isfile(original_avatar_path):
            print(f"错误: 原始头像路径无效或文件不存在: {original_avatar_path}")
            return None
        
        self._ensure_avatar_directory_exists() # 再次确保目录存在
        
        target_filename = self._generate_avatar_filename(assistant_id, original_avatar_path)
        full_dest_path = os.path.join(self.avatar_storage_dir, target_filename)
        
        try:
            shutil.copy2(original_avatar_path, full_dest_path)
            print(f"头像已复制到: {full_dest_path}")
            return target_filename # 返回相对文件名
        except Exception as e:
            print(f"错误: 复制头像失败 从 {original_avatar_path} 到 {full_dest_path}: {e}")
            return None

    def _delete_avatar_from_storage(self, avatar_filename: Optional[str]):
        """从存储中删除指定的头像文件。"""
        if not avatar_filename:
            return
        
        full_avatar_path = os.path.join(self.avatar_storage_dir, avatar_filename)
        if os.path.exists(full_avatar_path):
            try:
                os.remove(full_avatar_path)
                print(f"头像文件已删除: {full_avatar_path}")
            except Exception as e:
                print(f"错误: 删除头像文件失败 {full_avatar_path}: {e}")
        else:
            print(f"警告: 尝试删除不存在的头像文件 {full_avatar_path}")

    def _generate_id(self, name: str) -> str:
        """生成助手的唯一ID (可以根据需要实现更复杂的逻辑)"""
        # 简单实现：使用名称的小写并替换空格，实际应用中可能需要更健壮的唯一ID生成方式
        base_id = name.lower().replace(" ", "_").replace("/", "_").replace("\\", "_")
        _id = base_id
        count = 1
        while _id in self.assistants:
            _id = f"{base_id}_{count}"
            count += 1
        return _id

    def load_config(self):
        """从JSON文件加载助手配置"""
        try:
            if os.path.exists(self.config_file) and os.path.getsize(self.config_file) > 0:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                loaded_assistants = data.get("assistants", {})
                for id_key, config_data in loaded_assistants.items(): # id_key to avoid conflict with module id
                    config_data['id'] = id_key 
                    self.assistants[id_key] = AssistantConfig.from_dict(config_data)
                
                self.current_assistant_id = data.get("current_assistant")
                self.settings = data.get("settings", {"auto_save": True, "show_switch_notification": True}) # 提供默认设置

                # 校验 current_assistant_id 是否有效
                if self.current_assistant_id and self.current_assistant_id not in self.assistants:
                    print(f"警告: 配置文件中的 current_assistant_id '{self.current_assistant_id}' 无效。重置为None。")
                    self.current_assistant_id = None
                
                if not self.current_assistant_id and self.assistants:
                    # 如果没有当前助手，但有助手列表，默认选择第一个或最近使用的
                    # 这里简单选择第一个
                    self.current_assistant_id = next(iter(self.assistants))

                print(f"从 {self.config_file} 加载了 {len(self.assistants)} 个助手配置。")
            else:
                print(f"配置文件 {self.config_file} 不存在或为空。将创建新的配置。")
                # 如果文件不存在，创建一个空的结构并保存它
                self._create_default_config_if_needed()

        except json.JSONDecodeError:
            print(f"错误: 配置文件 {self.config_file} 格式错误。将使用默认配置。")
            self.assistants = {}
            self.current_assistant_id = None
            self.settings = {"auto_save": True, "show_switch_notification": True}
            self._create_default_config_if_needed(overwrite=True) # 覆盖损坏的配置
        except Exception as e:
            print(f"加载配置时发生未知错误: {e}")
            self.assistants = {}
            self.current_assistant_id = None
            self.settings = {"auto_save": True, "show_switch_notification": True}

    def _create_default_config_if_needed(self, overwrite=False):
        """如果配置文件不存在或需要覆盖，则创建并保存一个空的默认配置"""
        if overwrite or not os.path.exists(self.config_file) or os.path.getsize(self.config_file) == 0 :
            print(f"创建默认配置文件于 {self.config_file}")
            default_config_content = {
                "assistants": {},
                "current_assistant": None,
                "settings": {
                    "auto_save": True,
                    "show_switch_notification": True
                }
            }
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config_content, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"创建默认配置文件失败: {e}")


    def save_config(self):
        """将当前助手配置保存到JSON文件"""
        if not self.settings.get("auto_save", True) and not self.config_file: # 增加对config_file是否为空的检查
             print("自动保存已禁用，且未指定配置文件路径。跳过保存。")
             return

        data_to_save = {
            "assistants": {id_key: assistant.to_dict() for id_key, assistant in self.assistants.items()},
            "current_assistant": self.current_assistant_id,
            "settings": self.settings
        }
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            print(f"配置已保存到 {self.config_file}")
        except Exception as e:
            print(f"保存配置到 {self.config_file} 时出错: {e}")

    def get_assistant(self, assistant_id: str) -> Optional[AssistantConfig]:
        """获取指定ID的助手配置"""
        return self.assistants.get(assistant_id)

    def get_current_assistant(self) -> Optional[AssistantConfig]:
        """获取当前选中的助手配置"""
        if self.current_assistant_id:
            return self.get_assistant(self.current_assistant_id)
        return None

    def get_all_assistants(self) -> list[AssistantConfig]:
        """获取所有助手配置的列表"""
        return list(self.assistants.values())

    def switch_assistant(self, assistant_id: str, live2d_widget=None, chat_system=None) -> bool:
        """切换助手（同时切换模型和提示词）"""
        config = self.get_assistant(assistant_id)
        if not config:
            print(f"切换失败：未找到ID为 '{assistant_id}' 的助手。")
            return False

        print(f"尝试切换到助手: {config.name} (ID: {assistant_id})")

        model_success = True
        if live2d_widget and hasattr(live2d_widget, 'switch_model'):
            print(f"  尝试切换Live2D模型: {config.model_path}")
            model_success = live2d_widget.switch_model(config.model_path) # 假设 switch_model 返回 bool
            if not model_success:
                print(f"  Live2D模型切换失败。")
        elif live2d_widget:
            print(f"  警告: live2d_widget 对象没有 switch_model 方法。")
        else:
            print(f"  跳过Live2D模型切换 (live2d_widget未提供)。")


        if model_success:
            if chat_system and hasattr(chat_system, 'update_system_prompt'):
                print(f"  尝试更新系统提示词: {config.system_prompt[:50]}...") # 打印部分提示词
                chat_system.update_system_prompt(config.system_prompt)
            elif chat_system:
                print(f"  警告: chat_system 对象没有 update_system_prompt 方法。")
            else:
                print(f"  跳过系统提示词更新 (chat_system未提供)。")

            self.current_assistant_id = assistant_id
            config.last_used = datetime.now(timezone.utc).isoformat()
            if self.settings.get("auto_save", True):
                self.save_config()
            
            print(f"成功切换到助手: {config.name}")
            return True
        
        print(f"切换到助手 {config.name} 失败。")
        return False

    def create_assistant(self, name: str, model_path: str, system_prompt: str, avatar_original_path: Optional[str] = None, assistant_id: Optional[str] = None) -> Optional[AssistantConfig]:
        """创建新助手。avatar_original_path 是用户选择的原始头像文件路径。"""
        if not assistant_id:
            assistant_id = self._generate_id(name)
        
        if assistant_id in self.assistants:
            print(f"创建失败：ID为 '{assistant_id}' 的助手已存在。")
            return None

        stored_avatar_filename = None
        if avatar_original_path:
            stored_avatar_filename = self._copy_avatar_to_storage(avatar_original_path, assistant_id)
            if not stored_avatar_filename:
                print(f"警告: 头像复制失败，助手 '{name}' 将不带头像创建。")

        config = AssistantConfig(
            id=assistant_id,
            name=name,
            model_path=model_path,
            system_prompt=system_prompt,
            avatar_path=stored_avatar_filename, # 存储处理后的文件名或None
            created_time=datetime.now(timezone.utc).isoformat(),
            last_used=datetime.now(timezone.utc).isoformat()
        )

        self.assistants[assistant_id] = config
        if not self.current_assistant_id: # 如果之前没有当前助手，则将新创建的设为当前
            self.current_assistant_id = assistant_id

        if self.settings.get("auto_save", True):
            self.save_config()
        print(f"成功创建助手: {name} (ID: {assistant_id})。头像路径: {stored_avatar_filename}")
        return config

    def update_assistant(self, assistant_id_to_update: str, new_data: Dict[str, Any]) -> bool:
        """
        更新现有助手配置。
        new_data 字典可以包含 name, model_path, system_prompt。
        对于头像，new_data 可以包含 'avatar_original_path' (新选择的原始路径) 
        或 'clear_avatar' (一个布尔值，为True时表示清除头像)。
        如果这两个键都不存在，则不修改头像。
        """
        if assistant_id_to_update not in self.assistants:
            print(f"错误：尝试更新不存在的助手ID: {assistant_id_to_update}")
            return False
        
        assistant_to_update = self.assistants[assistant_id_to_update]

        # 更新文本字段
        assistant_to_update.name = new_data.get('name', assistant_to_update.name)
        assistant_to_update.model_path = new_data.get('model_path', assistant_to_update.model_path)
        assistant_to_update.system_prompt = new_data.get('system_prompt', assistant_to_update.system_prompt)

        # 处理头像更新
        new_avatar_original_path = new_data.get('avatar_original_path')
        clear_avatar_flag = new_data.get('clear_avatar', False)

        if clear_avatar_flag:
            if assistant_to_update.avatar_path:
                self._delete_avatar_from_storage(assistant_to_update.avatar_path)
            assistant_to_update.avatar_path = None
            print(f"助手 {assistant_to_update.name} 的头像已清除。")
        elif new_avatar_original_path: # 用户选择了新的头像文件
            # 删除旧头像（如果存在）
            if assistant_to_update.avatar_path:
                self._delete_avatar_from_storage(assistant_to_update.avatar_path)
            
            # 复制新头像并获取存储的文件名
            stored_filename = self._copy_avatar_to_storage(new_avatar_original_path, assistant_id_to_update)
            if stored_filename:
                assistant_to_update.avatar_path = stored_filename
                print(f"助手 {assistant_to_update.name} 的头像已更新为: {stored_filename}")
            else:
                # 复制失败，保持旧头像（如果之前有）或设为None
                assistant_to_update.avatar_path = None # 或者保留旧值，取决于期望行为
                print(f"警告: 为助手 {assistant_to_update.name} 更新头像失败，头像将设为None。")
        # 如果两个 avatar 相关的键都没有，则头像保持不变

        assistant_to_update.last_used = datetime.now(timezone.utc).isoformat()
        self.assistants[assistant_id_to_update] = assistant_to_update # 重新赋值以确保更新

        print(f"助手配置已更新: ID {assistant_to_update.id}, 名称 {assistant_to_update.name}")
        if self.settings.get("auto_save", True):
            self.save_config()
        
        return True

    def delete_assistant(self, assistant_id: str) -> bool:
        """删除助手"""
        if assistant_id not in self.assistants:
            print(f"删除失败：未找到ID为 '{assistant_id}' 的助手。")
            return False

        assistant_to_delete = self.assistants[assistant_id]
        deleted_assistant_name = assistant_to_delete.name
        
        # 删除头像文件（如果存在）
        if assistant_to_delete.avatar_path:
            self._delete_avatar_from_storage(assistant_to_delete.avatar_path)

        del self.assistants[assistant_id]
        print(f"助手 '{deleted_assistant_name}' (ID: {assistant_id}) 已删除。")

        if self.current_assistant_id == assistant_id:
            self.current_assistant_id = None
            # 如果删除了当前助手，尝试选择另一个助手作为当前助手
            if self.assistants:
                self.current_assistant_id = next(iter(self.assistants.keys()))
                current_name = self.assistants[self.current_assistant_id].name if self.current_assistant_id and self.current_assistant_id in self.assistants else 'None'
                print(f"当前助手已切换为: {current_name}")
            else:
                print("所有助手均已删除。")
        
        if self.settings.get("auto_save", True):
            self.save_config()
        return True

    def get_settings(self) -> Dict:
        """获取设置"""
        return self.settings

    def update_settings(self, new_settings: Dict):
        """更新设置"""
        self.settings.update(new_settings)
        if self.settings.get("auto_save", True): # 如果更新了设置，且auto_save为true，则保存
            self.save_config()
        print(f"设置已更新: {self.settings}")

    def get_full_avatar_path(self, avatar_filename: Optional[str]) -> Optional[str]:
        """根据存储的头像文件名获取其完整的绝对路径。"""
        if not avatar_filename:
            return None
        full_path = os.path.join(self.avatar_storage_dir, avatar_filename)
        if os.path.exists(full_path):
            return full_path
        print(f"警告: 头像文件在完整路径下未找到: {full_path}")
        return None

# 示例用法 (可以放在 if __name__ == '__main__': 中测试)
def example_usage():
    # 确保 assistant_config.py 在同一目录或PYTHONPATH中
    # 创建一个临时的 aipet 目录用于测试
    test_dir = "aipet_test_temp"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)

    # 模拟 Live2D 组件和聊天系统
    class MockLive2DWidget:
        def switch_model(self, model_path: str) -> bool:
            print(f"[MockLive2D] 切换模型到: {model_path}")
            if "invalid" in model_path:
                return False
            return True

    class MockChatSystem:
        def __init__(self):
            self.system_prompt = ""

        def update_system_prompt(self, prompt: str):
            self.system_prompt = prompt
            print(f"[MockChatSystem] 系统提示词更新为: {prompt[:30]}...")
    
    print("\n--- AssistantManager Example Usage ---")
    manager = AssistantManager(config_file="test_assistants.json", config_dir=test_dir)
    live2d_widget = MockLive2DWidget()
    chat_system = MockChatSystem()

    # 清理旧的测试文件
    test_config_path = os.path.join(test_dir, "test_assistants.json")
    if os.path.exists(test_config_path):
        os.remove(test_config_path)
        print(f"已删除旧的测试配置文件: {test_config_path}")
    manager = AssistantManager(config_file="test_assistants.json", config_dir=test_dir) # 重新加载

    # 1. 创建助手
    print("\n--- 1. 创建助手 ---")
    ganyu_id_config = manager.create_assistant(
        name="原神甘雨",
        model_path="ganyu/ganyu.model3.json",
        system_prompt="你是甘雨，璃月七星的秘书..."
    )
    if ganyu_id_config:
        ganyu_id = ganyu_id_config.id
        print(f"甘雨创建成功, ID: {ganyu_id}")

    qianqian_id_config = manager.create_assistant(
        name="灵狐芊芊",
        model_path="linghu/芊芊.model3.json",
        system_prompt="你是芊芊，一个活泼可爱的狐仙..."
    )
    if qianqian_id_config:
        qianqian_id = qianqian_id_config.id
        print(f"芊芊创建成功, ID: {qianqian_id}")
    
    # 2. 查看所有助手
    print("\n--- 2. 查看所有助手 ---")
    all_assistants = manager.get_all_assistants()
    for assistant in all_assistants:
        print(f"  - {assistant.name} (ID: {assistant.id}, Model: {assistant.model_path})")

    # 3. 切换助手
    print("\n--- 3. 切换助手 ---")
    if qianqian_id_config:
        manager.switch_assistant(qianqian_id_config.id, live2d_widget, chat_system)
    current_assistant = manager.get_current_assistant()
    if current_assistant:
        print(f"当前助手: {current_assistant.name}")
    else:
        print("当前无助手")

    # 4. 更新助手
    print("\n--- 4. 更新助手 ---")
    if ganyu_id_config:
        manager.update_assistant(ganyu_id_config.id, {"name": "原神甘雨", "model_path": "ganyu/ganyu.model3.json", "system_prompt": "你是甘雨，璃月七星的秘书..."})
        updated_ganyu = manager.get_assistant(ganyu_id_config.id)
        if updated_ganyu:
            print(f"更新后的甘雨: {updated_ganyu.name}, Prompt: {updated_ganyu.system_prompt[:30]}...")

    # 5. 删除助手
    print("\n--- 5. 删除助手 ---")
    if qianqian_id_config:
        manager.delete_assistant(qianqian_id_config.id)
    all_assistants_after_delete = manager.get_all_assistants()
    print("删除芊芊后的助手列表:")
    for assistant in all_assistants_after_delete:
        print(f"  - {assistant.name}")
    
    current_after_delete = manager.get_current_assistant()
    if current_after_delete:
        print(f"删除芊芊后的当前助手: {current_after_delete.name}")
    else:
        print("删除芊芊后当前无助手 (或所有助手均已删除)")


    # 6. 测试加载配置 (实例化一个新的Manager)
    print("\n--- 6. 测试从文件加载配置 ---")
    manager2 = AssistantManager(config_file="test_assistants.json", config_dir=test_dir)
    current_assistant_loaded = manager2.get_current_assistant()
    if current_assistant_loaded:
        print(f"从文件加载后, 当前助手是: {current_assistant_loaded.name}")
        all_assistants_loaded = manager2.get_all_assistants()
        print("所有已加载助手:")
        for assistant in all_assistants_loaded:
            print(f"  - {assistant.name} (ID: {assistant.id})")
    else:
        print("从文件加载后, 当前无助手。")
        if not manager2.get_all_assistants():
            print("且无任何助手配置被加载。")


    # 7. 测试设置
    print("\n--- 7. 测试设置 ---")
    print(f"当前设置: {manager.get_settings()}")
    manager.update_settings({"auto_save": False, "new_setting": 123})
    print(f"更新后设置: {manager.get_settings()}")
    manager.update_settings({"auto_save": True}) # 恢复自动保存以供后续测试

    # 8. 测试切换到不存在的助手
    print("\n--- 8. 测试切换到不存在的助手 ---")
    manager.switch_assistant("non_existent_id", live2d_widget, chat_system)

    # 9. 测试创建同名助手 (ID生成逻辑)
    print("\n--- 9. 测试创建同名助手 ---")
    manager.create_assistant(name="原神甘雨", model_path="ganyu/ganyu2.model3.json", system_prompt="...")
    manager.create_assistant(name="原神甘雨", model_path="ganyu/ganyu3.model3.json", system_prompt="...")
    print("查看所有助手，检查ID生成:")
    for assistant in manager.get_all_assistants():
        print(f"  - {assistant.name} (ID: {assistant.id})")


    # 清理测试目录和文件
    # try:
    #     if os.path.exists(test_config_path):
    #         os.remove(test_config_path)
    #     if os.path.exists(test_dir) and not os.listdir(test_dir): # 检查目录是否为空
    #         os.rmdir(test_dir)
    #     elif os.path.exists(test_dir): # 如果目录不为空，打印提示
    #         print(f"Warning: Directory {test_dir} is not empty and was not removed.")
    # except OSError as e:
    #     print(f"Error cleaning up test files/directory: {e}")


if __name__ == '__main__':
    # 为了能直接运行此文件进行测试，需要确保 AssistantConfig 能被找到
    # 如果 assistant_config.py 在同级目录，上面的 try-except ImportError 应该能处理
    example_usage() 