# --- START OF FILE api_client.py ---

import requests
import json
from typing import Optional, Dict, Any, List, Generator, Union

class APIClient:
    """
    负责与后端 AI 服务 (OpenAI 兼容 LLM 接口) 进行通信。
    """
    def __init__(self, base_url: str, api_key: Optional[Any] = None, timeout: int = 60):
        """
        初始化 APIClient。

        Args:
            base_url (str): AI 服务的基础 URL (例如 'http://localhost:8000/v1')。
            api_key (Optional[Any]): API 密钥。如果提供，将被用作 Bearer Token。
                                     可以是字符串、数字 (会被转为字符串) 或 None。
            timeout (int): 请求超时时间 (秒)。
        """
        if not base_url.endswith('/'):
            base_url += '/'
        self.base_url = base_url
        self.api_key = str(api_key) if api_key is not None and str(api_key).strip() else None
        self.timeout = timeout
        self.headers = {
            "Content-Type": "application/json"
        }
        if self.api_key:
            self.headers["Authorization"] = f"Bearer {self.api_key}"
            print(f"APIClient: 使用 API Key (前5位): Bearer {self.api_key[:5]}...")
        else:
            print("APIClient: 未配置或未使用 API Key。")

    def send_chat_message(self, 
                          messages: List[Dict[str, Any]], 
                          model: str = "default-model", 
                          stream: bool = False,
                          tools: Optional[List[Dict[str, Any]]] = None,
                          tool_choice: Optional[Union[str, Dict[str, Any]]] = "auto",
                          # system_instruction_payload: Optional[Dict[str, Any]] = None, # REMOVED
                          **other_llm_params) -> Union[Optional[Dict[str, Any]], Generator[tuple[str, Dict[str, Any]], None, None]]: # Adjusted Generator type hint
       """
       发送聊天消息到 OpenAI 兼容的 LLM 服务。

       Args:
           messages (list): OpenAI格式的消息列表 (包含 role: system/user/assistant, content: string/array)。
           model (str): 要使用的模型名称。
           stream (bool): 是否使用流式响应。
           tools (Optional[List[Dict[str, Any]]]): 可选的工具定义列表。
           tool_choice (Optional[Union[str, Dict[str, Any]]]): 控制模型如何选择工具。
           **other_llm_params: 其他传递给LLM API的参数 (如 temperature, max_tokens)。

       Returns:
           如果 stream=False，返回解析后的JSON响应。
           如果 stream=True，返回一个生成器，逐块产生响应事件 (event_type, event_data)。
           错误时行为依赖具体实现（可能返回None或在流中产生错误）。
       """
       endpoint = "chat/completions"
       url = f"{self.base_url.rstrip('/')}/{endpoint}"
       
       payload = {
           "model": model,
           "messages": messages, # messages 应该已经是 OpenAI 格式
           "stream": stream,
           **other_llm_params
       }

       # if system_instruction_payload: # REMOVED
       #     payload["system_instruction"] = system_instruction_payload

       if tools:
           payload["tools"] = tools
           if tool_choice:
               payload["tool_choice"] = tool_choice

       # 为了日志简洁，可以有选择地打印 payload
       payload_log = {k: (f"<{len(v)} messages>" if k == "messages" and isinstance(v, list) else v) 
                      for k, v in payload.items()}
       print(f"APIClient: 发送请求到 {url}，有效载荷 (stream={stream}): {json.dumps(payload_log, indent=2, ensure_ascii=False)}")
       
       # --- BEGIN DEBUG LOG FOR FULL PAYLOAD ---
       print(f"APIClient: DEBUG - 最终发送的完整 payload:\n{json.dumps(payload, indent=2, ensure_ascii=False)}")
       # --- END DEBUG LOG FOR FULL PAYLOAD ---

       try:
           response = requests.post(url, headers=self.headers, json=payload, timeout=self.timeout, stream=stream)
           response.raise_for_status() # 如果HTTP状态码是4xx或5xx，则抛出异常

           if stream:
               current_tool_calls_list: List[Dict[str, Any]] = []

               for line in response.iter_lines():
                   if not line: continue
                   decoded_line = line.decode('utf-8')
                   if decoded_line.startswith("data: "):
                       json_str = decoded_line[len("data: "):].strip()
                       if json_str == "[DONE]":
                           if current_tool_calls_list:
                               print(f"APIClient: 在流结束 [DONE] 前，检测到已累积的工具调用，发出 tool_call_request。")
                               yield "tool_call_request", {"tool_calls": list(current_tool_calls_list)}
                               current_tool_calls_list.clear()
                           print("APIClient: 流结束信号 [DONE] 收到")
                           yield "stream_end_signal", {}
                           return

                       if not json_str: continue
                       try:
                           chunk = json.loads(json_str)
                           choice = chunk.get("choices", [{}])[0]
                           delta = choice.get("delta", {})
                           finish_reason = choice.get("finish_reason")

                           if delta.get("content") is not None:
                               yield "ai_delta", {"content": delta["content"]}
                           
                           if delta.get("tool_calls"):
                               for tc_delta_item in delta["tool_calls"]:
                                   index = tc_delta_item.get("index")
                                   if index is None and len(current_tool_calls_list) == 0 : index = 0
                                   
                                   while len(current_tool_calls_list) <= index:
                                       current_tool_calls_list.append({}) 

                                   current_tc = current_tool_calls_list[index]
                                   if not current_tc: 
                                       current_tc = {"id": None, "type": "function", "function": {"name": "", "arguments": ""}}
                                       current_tool_calls_list[index] = current_tc

                                   if tc_delta_item.get("id"):
                                       current_tc["id"] = tc_delta_item["id"]
                                   if tc_delta_item.get("type"): 
                                       current_tc["type"] = tc_delta_item["type"]
                                   
                                   if isinstance(tc_delta_item.get("function"), dict):
                                       func_delta = tc_delta_item["function"]
                                       if "name" in func_delta:
                                           current_tc["function"]["name"] = (current_tc["function"].get("name","") or "") + func_delta["name"]
                                       if "arguments" in func_delta:
                                           current_tc["function"]["arguments"] = (current_tc["function"].get("arguments","") or "") + func_delta["arguments"]
                           
                           if finish_reason == "tool_calls":
                               if current_tool_calls_list:
                                   complete_tool_calls = [
                                       tc for tc in current_tool_calls_list 
                                       if tc.get("id") and tc.get("function",{}).get("name") is not None
                                   ]
                                   if complete_tool_calls:
                                       print(f"APIClient: finish_reason='tool_calls', 发出 tool_call_request 含 {len(complete_tool_calls)} 个工具。")
                                       yield "tool_call_request", {"tool_calls": list(complete_tool_calls)}
                                   else:
                                       print("APIClient: WARN - finish_reason='tool_calls' 但未能组装有效的工具调用。")
                               current_tool_calls_list.clear() 

                           elif finish_reason and finish_reason != "tool_calls": 
                               if current_tool_calls_list:
                                   print(f"APIClient: WARN - finish_reason='{finish_reason}' 但仍有未发出的累积工具调用，尝试发出。")
                                   complete_tool_calls = [tc for tc in current_tool_calls_list if tc.get("id") and tc.get("function",{}).get("name") is not None]
                                   if complete_tool_calls:
                                       yield "tool_call_request", {"tool_calls": list(complete_tool_calls)}
                                   current_tool_calls_list.clear()
                       
                       except json.JSONDecodeError as e_json:
                           print(f"APIClient: 解析流式JSON块失败: {e_json} - 原始块: '{json_str}'")
                           yield "error", {"message": f"JSON解析错误: {e_json}", "raw_chunk": json_str}
                       except Exception as e_proc:
                           print(f"APIClient: 处理流式块时发生错误: {e_proc} - 原始块: '{json_str}'")
                           yield "error", {"message": f"块处理错误: {e_proc}", "raw_chunk": json_str}
               
               if current_tool_calls_list:
                   print(f"APIClient: WARN - 流结束后仍有未处理的累积工具调用，尝试发出。")
                   complete_tool_calls = [tc for tc in current_tool_calls_list if tc.get("id") and tc.get("function",{}).get("name") is not None]
                   if complete_tool_calls:
                       yield "tool_call_request", {"tool_calls": list(complete_tool_calls)}
                   current_tool_calls_list.clear()

           else: # 非流式
               response_data = response.json()
               message_data = response_data.get("choices", [{}])[0].get("message", {})
               if message_data.get("tool_calls"):
                   tool_calls_non_stream = message_data["tool_calls"]
                   print(f"APIClient: 非流式响应中检测到工具调用: {len(tool_calls_non_stream)}个")
                   # 为了与流式保持一致的事件结构，即使是非流式也用 yield
                   yield "tool_call_request", {"tool_calls": tool_calls_non_stream}
                   yield "stream_end_signal", {} 
                   return 
               
               return response_data 
               
       except requests.exceptions.HTTPError as e_http:
           error_content = e_http.response.text
           print(f"APIClient: HTTP请求错误 (stream={stream}): {e_http} - 响应体: {error_content}")
           if stream: yield "error", {"message": f"HTTP Error {e_http.response.status_code}: {error_content}"}; yield "stream_end_signal", {}; return
           else: return {"error": {"message": f"HTTP Error {e_http.response.status_code}: {error_content}", "status_code": e_http.response.status_code}}
       except requests.exceptions.RequestException as e_req:
           print(f"APIClient: 请求错误 (stream={stream}): {e_req}")
           if stream: yield "error", {"message": str(e_req)}; yield "stream_end_signal", {}; return
           else: return None # Or raise
       except json.JSONDecodeError as e_json_full: # Should only happen for non-stream if response is not JSON
           print(f"APIClient: JSON解码错误 (非流式): {e_json_full} - 响应内容: {response.text if 'response' in locals() else 'N/A'}")
           # Non-stream errors are typically returned as dict, not yielded.
           # However, if stream=True and this somehow happens (e.g. after stream=True block), then yield.
           if stream: yield "error", {"message": f"JSON解码错误: {e_json_full}"}; yield "stream_end_signal", {}; return
           return {"error": {"message": f"JSON解码错误: {e_json_full}", "raw_response": response.text if 'response' in locals() else 'N/A'}}
       except Exception as e_unknown:
           print(f"APIClient: 发生未知错误 (stream={stream}): {e_unknown}")
           import traceback; traceback.print_exc()
           if stream: yield "error", {"message": str(e_unknown)}; yield "stream_end_signal", {}; return
           else: return {"error": {"message": f"未知错误: {str(e_unknown)}"}}
       
       if stream: # Ensure the generator is properly closed if it hasn't returned/yielded stream_end_signal
           # This part might be redundant if all paths correctly yield stream_end_signal or return
           # However, as a safeguard:
           # Check if the last yielded event was stream_end_signal, if not, yield it.
           # This requires tracking the last event, or a flag.
           # For simplicity, we assume normal flow or error paths handle this.
           pass


if __name__ == '__main__':
    # --- 配置你的 OpenAI 兼容 API 信息 ---
    # 1. 替换为你的 API 基础 URL
    # TEST_API_BASE_URL = "http://localhost:11434/v1" # 例如本地Ollama
    TEST_API_BASE_URL = "https://vps.012255.xyz/v1" # 你的URL

    # 2. 替换为你的 API Key (确保是字符串)
    TEST_API_KEY = "sk-parkTrBaCidihtErzgCEGcbpUUbEBznf3BALp067BDCs9Iat" # 你的API Key

    # 3. 替换为你的 API 支持的且你想测试的模型名称
    TEST_MODEL_NAME = "gemini-2.0-flash" # 你的模型
    # TEST_MODEL_NAME = "qwen2:0.5b" # 或者一个本地Ollama模型名


    if not TEST_API_KEY or TEST_API_KEY == "你的实际API_KEY字符串":
        print("错误：请在 api_client.py 的 __main__ 测试部分填写真实的 TEST_API_KEY。")
        # sys.exit(1) # 取消退出，以便观察其他测试

    client = APIClient(base_url=TEST_API_BASE_URL, api_key=TEST_API_KEY)
    
    print(f"\n--- 1. 测试非流式聊天 (模型: {TEST_MODEL_NAME}) ---")
    test_messages_non_stream = [{"role": "user", "content": "你好，请用中文简单介绍一下你自己。"}]
    response_ns = client.send_chat_message(test_messages_non_stream, model=TEST_MODEL_NAME, stream=False)
    if response_ns and isinstance(response_ns, dict) and response_ns.get("choices"):
        ai_content_ns = response_ns.get("choices", [{}])[0].get("message", {}).get("content")
        print(f"\nAI (非流式) 回复内容: {ai_content_ns if ai_content_ns else '无内容或格式错误'}")
    elif response_ns and isinstance(response_ns, dict) and response_ns.get("error"):
        print(f"\nAPI (非流式) 错误: {response_ns['error']}")
    else: print("\nAPI (非流式) 请求失败或未收到有效响应。")

    print(f"\n--- 2. 测试流式聊天 (模型: {TEST_MODEL_NAME}) ---")
    test_messages_stream = [{"role": "user", "content": "用一句话描述宇宙的浩瀚。"}]
    try:
        print("AI (流式) 回复:")
        full_stream_response = ""
        # Correctly unpack the tuple from the generator
        for event_type, event_data in client.send_chat_message(test_messages_stream, model=TEST_MODEL_NAME, stream=True):
            if event_type == "ai_delta" and event_data.get("content"):
                print(event_data["content"], end="", flush=True); full_stream_response += event_data["content"]
            elif event_type == "stream_end_signal": print("\n--- 流结束 ---"); break
            elif event_type == "error": print(f"\n--- 流错误: {event_data.get('message')} ---"); break
        if not full_stream_response: print("(未收到任何流式内容)")
    except Exception as e: print(f"\n流式聊天测试出错: {e}")

    print(f"\n--- 3. 测试工具调用 (流式, 模型: {TEST_MODEL_NAME}) ---")
    weather_tool_def_for_test = {
        "type": "function",
        "function": {
            "name": "query_weather", 
            "description": "获取指定位置的当前天气信息。",
            "parameters": {
                "type": "object",
                "properties": {"city": {"type": "string", "description": "城市名称，例如：'Beijing' 或 'Tokyo'."}},
                "required": ["city"]
            }
        }
    }
    test_messages_tool_call = [{"role": "user", "content": "东京今天天气怎么样？"}]
    try:
        print(f"发送带工具的请求: messages={test_messages_tool_call}, tools={[weather_tool_def_for_test]}")
        print("AI (工具调用流式) 回复/事件:")
        accumulated_tool_calls_test = []
        # Correctly unpack the tuple from the generator
        for event_type, event_data in client.send_chat_message(
            test_messages_tool_call, model=TEST_MODEL_NAME, stream=True, tools=[weather_tool_def_for_test]
        ):
            print(f"收到(工具调用测试)事件: ({event_type}, {event_data})") # Print tuple for clarity
            if event_type == "ai_delta" and event_data.get("content"): print(f"AI Delta: {event_data['content']}", end="", flush=True)
            elif event_type == "tool_call_request":
                print("\n--- 检测到工具调用请求！ ---")
                accumulated_tool_calls_test.extend(event_data.get('tool_calls', []))
            elif event_type == "stream_end_signal": print("\n--- 流结束 (工具调用测试) ---"); break
            elif event_type == "error": print(f"\n--- 流错误 (工具调用测试): {event_data.get('message')} ---"); break
        if accumulated_tool_calls_test: print(f"\n最终累积的工具调用请求: {json.dumps(accumulated_tool_calls_test, indent=2, ensure_ascii=False)}")
        else: print("\n(在工具调用测试中，未收到期望的 'tool_call_request' 事件或工具调用内容，请确保你的模型和API端点支持工具调用并正确配置)")
    except Exception as e: print(f"\n工具调用测试出错: {e}"); import traceback; traceback.print_exc()

# --- END OF MODIFIED api_client.py ---