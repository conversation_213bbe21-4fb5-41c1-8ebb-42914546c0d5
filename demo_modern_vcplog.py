#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化 VCPLog 通知系统演示
展示基于 VCPChat 设计的现代化通知功能
"""

import sys
import os
import json
import random
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QCheckBox, QSpinBox, QTextEdit, QSplitter,
    QGroupBox, QFormLayout, QComboBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from aipet.ui.modern_vcplog_system import VCPLogNotificationBar

class ModernVCPLogDemo(QMainWindow):
    """现代化VCPLog演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.notification_bar = None
        self.auto_timer = QTimer()
        self.demo_data_index = 0
        self.setup_ui()
        self.setup_demo_data()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("现代化 VCPLog 通知系统演示")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 右侧通知区域
        self.create_notification_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        
        # 状态栏
        self.statusBar().showMessage("现代化VCPLog通知系统就绪")
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 标题
        title = QLabel("🎛️ 控制面板")
        title.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        control_layout.addWidget(title)
        
        # 基本控制
        basic_group = QGroupBox("基本控制")
        basic_layout = QFormLayout(basic_group)
        
        # 主题切换
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题"])
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        basic_layout.addRow("主题:", self.theme_combo)
        
        # 侧边栏控制
        sidebar_layout = QHBoxLayout()
        self.toggle_sidebar_btn = QPushButton("切换侧边栏")
        self.toggle_sidebar_btn.clicked.connect(self.toggle_sidebar)
        sidebar_layout.addWidget(self.toggle_sidebar_btn)
        
        self.clear_btn = QPushButton("清空通知")
        self.clear_btn.clicked.connect(self.clear_notifications)
        sidebar_layout.addWidget(self.clear_btn)
        
        basic_layout.addRow("操作:", sidebar_layout)
        
        control_layout.addWidget(basic_group)
        
        # 功能设置
        feature_group = QGroupBox("功能设置")
        feature_layout = QFormLayout(feature_group)
        
        self.toast_enabled = QCheckBox("启用Toast通知")
        self.toast_enabled.setChecked(True)
        self.toast_enabled.toggled.connect(self.on_toast_toggled)
        feature_layout.addRow("Toast:", self.toast_enabled)
        
        self.sidebar_enabled = QCheckBox("启用侧边栏")
        self.sidebar_enabled.setChecked(True)
        self.sidebar_enabled.toggled.connect(self.on_sidebar_toggled)
        feature_layout.addRow("侧边栏:", self.sidebar_enabled)
        
        self.smart_display = QCheckBox("智能显示")
        self.smart_display.setChecked(True)
        self.smart_display.toggled.connect(self.on_smart_display_toggled)
        feature_layout.addRow("智能显示:", self.smart_display)
        
        control_layout.addWidget(feature_group)
        
        # 数量设置
        count_group = QGroupBox("数量设置")
        count_layout = QFormLayout(count_group)
        
        self.max_items_spin = QSpinBox()
        self.max_items_spin.setRange(10, 200)
        self.max_items_spin.setValue(100)
        self.max_items_spin.valueChanged.connect(self.on_max_items_changed)
        count_layout.addRow("最大项目数:", self.max_items_spin)
        
        self.max_toasts_spin = QSpinBox()
        self.max_toasts_spin.setRange(1, 10)
        self.max_toasts_spin.setValue(5)
        self.max_toasts_spin.valueChanged.connect(self.on_max_toasts_changed)
        count_layout.addRow("最大Toast数:", self.max_toasts_spin)
        
        control_layout.addWidget(count_group)
        
        # 测试控制
        test_group = QGroupBox("测试控制")
        test_layout = QVBoxLayout(test_group)
        
        # 手动添加
        manual_layout = QHBoxLayout()
        self.add_success_btn = QPushButton("成功消息")
        self.add_success_btn.clicked.connect(lambda: self.add_test_message("success"))
        manual_layout.addWidget(self.add_success_btn)
        
        self.add_error_btn = QPushButton("错误消息")
        self.add_error_btn.clicked.connect(lambda: self.add_test_message("error"))
        manual_layout.addWidget(self.add_error_btn)
        
        test_layout.addLayout(manual_layout)
        
        # 自动测试
        auto_layout = QHBoxLayout()
        self.auto_test_btn = QPushButton("开始自动测试")
        self.auto_test_btn.clicked.connect(self.toggle_auto_test)
        auto_layout.addWidget(self.auto_test_btn)
        
        self.auto_interval_spin = QSpinBox()
        self.auto_interval_spin.setRange(1, 10)
        self.auto_interval_spin.setValue(3)
        self.auto_interval_spin.setSuffix("秒")
        auto_layout.addWidget(self.auto_interval_spin)
        
        test_layout.addLayout(auto_layout)
        
        control_layout.addWidget(test_group)
        
        # 状态显示
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 9))
        status_layout.addWidget(self.status_text)
        
        control_layout.addWidget(status_group)
        
        control_layout.addStretch()
        parent.addWidget(control_widget)
    
    def create_notification_area(self, parent):
        """创建通知区域"""
        # 创建通知栏
        self.notification_bar = VCPLogNotificationBar()
        
        # 连接信号
        self.notification_bar.status_changed.connect(self.on_status_changed)
        self.notification_bar.toggle_requested.connect(self.on_toggle_requested)
        
        # 初始配置
        self.notification_bar.configure(
            toast_enabled=True,
            sidebar_enabled=True,
            smart_display=True,
            max_items=100,
            max_toasts=5
        )
        
        # 模拟连接状态
        self.notification_bar.update_connection_status("connected", "已连接到VCP服务器")
        
        parent.addWidget(self.notification_bar)
    
    def setup_demo_data(self):
        """设置演示数据"""
        self.demo_messages = [
            {
                "type": "vcp_log",
                "data": {
                    "tool_name": "文件管理器",
                    "status": "success",
                    "content": json.dumps({
                        "MaidName": "小助手",
                        "timestamp": datetime.now().isoformat(),
                        "original_plugin_output": {
                            "action": "文件上传",
                            "file_count": 3,
                            "total_size": "2.5MB"
                        }
                    }, ensure_ascii=False)
                }
            },
            {
                "type": "vcp_log", 
                "data": {
                    "tool_name": "图像生成",
                    "status": "running",
                    "content": "正在生成图像，预计需要30秒..."
                }
            },
            {
                "type": "vcp_log",
                "data": {
                    "tool_name": "数据分析",
                    "status": "error", 
                    "content": "分析失败：数据格式不正确"
                }
            },
            {
                "type": "daily_note_created",
                "data": {
                    "maidName": "智能助手",
                    "dateString": datetime.now().strftime("%Y-%m-%d"),
                    "status": "success",
                    "message": "今日工作总结已自动生成"
                }
            },
            {
                "type": "vcp_log",
                "data": {
                    "tool_name": "语音识别",
                    "status": "success",
                    "content": json.dumps({
                        "MaidName": "语音助手",
                        "timestamp": datetime.now().isoformat(),
                        "original_plugin_output": {
                            "text": "用户说：请帮我查询今天的天气情况",
                            "confidence": 0.95,
                            "duration": "3.2s"
                        }
                    }, ensure_ascii=False)
                }
            }
        ]
        
        # 自动测试定时器
        self.auto_timer.timeout.connect(self.add_random_message)
    
    @pyqtSlot()
    def toggle_sidebar(self):
        """切换侧边栏"""
        visible = self.notification_bar.toggle_sidebar()
        self.log_status(f"侧边栏{'显示' if visible else '隐藏'}")
    
    @pyqtSlot()
    def clear_notifications(self):
        """清空通知"""
        self.notification_bar.clear_messages()
        self.log_status("已清空所有通知")
    
    @pyqtSlot(str)
    def on_theme_changed(self, theme_text):
        """主题变化"""
        is_dark = theme_text == "深色主题"
        self.notification_bar.update_theme(is_dark)
        self.log_status(f"主题已切换到: {theme_text}")
    
    @pyqtSlot(bool)
    def on_toast_toggled(self, enabled):
        """Toast开关"""
        self.notification_bar.configure(toast_enabled=enabled)
        self.log_status(f"Toast通知{'启用' if enabled else '禁用'}")
    
    @pyqtSlot(bool)
    def on_sidebar_toggled(self, enabled):
        """侧边栏开关"""
        self.notification_bar.configure(sidebar_enabled=enabled)
        self.log_status(f"侧边栏{'启用' if enabled else '禁用'}")
    
    @pyqtSlot(bool)
    def on_smart_display_toggled(self, enabled):
        """智能显示开关"""
        self.notification_bar.configure(smart_display=enabled)
        self.log_status(f"智能显示{'启用' if enabled else '禁用'}")
    
    @pyqtSlot(int)
    def on_max_items_changed(self, value):
        """最大项目数变化"""
        self.notification_bar.configure(max_items=value)
        self.log_status(f"最大项目数设置为: {value}")
    
    @pyqtSlot(int)
    def on_max_toasts_changed(self, value):
        """最大Toast数变化"""
        self.notification_bar.configure(max_toasts=value)
        self.log_status(f"最大Toast数设置为: {value}")
    
    @pyqtSlot()
    def toggle_auto_test(self):
        """切换自动测试"""
        if self.auto_timer.isActive():
            self.auto_timer.stop()
            self.auto_test_btn.setText("开始自动测试")
            self.log_status("自动测试已停止")
        else:
            interval = self.auto_interval_spin.value() * 1000
            self.auto_timer.start(interval)
            self.auto_test_btn.setText("停止自动测试")
            self.log_status(f"自动测试已开始，间隔: {self.auto_interval_spin.value()}秒")
    
    def add_test_message(self, msg_type: str):
        """添加测试消息"""
        if msg_type == "success":
            message = {
                "type": "vcp_log",
                "data": {
                    "tool_name": "测试工具",
                    "status": "success",
                    "content": f"手动测试消息 - {datetime.now().strftime('%H:%M:%S')}"
                }
            }
        else:
            message = {
                "type": "vcp_log", 
                "data": {
                    "tool_name": "测试工具",
                    "status": "error",
                    "content": f"错误测试消息 - {datetime.now().strftime('%H:%M:%S')}"
                }
            }
        
        self.notification_bar.add_vcp_message(message)
        self.log_status(f"已添加{msg_type}测试消息")
    
    def add_random_message(self):
        """添加随机消息"""
        message = random.choice(self.demo_messages).copy()
        
        # 更新时间戳
        if message["type"] == "vcp_log" and "content" in message["data"]:
            try:
                content = json.loads(message["data"]["content"])
                if "timestamp" in content:
                    content["timestamp"] = datetime.now().isoformat()
                    message["data"]["content"] = json.dumps(content, ensure_ascii=False)
            except:
                pass
        
        self.notification_bar.add_vcp_message(message)
        self.demo_data_index += 1
    
    @pyqtSlot(str)
    def on_status_changed(self, status):
        """状态变化"""
        self.statusBar().showMessage(status)
    
    @pyqtSlot()
    def on_toggle_requested(self):
        """切换请求"""
        self.log_status("收到侧边栏切换请求")
    
    def log_status(self, message: str):
        """记录状态"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.status_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("现代化VCPLog演示")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = ModernVCPLogDemo()
    window.show()
    
    print("=" * 60)
    print("🎉 现代化 VCPLog 通知系统演示")
    print("=" * 60)
    print("功能特性：")
    print("✨ 基于 VCPChat 设计的现代化UI")
    print("🔔 智能Toast + 侧边栏双重通知")
    print("🎨 完整的深色/浅色主题支持")
    print("📱 流畅的动画和交互效果")
    print("⚙️ 丰富的配置选项")
    print("🚀 高性能和稳定性")
    print("=" * 60)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
