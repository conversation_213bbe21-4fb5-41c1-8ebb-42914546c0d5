import os
import json
from typing import Optional, List, Union

# Security Note: This plugin allows reading and writing to the local filesystem.
# We restrict file access to be within the project's workspace for safety.
# The project workspace is determined as two directories above this plugin file.
# e.g., /path/to/project/aipet/plugins/file_io.py -> /path/to/project
WORKSPACE_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def _get_safe_path(path: str) -> Optional[str]:
    """
    Resolves a file path.
    - If the path is absolute, it is used directly after normalization.
    - If the path is relative, it is resolved from the workspace root.
    """
    if os.path.isabs(path):
        return os.path.realpath(path)
    
    # For relative paths, resolve them from the workspace root.
    full_path = os.path.join(WORKSPACE_ROOT, path)
    return os.path.realpath(full_path)

def read_file(path: str) -> str:
    """Reads the content of a file at the given path."""
    safe_path = _get_safe_path(path)
    if not safe_path:
        return f"Error: Invalid path '{path}'."

    try:
        with open(safe_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        return f"Error: File not found at path: {path}"
    except Exception as e:
        return f"Error: Failed to read file: {str(e)}"

def write_file(path: str, content: str) -> str:
    """Writes or overwrites content to a file at the given path."""
    safe_path = _get_safe_path(path)
    if not safe_path:
        return f"Error: Invalid path '{path}'."
        
    try:
        os.makedirs(os.path.dirname(safe_path), exist_ok=True)
        with open(safe_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return f"Success: File written successfully to {path}"
    except Exception as e:
        return f"Error: Failed to write file: {str(e)}"
        
def list_files(path: str = ".") -> Union[List[str], str]:
    """Lists files and directories at a given path."""
    safe_path = _get_safe_path(path)
    if not safe_path:
        return f"Error: Invalid path '{path}'."
        
    try:
        if not os.path.isdir(safe_path):
            return f"Error: Path '{path}' is not a directory."
        
        items = os.listdir(safe_path)
        return items
    except FileNotFoundError:
        return f"Error: Directory not found at path: {path}"
    except Exception as e:
        return f"Error: Failed to list files: {str(e)}"

def get_tools():
    return {
        "read_file": {
            "function": read_file,
            "definition": {
                "type": "function",
                "function": {
                    "name": "read_file",
                    "description": "Reads the content of a specified file. Use an absolute path (e.g., 'C:/Users/<USER>/doc.txt') to access any file on the system, or a relative path to access a file inside the project workspace.",
                    "parameters": {"type": "object", "properties": {"path": {"type": "string", "description": "The absolute or relative path to the file."}}, "required": ["path"]}
                }
            }
        },
        "write_file": {
            "function": write_file,
            "definition": {
                "type": "function",
                "function": {
                    "name": "write_file",
                    "description": "Writes content to a specified file. Use an absolute path to access any file on the system, or a relative path for a file inside the project workspace. This will create parent directories and overwrite existing files.",
                    "parameters": {"type": "object", "properties": {"path": {"type": "string", "description": "The absolute or relative path to the file."}, "content": {"type": "string", "description": "The content to write."}}, "required": ["path", "content"]}
                }
            }
        },
        "list_files": {
            "function": list_files,
            "definition": {
                "type": "function",
                "function": {
                    "name": "list_files",
                    "description": "Lists files and directories. Use an absolute path to list any directory, or a relative path for a directory inside the project workspace. Defaults to '.' (the project root).",
                    "parameters": {"type": "object", "properties": {"path": {"type": "string", "description": "The absolute or relative path to the directory. Defaults to '.' (the project root)."}},"required": []}
                }
            }
        }
    } 