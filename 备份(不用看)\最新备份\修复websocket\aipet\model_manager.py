import os
import json
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import glob

@dataclass
class ModelInfo:
    """模型信息数据类"""
    id: str                    # 模型唯一标识
    name: str                  # 显示名称
    path: str                  # model3.json文件路径
    description: str           # 模型描述
    thumbnail: Optional[str]   # 缩略图路径
    author: Optional[str]      # 作者信息
    version: Optional[str]     # 版本信息
    tags: List[str]           # 标签（如：女性、动漫、游戏等）

class ModelManager:
    """Live2D模型管理器"""
    
    def __init__(self, config_file: str = "model_config.json"):
        self.config_file = config_file
        self.available_models: Dict[str, ModelInfo] = {}
        self.current_model_id: Optional[str] = None
        self.favorites: List[str] = []
        self.recent_used: List[str] = []
        
        # 模型扫描基础目录 - 修改为新的目录结构
        self.models_base_dir = os.path.dirname(os.path.dirname(__file__))
        
        self._load_config()
        self._scan_available_models()
    
    def _scan_available_models(self) -> Dict[str, ModelInfo]:
        """扫描并验证可用的Live2D模型 - 更新扫描路径"""
        models = {}
        
        # 确保模型目录存在
        if not os.path.exists(self.models_base_dir):
            print(f"模型目录不存在: {self.models_base_dir}")
            print("请确保将模型文件放置在 aipet/live2dmodle/ 目录下")
            return models
        
        # 扫描 live2dmodle 目录下的模型文件
        search_patterns = [
            os.path.join(self.models_base_dir, "*", "*.model3.json"),
            os.path.join(self.models_base_dir, "*", "*.model.json"),
        ]
        
        print(f"正在扫描模型目录: {self.models_base_dir}")
        
        for pattern in search_patterns:
            for model_path in glob.glob(pattern):
                print(f"发现模型文件: {model_path}")
                if self._validate_model_file(model_path):
                    model_info = self._extract_model_info(model_path)
                    if model_info:
                        models[model_info.id] = model_info
                        print(f"✓ 成功加载模型: {model_info.name} ({model_info.id})")
                else:
                    print(f"✗ 模型文件验证失败: {model_path}")
        
        self.available_models = models
        print(f"总共发现 {len(models)} 个有效模型")
        return models
    
    def _validate_model_file(self, model_path: str) -> bool:
        """验证模型文件完整性"""
        try:
            if not os.path.exists(model_path):
                print(f"模型文件不存在: {model_path}")
                return False
            
            # 检查文件大小
            file_size = os.path.getsize(model_path)
            if file_size == 0:
                print(f"模型文件为空: {model_path}")
                return False
            
            # 尝试解析JSON
            try:
                with open(model_path, 'r', encoding='utf-8') as f:
                    model_config = json.load(f)
            except json.JSONDecodeError as e:
                print(f"模型文件JSON格式无效 {model_path}: {e}")
                return False
            except UnicodeDecodeError as e:
                print(f"模型文件编码无效 {model_path}: {e}")
                return False
            
            # 检查必要字段
            required_fields = ['FileReferences', 'Groups']
            missing_fields = [field for field in required_fields if field not in model_config]
            if missing_fields:
                print(f"模型配置缺少必要字段 {model_path}: {missing_fields}")
                return False
            
            # 检查相关文件是否存在
            model_dir = os.path.dirname(model_path)
            file_refs = model_config.get('FileReferences', {})
            
            # 检查moc文件
            if 'Moc' in file_refs:
                moc_file = file_refs['Moc']
                moc_path = os.path.join(model_dir, moc_file)
                if not os.path.exists(moc_path):
                    print(f"缺少Moc文件: {moc_path}")
                    return False
                if os.path.getsize(moc_path) == 0:
                    print(f"Moc文件为空: {moc_path}")
                    return False
            else:
                print(f"模型配置缺少Moc文件引用: {model_path}")
                return False
            
            # 检查纹理文件
            if 'Textures' in file_refs:
                textures = file_refs['Textures']
                if not textures:
                    print(f"模型配置纹理列表为空: {model_path}")
                    return False
                
                for texture_file in textures:
                    texture_path = os.path.join(model_dir, texture_file)
                    if not os.path.exists(texture_path):
                        print(f"缺少纹理文件: {texture_path}")
                        return False
                    if os.path.getsize(texture_path) == 0:
                        print(f"纹理文件为空: {texture_path}")
                        return False
            else:
                print(f"模型配置缺少纹理文件引用: {model_path}")
                return False
            
            print(f"✓ 模型文件验证通过: {model_path}")
            return True
            
        except Exception as e:
            print(f"验证模型文件失败 {model_path}: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _extract_model_info(self, model_path: str) -> Optional[ModelInfo]:
        """从模型文件提取信息"""
        try:
            model_dir = os.path.dirname(model_path)
            folder_name = os.path.basename(model_dir)
            
            # 生成模型ID
            model_id = folder_name.lower().replace(' ', '_')
            
            # 根据文件夹名称推断显示名称
            name_mapping = {
                'linghu': '灵狐芊芊',
                'ganyu': '原神甘雨', 
                'chocola': '巧克力',
                'kasumi2': '香澄',
                'vanilla': '香草',
                'azuki': '红豆',
                'maple': '枫',
                'cinnamon': '肉桂',
                'coconut': '椰子',
                'shigure': '时雨'
            }
            
            display_name = name_mapping.get(folder_name, folder_name.title())
            
            # 查找缩略图
            thumbnail_patterns = [
                os.path.join(model_dir, 'thumbnail.png'),
                os.path.join(model_dir, 'icon.png'),
                os.path.join(model_dir, 'preview.png'),
                os.path.join(model_dir, f'{folder_name}.png')
            ]
            
            thumbnail = None
            for thumb_path in thumbnail_patterns:
                if os.path.exists(thumb_path):
                    thumbnail = thumb_path
                    break
            
            # 生成相对路径用于配置文件，并统一使用正斜杠
            try:
                relative_path = os.path.relpath(model_path, os.path.dirname(os.path.dirname(__file__)))
                relative_path = relative_path.replace('\\', '/')  # 统一使用正斜杠
                print(f"DEBUG: 模型 {folder_name} 相对路径: {relative_path}")
            except Exception as e:
                # 如果相对路径计算失败，使用绝对路径
                print(f"计算相对路径失败，使用绝对路径: {e}")
                relative_path = model_path.replace('\\', '/')
            
            return ModelInfo(
                id=model_id,
                name=display_name,
                path=relative_path,  # 使用相对路径
                description=f"Live2D模型: {display_name}",
                thumbnail=thumbnail,
                author=None,
                version=None,
                tags=[]
            )
            
        except Exception as e:
            print(f"提取模型信息失败 {model_path}: {e}")
            return None
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """获取指定模型信息"""
        return self.available_models.get(model_id)
    
    def get_all_models(self) -> Dict[str, ModelInfo]:
        """获取所有可用模型"""
        return self.available_models.copy()
    
    def set_current_model(self, model_id: str) -> bool:
        """设置当前模型"""
        if model_id in self.available_models:
            self.current_model_id = model_id
            self._update_recent_used(model_id)
            self._save_config()
            return True
        return False
    
    def get_current_model(self) -> Optional[ModelInfo]:
        """获取当前模型信息"""
        if self.current_model_id:
            return self.available_models.get(self.current_model_id)
        return None
    
    def add_to_favorites(self, model_id: str) -> bool:
        """添加到收藏"""
        if model_id in self.available_models and model_id not in self.favorites:
            self.favorites.append(model_id)
            self._save_config()
            return True
        return False
    
    def remove_from_favorites(self, model_id: str) -> bool:
        """从收藏移除"""
        if model_id in self.favorites:
            self.favorites.remove(model_id)
            self._save_config()
            return True
        return False
    
    def _update_recent_used(self, model_id: str):
        """更新最近使用列表"""
        if model_id in self.recent_used:
            self.recent_used.remove(model_id)
        self.recent_used.insert(0, model_id)
        
        # 只保留最近5个
        self.recent_used = self.recent_used[:5]
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.current_model_id = config.get('current_model')
                self.favorites = config.get('favorites', [])
                self.recent_used = config.get('recent_used', [])
        except Exception as e:
            print(f"加载模型配置失败: {e}")
    
    def _save_config(self):
        """保存配置文件"""
        try:
            config = {
                'current_model': self.current_model_id,
                'favorites': self.favorites,
                'recent_used': self.recent_used,
                'models_directory': self.models_base_dir,
                'models': {
                    model_id: {
                        'name': info.name,
                        'path': info.path,
                        'description': info.description,
                        'thumbnail': info.thumbnail,
                        'author': info.author,
                        'version': info.version,
                        'tags': info.tags
                    }
                    for model_id, info in self.available_models.items()
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存模型配置失败: {e}")
    
    def refresh_models(self):
        """刷新模型列表"""
        print("正在刷新模型列表...")
        self._scan_available_models()
        self._save_config()
        print("模型列表刷新完成")