import sys
import os

# --- BEGIN: Add PyQt5 DLL directory to search path ---
# Construct path to PyQt5's Qt5\bin directory relative to sys.executable
# sys.executable is E:\live2d\.venv\Scripts\python.exe
# We want E:\live2d\.venv\Lib\site-packages\PyQt5\Qt5\bin
pyqt_dll_path = ""
if sys.executable and "python.exe" in sys.executable.lower():
    # Assuming standard venv structure: .venv/Scripts/python.exe
    # Go up two levels from Scripts to .venv, then down to Lib/site-packages
    venv_root = os.path.dirname(os.path.dirname(sys.executable))
    potential_pyqt_dll_path = os.path.join(venv_root, 'Lib', 'site-packages', 'PyQt5', 'Qt5', 'bin')
    if os.path.isdir(potential_pyqt_dll_path):
        pyqt_dll_path = potential_pyqt_dll_path
    else:
        # Fallback or alternative structure check if needed, for now just log
        print(f"Info: Standard venv PyQt5 DLL path not found at {potential_pyqt_dll_path}")
        # Attempt a more direct path based on common site-packages location if sys.path[0] is script dir
        # This is less reliable if sys.path has been heavily modified
        for path_entry in sys.path:
            if 'site-packages' in path_entry:
                test_path = os.path.join(path_entry, 'PyQt5', 'Qt5', 'bin')
                if os.path.isdir(test_path):
                    pyqt_dll_path = test_path
                    print(f"Info: Found PyQt5 DLL path via sys.path scan: {pyqt_dll_path}")
                    break
else:
    print(f"Warning: sys.executable does not look like a standard venv python.exe: {sys.executable}")

if pyqt_dll_path and os.path.exists(pyqt_dll_path):
    print(f"Attempting to add DLL directory: {pyqt_dll_path}")
    try:
        if hasattr(os, 'add_dll_directory'): # Python 3.8+
            # Check if it's already effectively in PATH or added
            # This is a simplification; true check is harder.
            # For now, just add it if the function exists.
            os.add_dll_directory(pyqt_dll_path)
            print(f"Successfully added to DLL search path (os.add_dll_directory): {pyqt_dll_path}")
        else: # Fallback for older Python
            print(f"os.add_dll_directory not available. Modifying PATH temporarily.")
            current_path = os.environ.get('PATH', '')
            if pyqt_dll_path not in current_path.split(os.pathsep):
                os.environ['PATH'] = pyqt_dll_path + os.pathsep + current_path
                print(f"PATH modified to include: {pyqt_dll_path}")
            else:
                print(f"Info: PyQt5 DLL path already in PATH: {pyqt_dll_path}")
    except Exception as e_dll:
        print(f"Error adding DLL directory {pyqt_dll_path}: {e_dll}")
else:
    if not pyqt_dll_path:
        print(f"Error: Could not determine PyQt5 DLL path.")
    else:
        print(f"Error: Determined PyQt5 DLL path does not exist: {pyqt_dll_path}")
# --- END: Add PyQt5 DLL directory to search path ---

import json
import time  # 移动到文件顶部
import math  # 移动到文件顶部
from typing import Optional, Dict, List, Callable, Union, Any # Union 已添加, Any 新增
import ctypes
from ctypes import wintypes
import threading # 新增导入

# 尝试导入PyQt5,如果失败则尝试PyQt6
try:
    from PyQt5.QtWidgets import QOpenGLWidget, QApplication
    from PyQt5.QtCore import Qt, QPoint, QSize, QTimer, QTimerEvent, pyqtSignal, PYQT_VERSION_STR
    from PyQt5.QtGui import QMouseEvent, QPainter, QColor, QPixmap, QImage, QGuiApplication, QCursor, QWheelEvent # 添加 QWheelEvent
    # from PyQt5.QtOpenGL import QOpenGLFormat # 再次移除
    IS_PYQT6 = False
    if PYQT_VERSION_STR and PYQT_VERSION_STR.startswith("6."):
        IS_PYQT6 = True
    print("✓ PyQt5 导入成功")  # 添加确认信息
except ImportError:
    try:
        from PyQt6.QtWidgets import QOpenGLWidget, QApplication
        from PyQt6.QtCore import Qt, QPoint, QSize, QTimer, QTimerEvent, pyqtSignal, PYQT_VERSION_STR
        from PyQt6.QtGui import QMouseEvent, QPainter, QColor, QPixmap, QImage, QGuiApplication, QCursor, QWheelEvent # 添加 QWheelEvent
        # from PyQt6.QtOpenGL import QOpenGLFormat # 再次移除
        IS_PYQT6 = True
        if not (PYQT_VERSION_STR and PYQT_VERSION_STR.startswith("6.")):
            IS_PYQT6 = False
        print("✓ PyQt6 导入成功")  # 添加确认信息
    except ImportError:
        print("错误:未找到 PyQt5 或 PyQt6。请确保已安装其中一个。")
        sys.exit(1)
        IS_PYQT6 = False

# 尝试导入 OpenGL
try:
    from OpenGL.GL import (glClearColor, glClear, GL_COLOR_BUFFER_BIT, GL_DEPTH_BUFFER_BIT,
                          glReadPixels, GL_RGBA, GL_UNSIGNED_BYTE, glEnable, glBlendFunc,
                          GL_BLEND, GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA) # 保留原始更完整的导入
    GL_AVAILABLE = True
    print("✓ OpenGL 导入成功") # 添加确认信息
except ImportError:
    print("警告:无法导入 PyOpenGL。可能无法手动清除OpenGL背景或进行像素读取。")
    GL_AVAILABLE = False

# 尝试导入 live2d-py
try:
    import live2d.v3 as live2d
    LIVE2D_AVAILABLE = True
    print("✓ live2d-py 导入成功") # 添加确认信息
except ImportError:
    print("错误:无法导入 live2d.v3 模块。请确保已正确安装 live2d-py 库。")
    print("可以尝试: pip install live2d-py")
    LIVE2D_AVAILABLE = False
    live2d = None

class Live2DWidget(QOpenGLWidget):
    """
    负责 Live2D 模型的加载、OpenGL 渲染、动画更新、用户交互以及状态管理。
    作为桌宠的视觉呈现核心。
    """
    model_loaded_signal = pyqtSignal(bool)
    model_clicked_signal = pyqtSignal(str)
    window_dragged_signal = pyqtSignal(QPoint)
    
    # 新增口型同步相关信号
    lipsync_started_signal = pyqtSignal(str)  # 发送音频文件路径
    lipsync_stopped_signal = pyqtSignal()
    lipsync_error_signal = pyqtSignal(str)  # 发送错误信息
    
    # 新增TTS播放控制信号
    stop_tts_playback_signal = pyqtSignal()  # 请求停止TTS播放信号

    def __init__(self, model_config_path: str, initial_pos: Optional[QPoint] = None, initial_size: Optional[QSize] = None, parent=None):
        super().__init__(parent)
        
        # 设置高质量OpenGL格式 (暂时移除以排查导入问题)
        # format_obj = QOpenGLFormat()
        # format_obj.setSamples(4)  # 4x多重采样抗锯齿
        # format_obj.setAlphaBufferSize(8)  # 提高Alpha通道精度
        # format_obj.setDepthBufferSize(24)  # 提高深度缓冲精度
        # format_obj.setVersion(3, 3)  # 使用OpenGL 3.3
        # format_obj.setProfile(QOpenGLFormat.OpenGLContextProfile.CoreProfile)
        # self.setFormat(format_obj)
        
        # 模型相关属性
        self.model: Optional[live2d.LAppModel] = None
        self.model_config_path: str = model_config_path
        self.canvas: Optional[live2d.utils.canvas.Canvas] = None

        # 交互属性
        self.is_dragging: bool = False
        self.drag_start_position: Optional[QPoint] = None
        self.enable_mouse_tracking = True
        self.is_mouse_over_model_area = False

        # 动画和效果属性
        self.available_expressions: List[str] = []
        self.available_motion_groups: Dict[str, int] = {}
        self.current_scale = 1.0  # 新增：模型当前缩放比例
        self.min_scale = 0.1      # 新增：最小缩放比例
        self.max_scale = 5.0      # 新增：最大缩放比例
        
        # 新增：动画相关属性
        self.breathing_phase = 0.0
        self.breathing_speed = 2.0
        self._last_frame_time = time.time()
        
        # 新增：视线追踪优化
        self.target_eye_x = 0.0
        self.target_eye_y = 0.0
        self.current_eye_x = 0.0
        self.current_eye_y = 0.0
        self.eye_tracking_smoothness = 0.1
        
        # 新增：光晕效果
        self.glow_intensity = 0.0
        self.glow_target = 0.0
        
        # 口型同步相关属性
        self.wav_handler: Optional[object] = None  # live2d.utils.WavHandler实例
        self.is_lipsync_active: bool = False
        self.lipsync_audio_path: Optional[str] = None
        self.mouth_open_param_id: str = "ParamMouthOpenY"  # 标准Live2D嘴部开合参数
        self.lipsync_sensitivity: float = 3.0  # 口型同步敏感度调节
        self.lipsync_thread: Optional[threading.Thread] = None
        self.lipsync_stop_event: Optional[threading.Event] = None

        # 帧率控制
        self.target_fps = 60
        self.timer_interval_ms = int(1000 / self.target_fps)

        # 窗口属性设置
        if initial_pos:
            self.move(initial_pos)
        if initial_size:
            self.resize(initial_size)
        else:
            self.resize(500, 600)

        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.Tool)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating, True)
        self.setAutoFillBackground(False)
        self.setMouseTracking(True)
        self.setWindowTitle("AI桌宠 - Live2D")
        
        # 设置定时器
        self._setup_timers()
        
        # Windows特殊设置
        self._setNonActivatingStyleOnWindows()

    def _setup_timers(self):
        """设置各种定时器"""
        # 主更新定时器（合并多个更新循环以提高性能）
        self.main_update_timer = QTimer(self)
        self.main_update_timer.timeout.connect(self._main_update_loop)
        
        # 鼠标穿透状态更新定时器
        self.mouse_penetration_timer = QTimer(self)
        self.mouse_penetration_timer.timeout.connect(self._update_mouse_penetration_status)
        self.mouse_penetration_timer.start(50)
        
        # 随机空闲动作定时器
        self.idle_timer = QTimer(self)
        self.idle_timer.timeout.connect(self._trigger_random_idle_action)
        self.idle_timer.start(15000)  # 每15秒触发一次

    def _setNonActivatingStyleOnWindows(self):
        """在Windows上设置窗口样式以避免激活"""
        if sys.platform == "win32":
            try:
                hwnd_obj = self.winId()
                if hwnd_obj:
                    hwnd = int(hwnd_obj)
                    GWL_EXSTYLE = -20
                    WS_EX_NOACTIVATE = 0x08000000
                    
                    user32 = ctypes.windll.user32
                    current_ex_style = user32.GetWindowLongPtrW(hwnd, GWL_EXSTYLE)
                    new_ex_style = current_ex_style | WS_EX_NOACTIVATE
                    
                    if user32.SetWindowLongPtrW(hwnd, GWL_EXSTYLE, new_ex_style) == 0:
                        pass

                    SWP_FRAMECHANGED = 0x0020
                    SWP_NOMOVE = 0x0002
                    SWP_NOSIZE = 0x0001
                    SWP_NOZORDER = 0x0004
                    user32.SetWindowPos(hwnd, 0, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER | SWP_FRAMECHANGED)
                else:
                    pass
            except Exception as e:
                pass

    def start_lipsync(self, audio_path: str, sensitivity: float = 3.0) -> bool:
        """
        启动口型同步
        
        Args:
            audio_path: WAV音频文件路径
            sensitivity: 敏感度调节（建议范围：1.0-10.0）
        
        Returns:
            启动是否成功
        """
        if not LIVE2D_AVAILABLE or not self.model:
            print("警告: Live2D不可用，无法启动口型同步")
            self.lipsync_error_signal.emit("Live2D不可用或模型未加载")
            return False
        
        if not os.path.exists(audio_path):
            print(f"错误: 音频文件不存在: {audio_path}")
            self.lipsync_error_signal.emit(f"音频文件不存在: {audio_path}")
            return False
        
        # 停止现有的口型同步
        # self.stop_lipsync() # stop_lipsync 内部会处理信号发送 (确保在try之前调用，如果需要先停止)
        # 考虑到 stop_lipsync 可能会 emit error, 并且 start 也可能 emit error,
        # 确保 stop_lipsync 的调用位置和错误处理逻辑清晰。
        # 如果 start_lipsync 失败，不应该因为之前的 stop_lipsync 信号而混淆。
        # 决定在 try 块内部，成功创建 WavHandler 之前停止。
        
        try:
            # 停止现有的口型同步
            current_was_active = self.is_lipsync_active
            self.stop_lipsync() # 调用 stop_lipsync，它会处理自己的信号

            # 检查是否有WavHandler
            wav_handler_class = None
            if hasattr(live2d, 'utils') and hasattr(live2d.utils, 'WavHandler'):
                wav_handler_class = live2d.utils.WavHandler
                print("DEBUG: Found WavHandler via live2d.utils.WavHandler")
            else:
                print("DEBUG: live2d.utils.WavHandler not found via hasattr. Attempting direct import from live2d.utils.lipsync...")
                try:
                    from live2d.utils.lipsync import WavHandler as DirectWavHandler
                    wav_handler_class = DirectWavHandler
                    print("DEBUG: Successfully imported WavHandler directly from live2d.utils.lipsync.")
                except ImportError:
                    error_msg = "警告: live2d-py WavHandler 无法找到或导入 (ImportError)，口型同步不可用"
                    print(error_msg)
                    self.lipsync_error_signal.emit(error_msg)
                    return False
            
            if not wav_handler_class:
                # This case should ideally be caught by the ImportError above if direct import also fails.
                error_msg = "警告: 未能获取 WavHandler 类，口型同步不可用"
                print(error_msg)
                self.lipsync_error_signal.emit(error_msg)
                return False

            # 创建WavHandler实例
            self.wav_handler = wav_handler_class()
            
            # 加载音频文件
            self.wav_handler.Start(audio_path) # Call Start(), it will return None
            
            # 检查 WavHandler 内部状态以判断是否成功加载
            # WavHandler.Start() 成功时会设置 pcmData 和 startTime
            if self.wav_handler and self.wav_handler.pcmData is not None and \
               hasattr(self.wav_handler, 'startTime') and self.wav_handler.startTime != -1:
                self.is_lipsync_active = True
                self.lipsync_audio_path = audio_path
                self.lipsync_sensitivity = sensitivity
                
                self.lipsync_started_signal.emit(audio_path)
                print(f"✓ 口型同步已启动 (WavHandler 状态检查通过): {audio_path} (敏感度: {sensitivity})")
                return True
            else:
                # WavHandler.Start() 内部可能发生错误，或者 pcmData 未被设置
                error_msg = f"错误: WavHandler 未能成功加载音频文件: {audio_path} (WavHandler.Start 返回None或内部状态无效)"
                print(error_msg)
                if self.wav_handler: # 尝试打印更多调试信息
                    print(f"  WavHandler pcmData is None: {self.wav_handler.pcmData is None}")
                    if hasattr(self.wav_handler, 'startTime'):
                        print(f"  WavHandler startTime: {self.wav_handler.startTime}")
                    else:
                        print(f"  WavHandler has no startTime attribute.")
                self.wav_handler = None
                self.is_lipsync_active = False
                self.lipsync_error_signal.emit(error_msg)
                return False
                
        except Exception as e:
            error_msg = f"启动口型同步失败: {e}"
            print(error_msg)
            self.wav_handler = None
            self.is_lipsync_active = False
            self.lipsync_error_signal.emit(error_msg)
            return False

    def stop_lipsync(self):
        """停止口型同步"""
        was_active_before_call = self.is_lipsync_active # 记录调用此方法时是否处于活动状态
        
        try:
            if self.is_lipsync_active: # 只有在活动时才执行停止逻辑
                self.is_lipsync_active = False # 首先标记为不活动
                
                if self.wav_handler:
                    try:
                        # 停止WavHandler
                        if hasattr(self.wav_handler, 'Stop'):
                            self.wav_handler.Stop()
                    except Exception as e:
                        print(f"停止WavHandler时出错: {e}")
                        # 不在这里发送 error_signal，避免与 start_lipsync 中的错误混淆
                    finally:
                        self.wav_handler = None # 确保释放handler
                
                # 重置嘴部参数为关闭状态
                if self.model and hasattr(self.model, 'SetParameterValue'):
                    try:
                        self.model.SetParameterValue(self.mouth_open_param_id, 0.0)
                    except Exception as e:
                        print(f"重置嘴部参数时出错: {e}")
                
                self.lipsync_audio_path = None
                print("✓ 口型同步已停止")
            
            # 只有当它之前是活动的，并且现在（尝试）停止后才发送信号
            if was_active_before_call:
                self.lipsync_stopped_signal.emit()
                
        except Exception as e:
            # 这个异常是 stop_lipsync 内部的逻辑错误，而不是 WavHandler 的错误
            error_msg = f"停止口型同步过程中发生内部异常: {e}"
            print(error_msg)
            # 考虑是否需要一个特定的内部错误信号，或者也用 lipsync_error_signal
            self.lipsync_error_signal.emit(error_msg)

    def update_lipsync(self):
        """
        更新口型同步（在主渲染循环中调用）
        """
        if not self.is_lipsync_active or not self.wav_handler or not self.model:
            return
        
        try:
            # 更新WavHandler
            if hasattr(self.wav_handler, 'Update') and callable(getattr(self.wav_handler, 'Update')):
                 self.wav_handler.Update()
            else:
                # 如果 WavHandler 没有 Update 方法，或者 Update 不可调用，则认为无法进行更新
                # 这可能表示 WavHandler 未正确初始化或 live2d-py 版本问题
                # print("警告: WavHandler 没有可调用的 Update 方法。")
                return

            # 获取当前的RMS值（音频强度）
            rms_value = self.wav_handler.GetRms()
            
            # 将RMS值转换为嘴部开合程度
            mouth_open_value = min(1.0, rms_value * self.lipsync_sensitivity)
            
            # 平滑处理：避免过于突然的变化
            if hasattr(self, '_last_mouth_value'):
                smooth_factor = 0.7  # 平滑系数，越大越平滑
                mouth_open_value = (self._last_mouth_value * smooth_factor +
                                  mouth_open_value * (1 - smooth_factor))
            
            self._last_mouth_value = mouth_open_value
            
            # 设置Live2D模型的嘴部参数
            self.model.SetParameterValue(self.mouth_open_param_id, mouth_open_value)
            
        except Exception as e:
            error_msg = f"更新口型同步时出错: {e}"
            print(error_msg)
            self.lipsync_error_signal.emit(error_msg)
            self.stop_lipsync() # 发生错误时停止口型同步

    def set_lipsync_sensitivity(self, sensitivity: float):
        """
        设置口型同步敏感度
        
        Args:
            sensitivity: 敏感度值（建议范围：1.0-10.0）
        """
        self.lipsync_sensitivity = max(0.1, min(10.0, sensitivity))
        print(f"口型同步敏感度已设置为: {self.lipsync_sensitivity}")

    def is_lipsync_running(self) -> bool:
        """检查口型同步是否正在运行"""
        return self.is_lipsync_active and self.wav_handler is not None

    def get_lipsync_info(self) -> Dict[str, Any]:
        """
        获取口型同步状态信息
        
        Returns:
            包含口型同步状态的字典
        """
        return {
            "active": self.is_lipsync_active,
            "audio_path": self.lipsync_audio_path,
            "sensitivity": self.lipsync_sensitivity,
            "has_wav_handler": self.wav_handler is not None,
            "mouth_param_id": self.mouth_open_param_id
        }

    def test_lipsync_parameters(self) -> bool:
        """
        测试口型同步参数是否可用
        
        Returns:
            测试是否成功
        """
        if not self.model or not LIVE2D_AVAILABLE:
            print("模型未加载，无法测试口型同步参数")
            return False
        
        try:
            # 检查是否有嘴部参数
            param_count = self.model.GetParameterCount()
            available_params = []
            
            for i in range(param_count):
                param = self.model.GetParameter(i)
                if param and hasattr(param, 'id'):
                    available_params.append(param.id)
            
            # 检查常见的嘴部参数
            mouth_params = [
                "ParamMouthOpenY",
                "PARAM_MOUTH_OPEN_Y",
                "MouthOpenY",
                "mouth_open_y"
            ]
            
            found_mouth_param = None
            for param_id in mouth_params:
                if param_id in available_params:
                    found_mouth_param = param_id
                    break
            
            if found_mouth_param:
                self.mouth_open_param_id = found_mouth_param
                print(f"✓ 找到嘴部参数: {found_mouth_param}")
                
                # 测试参数设置
                self.model.SetParameterValue(found_mouth_param, 0.5)
                self.model.Update() # 需要Update才能看到效果或让参数生效
                self.model.SetParameterValue(found_mouth_param, 0.0)
                
                print("✓ 口型同步参数测试成功")
                return True
            else:
                print("❌ 未找到可用的嘴部参数")
                print(f"可用参数: {available_params[:10]}...")  # 显示前10个参数
                return False
                
        except Exception as e:
            print(f"测试口型同步参数时出错: {e}")
            return False

    def switch_model(self, new_model_path: str) -> bool:
        """
        切换到新的Live2D模型
        
        Args:
            new_model_path: 新模型的.model3.json文件路径（可以是相对路径）
        
        Returns:
            切换是否成功
        """
        try:
            # 处理相对路径
            if not os.path.isabs(new_model_path):
                # 相对于项目根目录的路径
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                new_model_path = os.path.join(project_root, new_model_path)
            
            # 标准化路径
            new_model_path = os.path.normpath(new_model_path)
            print(f"正在切换模型到: {new_model_path}")
            
            if not os.path.exists(new_model_path):
                print(f"模型文件不存在: {new_model_path}")
                return False
            
            # 停止当前的动画和音频
            self.stop_lipsync()
            if hasattr(self, '_stop_all_animations'):
                self._stop_all_animations()
            
            # 保存当前状态用于失败时恢复
            old_scale = self.current_scale
            old_position = self.pos()
            old_model_path = self.model_config_path
            old_expressions = self.available_expressions.copy()
            old_motion_groups = self.available_motion_groups.copy()
            
            # 安全地释放当前模型
            if self.model:
                try:
                    print("正在释放当前模型资源...")
                    if hasattr(self.model, 'Release'):
                        self.model.Release()
                    del self.model
                    self.model = None
                except Exception as e:
                    print(f"释放当前模型资源时出错: {e}")
                    self.model = None
            
            # 清空缓存的模型信息
            self.available_expressions.clear()
            self.available_motion_groups.clear()
            
            # 尝试加载新模型
            self.model_config_path = new_model_path
            success = self._load_model()
            
            if success and self.model:
                # 新模型加载成功
                print(f"✓ 模型切换成功: {new_model_path}")
                
                # 恢复之前的状态
                self.current_scale = old_scale
                self.move(old_position)
                
                # 重新解析模型配置
                self._parse_model_config()
                
                # 发送模型加载成功信号
                self.model_loaded_signal.emit(True)
                return True
            else:
                # 新模型加载失败，尝试恢复原模型
                print(f"✗ 模型加载失败: {new_model_path}")
                print("正在恢复原模型...")
                
                # 恢复原模型路径
                self.model_config_path = old_model_path
                self.available_expressions = old_expressions
                self.available_motion_groups = old_motion_groups
                
                # 尝试重新加载原模型
                if old_model_path and os.path.exists(old_model_path):
                    recovery_success = self._load_model()
                    if recovery_success and self.model:
                        print(f"✓ 已恢复到原模型: {old_model_path}")
                        self.current_scale = old_scale
                        self.move(old_position)
                        self._parse_model_config()
                        # 不发送信号，因为这只是恢复操作
                        return False
                    else:
                        print("✗ 恢复原模型也失败了")
                        # 尝试强制重新初始化
                        self._force_reinitialize_model(old_model_path)
                else:
                    print("⚠️ 原模型路径无效，无法恢复")
                
                self.model_loaded_signal.emit(False)
                return False
                
        except Exception as e:
            print(f"切换模型时发生错误: {e}")
            import traceback
            traceback.print_exc()
            self.model_loaded_signal.emit(False)
            return False

    def _load_model(self) -> bool:
        """加载Live2D模型（内部方法）"""
        try:
            if not LIVE2D_AVAILABLE:
                print("Live2D库不可用")
                return False
            
            if not os.path.exists(self.model_config_path):
                print(f"模型文件不存在: {self.model_config_path}")
                return False
            
            print(f"DEBUG: 开始加载模型: {self.model_config_path}")
            
            # 确保之前的模型已经完全释放
            if self.model:
                try:
                    if hasattr(self.model, 'Release'):
                        self.model.Release()
                    del self.model
                    self.model = None
                except Exception as e:
                    print(f"清理旧模型时出错: {e}")
                    self.model = None
            
            # 创建新的模型实例
            try:
                self.model = live2d.LAppModel()
                print("DEBUG: LAppModel 实例创建成功")
            except Exception as e:
                print(f"创建 LAppModel 实例失败: {e}")
                self.model = None
                return False
            
            # 加载模型文件
            try:
                print(f"DEBUG: 调用 LoadModelJson: {self.model_config_path}")
                load_result = self.model.LoadModelJson(self.model_config_path)
                print(f"DEBUG: LoadModelJson 返回结果: {load_result} (类型: {type(load_result)})")
                
                # 处理LoadModelJson可能返回None的情况
                # 在某些live2d-py版本中，LoadModelJson可能不返回值或返回None
                if load_result is None:
                    print("DEBUG: LoadModelJson 返回None，尝试验证模型是否真正加载成功...")
                    # 通过检查模型参数数量来验证是否加载成功
                    try:
                        if hasattr(self.model, 'GetParameterCount'):
                            param_count = self.model.GetParameterCount()
                            print(f"DEBUG: 模型参数数量检查: {param_count}")
                            if param_count > 0:
                                print("DEBUG: 模型参数数量检查通过，认为加载成功")
                                load_result = True
                            else:
                                print("DEBUG: 模型参数数量为0，认为加载失败")
                                load_result = False
                        else:
                            print("DEBUG: 无法检查参数数量，尝试其他验证方法...")
                            # 尝试其他验证方法
                            if hasattr(self.model, 'GetModelMatrix'):
                                matrix = self.model.GetModelMatrix()
                                if matrix:
                                    print("DEBUG: 模型矩阵检查通过，认为加载成功")
                                    load_result = True
                                else:
                                    print("DEBUG: 模型矩阵检查失败，认为加载失败")
                                    load_result = False
                            else:
                                print("DEBUG: 无法验证模型状态，默认认为加载失败")
                                load_result = False
                    except Exception as verify_e:
                        print(f"DEBUG: 验证模型状态时出错: {verify_e}")
                        load_result = False
                        
            except Exception as e:
                print(f"调用 LoadModelJson 时发生异常: {e}")
                import traceback
                traceback.print_exc()
                self.model = None
                return False
            
            # 确保load_result是布尔值
            load_result = bool(load_result)
            print(f"DEBUG: 最终load_result: {load_result}")
            
            if load_result:
                print(f"✓ 模型加载成功: {self.model_config_path}")
                
                try:
                    # 验证模型是否真正可用
                    if hasattr(self.model, 'GetParameterCount'):
                        param_count = self.model.GetParameterCount()
                        print(f"DEBUG: 模型参数数量: {param_count}")
                    
                    # 设置模型位置和大小
                    try:
                        # 首先调整canvas尺寸
                        if self.canvas:
                            self.canvas.ResizeWindow(self.width(), self.height())
                            print("DEBUG: Canvas尺寸调整完成")
                        
                        # 设置模型矩阵
                        if hasattr(self.model, 'GetModelMatrix'):
                            matrix = self.model.GetModelMatrix()
                            if matrix:
                                # 重置矩阵到默认状态
                                matrix.LoadIdentity()
                                
                                # 计算合适的缩放比例以保持宽高比
                                window_width = self.width()
                                window_height = self.height()
                                
                                # 设置合适的缩放，确保模型完全显示且保持比例
                                scale_factor = min(window_width / 512.0, window_height / 512.0) * 1.8
                                matrix.Scale(scale_factor, scale_factor)
                                
                                # 居中显示
                                matrix.TranslateX(0.0)
                                matrix.TranslateY(-0.1)  # 稍微向上偏移
                                
                                print(f"DEBUG: 模型矩阵设置完成 - 窗口尺寸: {window_width}x{window_height}, 缩放: {scale_factor}")
                        
                        # 调整模型到窗口尺寸
                        if hasattr(self.model, 'Resize'):
                            self.model.Resize(self.width(), self.height())
                            print("DEBUG: 模型Resize调用完成")
                    except Exception as matrix_e:
                        print(f"设置模型矩阵时出错: {matrix_e}")
                    
                    # 强制更新模型一次以确保其处于正确状态
                    if hasattr(self.model, 'Update'):
                        self.model.Update()
                        print("DEBUG: 模型初始更新完成")
                    
                    return True
                    
                except Exception as e:
                    print(f"设置模型属性时出错: {e}")
                    # 即使设置失败，如果模型加载成功，仍然返回True
                    return True
            else:
                print(f"✗ 模型加载失败: {self.model_config_path}")
                if self.model:
                    try:
                        if hasattr(self.model, 'Release'):
                            self.model.Release()
                        del self.model
                    except:
                        pass
                    self.model = None
                return False
                
        except Exception as e:
            print(f"加载模型时发生错误: {e}")
            import traceback
            traceback.print_exc()
            if self.model:
                try:
                    if hasattr(self.model, 'Release'):
                        self.model.Release()
                    del self.model
                except:
                    pass
                self.model = None
            return False
    def _force_reinitialize_model(self, model_path: str) -> bool:
        """强制重新初始化模型（用于严重的加载失败情况）"""
        try:
            print("DEBUG: 开始强制重新初始化模型系统...")
            
            # 停止所有定时器
            if hasattr(self, 'main_update_timer'):
                self.main_update_timer.stop()
            
            # 完全清理当前状态
            self.model = None
            self.available_expressions.clear()
            self.available_motion_groups.clear()
            
            # 尝试重新初始化live2d系统
            try:
                if hasattr(live2d, 'dispose'):
                    live2d.dispose()
                    print("DEBUG: live2d.dispose() 调用完成")
            except Exception as e:
                print(f"DEBUG: live2d.dispose() 失败: {e}")
            
            # 重新初始化
            try:
                live2d.init()
                print("DEBUG: live2d.init() 重新调用完成")
                
                # 重新设置日志级别
                if hasattr(live2d, 'setLogEnable'):
                    live2d.setLogEnable(False)
                
                # 重新初始化GL
                if hasattr(live2d, 'glInit'):
                    live2d.glInit()
                elif hasattr(live2d, 'glewInit'):
                    live2d.glewInit()
                    
                print("DEBUG: GL重新初始化完成")
            except Exception as e:
                print(f"DEBUG: 重新初始化live2d失败: {e}")
                return False
            
            # 尝试加载模型
            self.model_config_path = model_path
            success = self._load_model()
            
            if success and self.model:
                # 重新启动定时器
                if hasattr(self, 'main_update_timer'):
                    self.main_update_timer.start(self.timer_interval_ms)
                    print("DEBUG: 主更新定时器重新启动")
                
                # 重新解析模型配置
                self._parse_model_config()
                print(f"✓ 强制重新初始化成功: {model_path}")
                return True
            else:
                print(f"✗ 强制重新初始化仍然失败: {model_path}")
                return False
                
        except Exception as e:
            print(f"强制重新初始化过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def check_model_health(self) -> bool:
        """检查模型健康状态"""
        try:
            if not LIVE2D_AVAILABLE:
                print("HEALTH CHECK: Live2D库不可用")
                return False
            
            if not self.model:
                print("HEALTH CHECK: 模型未加载")
                return False
            
            # 检查模型基本功能
            try:
                if hasattr(self.model, 'GetParameterCount'):
                    param_count = self.model.GetParameterCount()
                    if param_count <= 0:
                        print("HEALTH CHECK: 模型参数数量异常")
                        return False
                else:
                    print("HEALTH CHECK: 模型缺少GetParameterCount方法")
                    return False
                
                # 尝试更新模型
                if hasattr(self.model, 'Update'):
                    self.model.Update()
                else:
                    print("HEALTH CHECK: 模型缺少Update方法")
                    return False
                
                print("HEALTH CHECK: 模型状态正常")
                return True
                
            except Exception as e:
                print(f"HEALTH CHECK: 模型功能测试失败: {e}")
                return False
                
        except Exception as e:
            print(f"HEALTH CHECK: 健康检查过程出错: {e}")
            return False

    def debug_lipsync_status(self):
        """调试输出口型同步状态"""
        print("\n=== 口型同步调试信息 ===")
        print(f"Live2D可用: {LIVE2D_AVAILABLE}")
        print(f"模型已加载: {self.model is not None}")
        print(f"口型同步激活: {self.is_lipsync_active}")
        print(f"WavHandler可用: {self.wav_handler is not None}")
        print(f"音频文件: {self.lipsync_audio_path}")
        print(f"嘴部参数ID: {self.mouth_open_param_id}")
        print(f"敏感度: {self.lipsync_sensitivity}")
        
        if LIVE2D_AVAILABLE and hasattr(live2d, 'utils'): # 检查 live2d.utils 是否存在
            print(f"WavHandler支持: {hasattr(live2d.utils, 'WavHandler')}")
        else:
            print(f"WavHandler支持: False (live2d.utils 不可用)")

        if self.wav_handler:
            try:
                rms = self.wav_handler.GetRms()
                print(f"当前RMS值: {rms}")
            except AttributeError: # WavHandler实例可能没有GetRms方法，或者在Stop后调用会出错
                 print("无法获取RMS值 (WavHandler可能已停止或不支持)")
            except Exception as e_rms:
                print(f"获取RMS值时出错: {e_rms}")
        
        print("========================\n")

    def create_test_lipsync_audio(self) -> Optional[str]:
        """
        创建一个测试用的音频文件（如果系统支持）
        
        Returns:
            测试音频文件路径，失败时返回None
        """
        try:
            import numpy as np
            import wave
            import tempfile
            
            # 生成一个简单的测试音频（正弦波）
            sample_rate = 22050
            duration = 3.0  # 3秒
            frequency = 440  # A4音符
            
            t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False) # endpoint=False for linspace
            
            # 创建一个包含不同强度的音频信号
            audio_signal_float = np.sin(2 * np.pi * frequency * t)
            
            # 添加音量变化以测试口型同步
            envelope = np.sin(2 * np.pi * 0.5 * t) * 0.5 + 0.5  # 0.5Hz的音量包络
            audio_signal_float *= envelope
            
            # 转换为16位整数
            audio_signal_int16 = (audio_signal_float * 32767).astype(np.int16)
            
            # 创建临时WAV文件
            # 使用 with 来确保文件最终关闭，即使发生错误
            temp_file_obj = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            temp_path = temp_file_obj.name
            temp_file_obj.close() # 关闭由NamedTemporaryFile打开的文件句柄，以便wave.open可以打开它
              # 写入WAV文件
            with wave.open(temp_path, 'w') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_signal_int16.tobytes())
            
            print(f"✓ 创建测试音频文件: {temp_path}")
            return temp_path
            
        except ImportError:
            print("警告: 缺少numpy或wave模块，无法创建测试音频")
            return None
        except Exception as e:
            print(f"创建测试音频文件失败: {e}")
            return None

    def showEvent(self, event):
        """重写showEvent,在窗口首次显示时尝试应用Windows样式"""
        super().showEvent(event)
        if sys.platform == "win32" and not hasattr(self, '_non_activating_style_applied'):
            self._setNonActivatingStyleOnWindows()
            self._non_activating_style_applied = True
    
    def initializeGL(self):
        """初始化OpenGL上下文和资源 (由PyQt自动调用)"""
        # QOpenGLFormat 相关代码已移除
        
        if not LIVE2D_AVAILABLE:
            print("Live2D库不可用,无法初始化OpenGL资源。")
            self.model_loaded_signal.emit(False)
            return
        try:
            print("初始化 live2d-py...")
            live2d.init()
            
            # 更彻底地禁用 Live2D 的详细日志输出，减少 "Unknown property" 等警告信息
            try:
                # 尝试使用更严格的日志级别控制
                if hasattr(live2d, 'setLogLevel'):
                    # 如果支持setLogLevel，尝试设置为最低级别
                    if hasattr(live2d, 'LogLevel_Off'):
                        live2d.setLogLevel(live2d.LogLevel_Off)
                        print("Live2D 日志已设置为 LogLevel_Off")
                    elif hasattr(live2d, 'LOG_LEVEL_OFF'):
                        live2d.setLogLevel(live2d.LOG_LEVEL_OFF)
                        print("Live2D 日志已设置为 LOG_LEVEL_OFF")
                    elif hasattr(live2d, 'LogLevel'):
                        # 尝试使用枚举值
                        try:
                            live2d.setLogLevel(0)  # 通常0表示关闭日志
                            print("Live2D 日志级别已设置为 0 (关闭)")
                        except:
                            live2d.setLogEnable(False)
                            print("Live2D 日志已通过 setLogEnable(False) 禁用")
                    else:
                        live2d.setLogEnable(False)
                        print("Live2D 日志已通过 setLogEnable(False) 禁用")
                elif hasattr(live2d, 'setLogEnable'):
                    live2d.setLogEnable(False)
                    print("Live2D 日志已通过 setLogEnable(False) 禁用")
                
                # 额外尝试：如果有全局日志控制函数
                if hasattr(live2d, 'setVerbose'):
                    live2d.setVerbose(False)
                    print("Live2D verbose模式已禁用")
                
                if hasattr(live2d, 'disableLog'):
                    live2d.disableLog()
                    print("Live2D 日志已通过 disableLog() 禁用")
                    
            except AttributeError as e:
                print(f"部分Live2D日志控制功能不可用: {e}")
                # 回退到基本方法
                try:
                    live2d.setLogEnable(False)
                    print("Live2D 日志已通过基本方法禁用")
                except:
                    print("警告: 无法禁用Live2D日志，可能会看到一些警告信息")
            except Exception as e:
                print(f"设置Live2D日志级别时出错: {e}")
                print("这些警告通常是无害的，不会影响程序功能")
            
            print("初始化 gl...")
            if hasattr(live2d, 'glInit'):
                live2d.glInit()
            elif hasattr(live2d, 'glewInit'):
                live2d.glewInit()
            else:
                print("警告: live2d.glInit() 和 live2d.glewInit() 均未找到。")
            print("live2d-py 初始化完成。")

            print(f"尝试加载模型: {self.model_config_path}")
            self.model = live2d.LAppModel()
            
            if not os.path.isabs(self.model_config_path):
                pass

            self.model.LoadModelJson(self.model_config_path)
            print(f"模型 {self.model_config_path} 加载成功。")
            
            # 解析模型配置文件以获取表情和动作信息
            self._parse_model_config()

            self.model_loaded_signal.emit(True)

            # 调整模型视口以匹配窗口大小
            self.model.Resize(self.width(), self.height())

            # 启动主更新定时器
            self.main_update_timer.start(self.timer_interval_ms)
            print(f"主更新定时器已启动,间隔: {self.timer_interval_ms}ms")

        except Exception as e:
            print(f"Live2D 初始化或模型加载失败: {e}")
            import traceback
            traceback.print_exc()
            self.model_loaded_signal.emit(False)
            self.model = None

    def paintGL(self):
        """绘制OpenGL场景 (由PyQt自动调用)"""
        if not self.model or not LIVE2D_AVAILABLE:
            painter = QPainter(self)
            painter.fillRect(self.rect(), QColor(0,0,0,0))
            painter.end()
            return

        try:
            # 更新口型同步（在模型更新之前）
            self.update_lipsync()
            
            # 更新模型状态 (动画、物理等)
            current_time = time.time() # Placed before model.Update
            delta_time = current_time - self._last_frame_time
            self._last_frame_time = current_time

            if hasattr(self.model, 'Update'):
                try:
                    # 尝试传入delta_time，如果不支持则使用无参数版本
                    self.model.Update(delta_time)
                except TypeError:
                    self.model.Update() # Fallback for older live2d-py or if Update(delta_time) is not standard
            
            # 清除OpenGL缓冲区
            if GL_AVAILABLE:
                try:
                    glEnable(GL_BLEND) # Ensure blending is enabled
                    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA) # Standard alpha blending
                    glClearColor(0.0, 0.0, 0.0, 0.0)
                    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
                except Exception as gl_error:
                    print(f"手动清除OpenGL缓冲区时出错: {gl_error}")
            
            # 绘制模型
            self.model.Draw()

            # 渲染后处理效果
            if self.glow_intensity > 0.01:
                self._render_glow_effect()
            
        except Exception as e:
            print(f"绘制 Live2D 模型时出错: {e}")

    def resizeGL(self, width: int, height: int):
        """当窗口大小改变时调用 (由PyQt自动调用)"""
        if self.model and LIVE2D_AVAILABLE:
            print(f"窗口大小调整为: {width}x{height}")
            self.model.Resize(width, height)

    def _main_update_loop(self):
        """主更新循环，集中处理所有更新以提高性能"""
        if not self.model or not LIVE2D_AVAILABLE:
            return
        
        try:
            self._update_breathing_animation()
            self._update_eye_tracking()
            self._update_glow_effect()
            
            # 触发重绘
            self.update()
        except Exception as e:
            print(f"主更新循环出错: {e}")

    def _update_breathing_animation(self):
        """添加自然的呼吸动画"""
        if not self.model or not LIVE2D_AVAILABLE:
            return
        
        try:
            self.breathing_phase += 0.016 * self.breathing_speed
            breathing_value = math.sin(self.breathing_phase) * 0.5 + 0.5
            
            # 设置呼吸参数（需要根据模型的参数名调整）
            if hasattr(self.model, 'SetParameterValue'):
                # 轻微的身体摆动
                body_sway = math.sin(self.breathing_phase * 0.5) * 0.1
                self.model.SetParameterValue("ParamBodyAngleX", body_sway)
                
                # 呼吸效果
                breath_intensity = math.sin(self.breathing_phase) * 0.2 + 0.8
                self.model.SetParameterValue("ParamBreath", breath_intensity)
                
        except Exception as e:
            # 静默处理，避免频繁打印错误
            pass

    def _update_eye_tracking(self):
        """平滑的视线追踪更新"""
        if not self.model or not LIVE2D_AVAILABLE:
            return
        
        try:
            # 平滑插值
            self.current_eye_x += (self.target_eye_x - self.current_eye_x) * self.eye_tracking_smoothness
            self.current_eye_y += (self.target_eye_y - self.current_eye_y) * self.eye_tracking_smoothness
            
            # 应用到模型（需要根据模型参数名调整）
            if hasattr(self.model, 'SetParameterValue'):
                self.model.SetParameterValue("ParamEyeBallX", self.current_eye_x)
                self.model.SetParameterValue("ParamEyeBallY", self.current_eye_y)
                
                # 增强头部和身体跟随（大幅增加幅度）
                head_x = self.current_eye_x * 30   # 从8增加到30
                head_y = self.current_eye_y * 20   # 从4增加到20
                body_x = self.current_eye_x * 15   # 新增身体跟随
                body_y = self.current_eye_y * 10   # 新增身体跟随
                
                self.model.SetParameterValue("ParamAngleX", head_x)
                self.model.SetParameterValue("ParamAngleY", head_y)
                
                # 添加身体跟随
                self.model.SetParameterValue("ParamBodyAngleX", body_x)
                self.model.SetParameterValue("ParamBodyAngleY", body_y)
                
        except Exception as e:
            # 静默处理参数不存在的情况
            pass

    def _update_glow_effect(self):
        """更新光晕效果"""
        self.glow_intensity += (self.glow_target - self.glow_intensity) * 0.05

    def _render_glow_effect(self):
        """渲染简单的光晕效果"""
        if not GL_AVAILABLE:
            return
        
        try:
            # 这里可以添加简单的后处理光晕效果
            # 由于需要复杂的shader编程，暂时留空
            # 可以通过调整模型参数来实现类似效果
            if hasattr(self.model, 'SetParameterValue'):
                # 通过调整模型的某些参数来模拟光晕效果
                glow_factor = self.glow_intensity * 0.3
                # 这里可以根据具体模型参数调整
                pass
        except Exception as e:
            pass

    def set_emotion_glow(self, emotion_type: str):
        """根据表情设置光晕效果"""
        glow_map = {
            'happy': 0.4,
            'sad': 0.1,
            'angry': 0.6,
            'surprised': 0.5,
            'normal': 0.0,
            'love': 0.7,
            'sleepy': 0.2
        }
        self.glow_target = glow_map.get(emotion_type.lower(), 0.0)

    def is_in_live2d_area(self, local_x: int, local_y: int) -> bool:
        """检测指定坐标是否在Live2D模型的可见区域内"""
        if not self.model or not self.isVisible() or not GL_AVAILABLE:
            return False
        try:
            pixel_ratio = QGuiApplication.primaryScreen().devicePixelRatio() if QGuiApplication.primaryScreen() else 1.0
            gl_x = int(local_x * pixel_ratio)
            gl_y = int((self.height() - local_y) * pixel_ratio)

            if not (0 <= gl_x < self.width() * pixel_ratio and 0 <= gl_y < self.height() * pixel_ratio):
                return False

            alpha_data = glReadPixels(gl_x, gl_y, 1, 1, GL_RGBA, GL_UNSIGNED_BYTE)
            alpha = alpha_data[3]
            return alpha > 10
        except Exception as e:
            return False

    def _set_window_mouse_transparent_win32(self, is_transparent: bool):
        """在Windows上设置窗口的鼠标穿透状态"""
        if sys.platform != "win32":
            return
        try:
            hwnd = int(self.winId())
            if not hwnd:
                return

            GWL_EXSTYLE = -20
            WS_EX_TRANSPARENT = 0x00000020
            
            user32 = ctypes.windll.user32
            current_ex_style = user32.GetWindowLongPtrW(hwnd, GWL_EXSTYLE)
            
            if is_transparent:
                new_ex_style = current_ex_style | WS_EX_TRANSPARENT
            else:
                new_ex_style = current_ex_style & ~WS_EX_TRANSPARENT
            
            if current_ex_style != new_ex_style:
                if user32.SetWindowLongPtrW(hwnd, GWL_EXSTYLE, new_ex_style) == 0 and new_ex_style != 0:
                    pass
        except Exception as e:
            pass

    def _update_mouse_penetration_status(self):
        """更新鼠标穿透状态"""
        if not self.isVisible() or not self.model:
            return

        global_mouse_pos = QCursor.pos()
        local_mouse_pos = self.mapFromGlobal(global_mouse_pos)

        if self.rect().contains(local_mouse_pos):
            if self.is_in_live2d_area(local_mouse_pos.x(), local_mouse_pos.y()):
                self._set_window_mouse_transparent_win32(False)
            else:
                self._set_window_mouse_transparent_win32(True)
        else:
            self._set_window_mouse_transparent_win32(True)
    def mousePressEvent(self, event: QMouseEvent):
        """处理鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.model and LIVE2D_AVAILABLE:
                # 检查是否点击在模型上
                hit_parts = self.model.HitPart(event.pos().x(), event.pos().y(), True)
                if hit_parts:
                    top_part_id = hit_parts[0]
                    self.model_clicked_signal.emit(top_part_id)
                    
                    # 根据点击的部位触发不同反应
                    self._handle_part_interaction(top_part_id)
                    
                    self.is_dragging = True
                    if IS_PYQT6:
                        self.drag_start_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
                    else:
                        self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
                    event.accept()
                    return
                else:
                    self.is_dragging = False
                    event.ignore()
                    return
            else:
                self.is_dragging = False
                event.ignore()
                return
        
        elif event.button() == Qt.MouseButton.RightButton:
            # 新增：右键点击处理
            if self.model and LIVE2D_AVAILABLE:
                # 确保点击在模型可见区域内
                hit_parts = self.model.HitPart(event.pos().x(), event.pos().y(), True)
                if hit_parts:  # 如果确实点击在模型上
                    print("Live2DWidget: Right-click detected on model.")
                    self.stop_tts_playback_signal.emit()
                    event.accept()
                    return
        
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """处理鼠标移动事件"""
        if self.model and LIVE2D_AVAILABLE:
            local_pos = event.pos()
            hit_parts = self.model.HitPart(local_pos.x(), local_pos.y(), False)
            self.is_mouse_over_model_area = bool(hit_parts)
        else:
            self.is_mouse_over_model_area = False

        # 处理窗口拖动
        if self.is_dragging and event.buttons() == Qt.MouseButton.LeftButton:
            if IS_PYQT6:
                new_pos = event.globalPosition().toPoint() - self.drag_start_position
            else:
                new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)
            self.window_dragged_signal.emit(new_pos)
            event.accept()
        elif self.enable_mouse_tracking and self.is_mouse_over_model_area and self.model:
            # 优化的视线追踪
            self._update_mouse_tracking(event.pos())

    def mouseReleaseEvent(self, event: QMouseEvent):
        """处理鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = False
            event.accept()

    def wheelEvent(self, event: QWheelEvent):
        """处理鼠标滚轮事件以缩放模型"""
        if not self.model or not LIVE2D_AVAILABLE:
            event.ignore()
            return

        delta = event.angleDelta().y()
        previous_scale = self.current_scale

        if delta > 0:  # 向上滚动，放大
            zoom_factor = 1.1
            self.current_scale *= zoom_factor
        elif delta < 0:  # 向下滚动，缩小
            zoom_factor = 1 / 1.1
            self.current_scale *= zoom_factor
        else: # 没有滚动
            event.ignore()
            return

        # 限制缩放范围
        self.current_scale = max(self.min_scale, min(self.max_scale, self.current_scale))

        if abs(self.current_scale - previous_scale) < 0.001: # 如果缩放没有实际变化 (已达边界)
            event.ignore()
            return
        
        # print(f"Wheel event: delta={delta}, current_scale={self.current_scale}") # 调试信息

        try:
            if hasattr(self.model, 'SetScale'):
                self.model.SetScale(self.current_scale)
                self.update()
                event.accept()
                # print(f"模型已缩放至: {self.current_scale}") # 调试信息
            else:
                print("警告: self.model 对象没有 SetScale 方法。无法缩放。")
                self.current_scale = previous_scale # 恢复之前的缩放值
                event.ignore()
        except Exception as e:
            print(f"缩放模型时发生错误: {e}")
            self.current_scale = previous_scale # 发生错误时恢复之前的缩放值
            event.ignore()

    def _update_mouse_tracking(self, mouse_pos: QPoint):
        """更新鼠标追踪，计算目标视线位置"""
        # 计算标准化的鼠标位置
        normalized_x = (mouse_pos.x() / self.width()) * 2.0 - 1.0
        normalized_y = -((mouse_pos.y() / self.height()) * 2.0 - 1.0)
        
        # 限制视线范围并应用衰减
        max_range = 0.8
        self.target_eye_x = max(-max_range, min(max_range, normalized_x))
        self.target_eye_y = max(-max_range, min(max_range, normalized_y))

    def _handle_part_interaction(self, part_id: str):
        """处理不同部位的交互反应"""
        interaction_map = {
            'head': lambda: self._trigger_head_interaction(),
            'body': lambda: self._trigger_body_interaction(),
            'face': lambda: self._trigger_face_interaction(),
            'hair': lambda: self._trigger_hair_interaction(),
        }
        
        # 尝试匹配部位ID并触发相应反应
        for key, action in interaction_map.items():
            if key.lower() in part_id.lower():
                action()
                break
        else:
            # 默认反应
            self._trigger_default_interaction()

    def _trigger_head_interaction(self):
        """头部交互反应"""
        if self.available_expressions:
            import random
            happy_expressions = [expr for expr in self.available_expressions if 'happy' in expr.lower() or 'smile' in expr.lower()]
            if happy_expressions:
                self.set_expression(random.choice(happy_expressions))
            else:
                self.set_random_expression()
        self.set_emotion_glow('happy')

    def _trigger_face_interaction(self):
        """脸部交互反应"""
        if self.available_expressions:
            import random
            shy_expressions = [expr for expr in self.available_expressions if 'shy' in expr.lower() or 'blush' in expr.lower()]
            if shy_expressions:
                self.set_expression(random.choice(shy_expressions))
            else:
                self.set_random_expression()
        self.set_emotion_glow('love')

    def _trigger_body_interaction(self):
        """身体交互反应"""
        self._trigger_random_gesture()
        self.set_emotion_glow('surprised')

    def _trigger_hair_interaction(self):
        """头发交互反应"""
        self._trigger_hair_wave_animation()
        self.set_emotion_glow('normal')

    def _trigger_default_interaction(self):
        """默认交互反应"""
        if self.available_expressions:
            self.set_random_expression()
        self.set_emotion_glow('happy')

    def _trigger_random_idle_action(self):
        """触发随机的空闲动作"""
        if not self.model or not LIVE2D_AVAILABLE:
            return
        
        import random
        
        # 随机选择动作类型
        actions = ['expression', 'gesture', 'blink']
        if self.available_motion_groups:
            actions.append('motion')
        
        action_type = random.choice(actions)
        
        if action_type == 'expression' and self.available_expressions:
            expression = random.choice(self.available_expressions)
            self.set_expression(expression)
        elif action_type == 'motion' and self.available_motion_groups:
            group_name = random.choice(list(self.available_motion_groups.keys()))
            motion_count = self.available_motion_groups[group_name]
            motion_index = random.randint(0, motion_count - 1)
            self.play_motion(group_name, motion_index)
        elif action_type == 'gesture':
            self._trigger_random_gesture()
        elif action_type == 'blink':
            self._trigger_blink_animation()

    def _trigger_random_gesture(self):
        """触发随机手势动画"""
        if not self.model or not hasattr(self.model, 'SetParameterValue'):
            return
        
        import random
        
        gesture_types = ['wave', 'point', 'stretch']
        gesture_type = random.choice(gesture_types)
        
        if gesture_type == 'wave':
            self._animate_wave_gesture()
        elif gesture_type == 'point':
            self._animate_point_gesture()
        elif gesture_type == 'stretch':
            self._animate_stretch_gesture()

    def _animate_wave_gesture(self):
        """挥手手势动画"""
        if not self.model:
            return
            
        start_time = time.time()
        duration = 2.0
        
        def animate():
            current_time = time.time()
            progress = (current_time - start_time) / duration
            
            if progress < 1.0:
                eased_progress = self._ease_in_out_cubic(progress)
                wave_value = math.sin(eased_progress * math.pi * 4) * 0.6
                
                try:
                    if hasattr(self.model, 'SetParameterValue'):
                        self.model.SetParameterValue("ParamArmR", wave_value)
                        self.model.SetParameterValue("ParamHandR", abs(wave_value))
                except:
                    pass
                
                QTimer.singleShot(16, animate)
            else:
                # 重置手臂位置
                try:
                    if hasattr(self.model, 'SetParameterValue'):
                        self.model.SetParameterValue("ParamArmR", 0)
                        self.model.SetParameterValue("ParamHandR", 0)
                except:
                    pass
        
        animate()

    def _animate_point_gesture(self):
        """指向手势动画"""
        if not self.model:
            return
            
        start_time = time.time()
        duration = 1.5
        
        def animate():
            current_time = time.time()
            progress = (current_time - start_time) / duration
            
            if progress < 1.0:
                if progress < 0.5:
                    point_value = self._ease_out_cubic(progress * 2) * 0.8
                else:
                    point_value = 0.8 * (1 - self._ease_in_cubic((progress - 0.5) * 2))
                
                try:
                    if hasattr(self.model, 'SetParameterValue'):
                        self.model.SetParameterValue("ParamArmL", point_value)
                except:
                    pass
                
                QTimer.singleShot(16, animate)
            else:
                try:
                    if hasattr(self.model, 'SetParameterValue'):
                        self.model.SetParameterValue("ParamArmL", 0)
                except:
                    pass
        
        animate()

    def _animate_stretch_gesture(self):
        """伸展手势动画"""
        if not self.model:
            return
            
        start_time = time.time()
        duration = 3.0
        
        def animate():
            current_time = time.time()
            progress = (current_time - start_time) / duration
            
            if progress < 1.0:
                stretch_phase = progress * math.pi * 2
                stretch_value = math.sin(stretch_phase) * 0.3
                
                try:
                    if hasattr(self.model, 'SetParameterValue'):
                        self.model.SetParameterValue("ParamBodyAngleY", stretch_value)
                        self.model.SetParameterValue("ParamArmR", stretch_value * 0.5)
                        self.model.SetParameterValue("ParamArmL", -stretch_value * 0.5)
                except:
                    pass
                
                QTimer.singleShot(16, animate)
            else:
                try:
                    if hasattr(self.model, 'SetParameterValue'):
                        self.model.SetParameterValue("ParamBodyAngleY", 0)
                        self.model.SetParameterValue("ParamArmR", 0)
                        self.model.SetParameterValue("ParamArmL", 0)
                except:
                    pass
        
        animate()

    def _trigger_blink_animation(self):
        """触发眨眼动画"""
        if not self.model or not hasattr(self.model, 'SetParameterValue'):
            return
            
        start_time = time.time()
        duration = 0.3
        
        def animate():
            current_time = time.time()
            progress = (current_time - start_time) / duration
            
            if progress < 1.0:
                if progress < 0.5:
                    blink_value = progress * 2
                else:
                    blink_value = (1 - progress) * 2
                
                try:
                    self.model.SetParameterValue("ParamEyeLOpen", 1 - blink_value)
                    self.model.SetParameterValue("ParamEyeROpen", 1 - blink_value)
                except:
                    pass
                
                QTimer.singleShot(16, animate)
            else:
                try:
                    self.model.SetParameterValue("ParamEyeLOpen", 1)
                    self.model.SetParameterValue("ParamEyeROpen", 1)
                except:
                    pass
        
        animate()

    def _trigger_hair_wave_animation(self):
        """触发头发飘动动画"""
        if not self.model or not hasattr(self.model, 'SetParameterValue'):
            return
            
        start_time = time.time()
        duration = 2.5
        
        def animate():
            current_time = time.time()
            progress = (current_time - start_time) / duration
            
            if progress < 1.0:
                wave_phase = progress * math.pi * 3
                hair_wave = math.sin(wave_phase) * 0.4 * (1 - progress * 0.5)
                
                try:
                    self.model.SetParameterValue("ParamHairFront", hair_wave)
                    self.model.SetParameterValue("ParamHairSide", hair_wave * 0.7)
                    self.model.SetParameterValue("ParamHairBack", hair_wave * 0.5)
                except:
                    pass
                
                QTimer.singleShot(16, animate)
            else:
                try:
                    self.model.SetParameterValue("ParamHairFront", 0)
                    self.model.SetParameterValue("ParamHairSide", 0)
                    self.model.SetParameterValue("ParamHairBack", 0)
                except:
                    pass
        
        animate()

    def _ease_in_out_cubic(self, t):
        """三次贝塞尔缓动函数"""
        return 4 * t * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 3) / 2

    def _ease_out_cubic(self, t):
        """三次缓出函数"""
        return 1 - pow(1 - t, 3)

    def _ease_in_cubic(self, t):
        """三次缓入函数"""
        return t * t * t

    def nativeEvent(self, eventType, message):
        """处理特定平台的本地窗口事件,主要用于Windows的WM_NCHITTEST"""
        if sys.platform == "win32":
            try:
                event_type_str = eventType.decode('utf-8') if isinstance(eventType, bytes) else eventType
                
                if event_type_str == "windows_generic_MSG" or eventType == b"windows_generic_MSG":
                    try:
                        msg_ptr = int(message)
                    except TypeError:
                        return super().nativeEvent(eventType, message)

                    if msg_ptr == 0:
                        return super().nativeEvent(eventType, message)
                    
                    msg = ctypes.cast(msg_ptr, ctypes.POINTER(wintypes.MSG)).contents
                    
                    if msg.message == 0x0084:  # WM_NCHITTEST
                        return True, 1  # HTCLIENT
            except Exception as e:
                print(f"Error in nativeEvent: {e}")
        
        return super().nativeEvent(eventType, message)
    def _parse_model_config(self):
        """解析模型的 .model3.json 文件以提取表情和动作列表"""
        if not self.model_config_path or not os.path.exists(self.model_config_path):
            print(f"错误: 无法找到模型配置文件路径: {self.model_config_path}")
            return

        try:
            with open(self.model_config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 提取表情
            if "FileReferences" in config_data and "Expressions" in config_data["FileReferences"]:
                expressions = config_data["FileReferences"]["Expressions"]
                if isinstance(expressions, list):
                    self.available_expressions = [expr.get("Name") for expr in expressions if expr.get("Name")]
                    print(f"Live2DWidget: 提取到可用表情: {self.available_expressions}")
            
            # 提取动作组
            if "FileReferences" in config_data and "Motions" in config_data["FileReferences"]:
                motion_groups_data = config_data["FileReferences"]["Motions"]
                if isinstance(motion_groups_data, dict):
                    for group_name, motions_in_group in motion_groups_data.items():
                        if isinstance(motions_in_group, list):
                            self.available_motion_groups[group_name] = len(motions_in_group)
                    print(f"Live2DWidget: 提取到可用动作组: {self.available_motion_groups}")

            # 提取物理配置信息（如果存在）
            if "FileReferences" in config_data and "Physics" in config_data["FileReferences"]:
                physics_file = config_data["FileReferences"]["Physics"]
                print(f"Live2DWidget: 检测到物理配置文件: {physics_file}")

        except FileNotFoundError:
            print(f"错误: 模型配置文件未找到: {self.model_config_path}")
        except json.JSONDecodeError:
            print(f"错误: 解析模型配置文件JSON失败: {self.model_config_path}")
        except Exception as e:
            print(f"解析模型配置文件时发生未知错误: {e}")

    def get_available_expressions(self) -> List[str]:
        """返回当前加载模型的所有可用表情的名称列表"""
        return self.available_expressions.copy()

    def get_available_motions(self) -> Dict[str, int]:
        """返回当前加载模型的所有可用动作组及其包含的动作数量"""
        return self.available_motion_groups.copy()

    def get_parameter_ids(self) -> List[str]:
        """返回当前加载模型的所有可控制参数的ID列表"""
        if self.model and LIVE2D_AVAILABLE:
            try:
                count = self.model.GetParameterCount()
                ids = []
                for i in range(count):
                    param = self.model.GetParameter(i)
                    if param and hasattr(param, 'id'):
                        ids.append(param.id)
                    else:
                        print(f"警告: 索引 {i} 处的参数对象无效或没有id属性。")
                return ids
            except Exception as e:
                print(f"获取参数ID列表时出错: {e}")
        return []

    def get_parameter_value(self, parameter_id: str) -> Optional[float]:
        """获取指定参数的当前值"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'GetParameterValue'):
            try:
                return self.model.GetParameterValue(parameter_id)
            except Exception as e:
                print(f"获取参数 '{parameter_id}' 的值时出错: {e}")
        return None

    def set_parameter_value(self, parameter_id: str, value: float) -> bool:
        """设置指定参数的值"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'SetParameterValue'):
            try:
                self.model.SetParameterValue(parameter_id, value)
                return True
            except Exception as e:
                print(f"设置参数 '{parameter_id}' 的值时出错: {e}")
        return False

    def set_expression(self, expression_name: str) -> bool:
        """应用指定的表情"""
        if self.model and LIVE2D_AVAILABLE and expression_name in self.available_expressions:
            try:
                # 设置表情同时更新光晕效果
                emotion_type = self._detect_emotion_type(expression_name)
                self.set_emotion_glow(emotion_type)
                
                # 应用表情
                if hasattr(self.model, 'SetExpression'):
                    self.model.SetExpression(expression_name)
                    print(f"应用表情: {expression_name}")
                    return True
            except Exception as e:
                print(f"应用表情 '{expression_name}' 失败: {e}")
        else:
            print(f"表情 '{expression_name}' 不在可用列表中: {self.available_expressions}")
        return False

    def set_random_expression(self) -> Optional[str]:
        """随机设置一个表情"""
        if not self.available_expressions:
            return None
            
        import random
        expression_name = random.choice(self.available_expressions)
        
        if self.set_expression(expression_name):
            return expression_name
        return None

    def reset_expression(self) -> bool:
        """重置为默认表情"""
        if self.model and LIVE2D_AVAILABLE:
            try:
                # 重置光晕效果
                self.set_emotion_glow('normal')
                
                if hasattr(self.model, 'ResetExpression'):
                    self.model.ResetExpression()
                    print("表情已重置")
                    return True
                elif self.available_expressions:
                    # 如果没有ResetExpression方法，尝试设置一个默认表情
                    default_expressions = [expr for expr in self.available_expressions
                                         if 'normal' in expr.lower() or 'default' in expr.lower()]
                    if default_expressions:
                        return self.set_expression(default_expressions[0])
            except Exception as e:
                print(f"重置表情失败: {e}")
        return False

    def play_motion(self, group_name: str, motion_index: int, priority: int = 0,
                    on_start_callback: Optional[Callable[[str, int], None]] = None,
                    on_finish_callback: Optional[Callable[[], None]] = None) -> bool:
        """播放指定的动作"""
        if self.model and LIVE2D_AVAILABLE and group_name in self.available_motion_groups:
            motion_count = self.available_motion_groups[group_name]
            if 0 <= motion_index < motion_count:
                try:
                    # 确定优先级
                    actual_priority = priority
                    if hasattr(live2d, 'MotionPriority'):
                        if priority == 0 and hasattr(live2d.MotionPriority, 'NORMAL'):
                            actual_priority = live2d.MotionPriority.NORMAL
                        elif priority == 1 and hasattr(live2d.MotionPriority, 'IDLE'):
                            actual_priority = live2d.MotionPriority.IDLE
                        elif priority == 2 and hasattr(live2d.MotionPriority, 'FORCE'):
                            actual_priority = live2d.MotionPriority.FORCE
                    
                    # 播放动作
                    if hasattr(self.model, 'StartMotion'):
                        self.model.StartMotion(group_name, motion_index, actual_priority, 
                                             on_start_callback, on_finish_callback)
                        print(f"播放动作: 组='{group_name}', 索引={motion_index}, 优先级={actual_priority}")
                        return True
                except Exception as e:
                    print(f"播放动作 '{group_name}_{motion_index}' 失败: {e}")
            else:
                print(f"动作索引 {motion_index} 超出范围 [0, {motion_count-1}]")
        else:
            print(f"动作组 '{group_name}' 不在可用列表中: {list(self.available_motion_groups.keys())}")
        return False

    def play_random_motion(self, group_name: Optional[str] = None, priority: int = 0) -> Optional[tuple]:
        """播放随机动作，返回(group_name, motion_index)或None"""
        if not self.available_motion_groups:
            return None
            
        import random
        
        if group_name is None:
            group_name = random.choice(list(self.available_motion_groups.keys()))
        elif group_name not in self.available_motion_groups:
            print(f"指定的动作组 '{group_name}' 不存在")
            return None
        
        motion_count = self.available_motion_groups[group_name]
        motion_index = random.randint(0, motion_count - 1)
        
        if self.play_motion(group_name, motion_index, priority):
            return (group_name, motion_index)
        return None

    def stop_motion(self) -> bool:
        """停止当前播放的动作"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'StopMotion'):
            try:
                self.model.StopMotion()
                print("动作已停止")
                return True
            except Exception as e:
                print(f"停止动作失败: {e}")
        return False

    def _detect_emotion_type(self, expression_name: str) -> str:
        """根据表情名称检测情感类型"""
        expression_lower = expression_name.lower()
        
        emotion_keywords = {
            'happy': ['happy', 'smile', 'joy', 'laugh', 'cheerful', '开心', '笑', '高兴'],
            'sad': ['sad', 'cry', 'tear', 'sorrow', '伤心', '哭', '难过'],
            'angry': ['angry', 'mad', 'rage', 'furious', '生气', '愤怒', '恼火'],
            'surprised': ['surprised', 'shock', 'amazed', 'wonder', '惊讶', '震惊', '吃惊'],
            'love': ['love', 'heart', 'romantic', 'adore', '爱', '喜欢', '爱心'],
            'sleepy': ['sleepy', 'tired', 'yawn', 'drowsy', '困', '累', '打哈欠'],
            'shy': ['shy', 'blush', 'embarrassed', 'bashful', '害羞', '脸红', '不好意思']
        }
        
        for emotion, keywords in emotion_keywords.items():
            if any(keyword in expression_lower for keyword in keywords):
                return emotion
        
        return 'normal'

    def set_auto_eye_blink(self, enabled: bool) -> bool:
        """设置自动眨眼"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'SetAutoEyeBlink'):
            try:
                self.model.SetAutoEyeBlink(enabled)
                print(f"自动眨眼: {'启用' if enabled else '禁用'}")
                return True
            except Exception as e:
                print(f"设置自动眨眼失败: {e}")
        return False

    def set_auto_breathing(self, enabled: bool) -> bool:
        """设置自动呼吸"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'SetAutoBreathing'):
            try:
                self.model.SetAutoBreathing(enabled)
                print(f"自动呼吸: {'启用' if enabled else '禁用'}")
                return True
            except Exception as e:
                print(f"设置自动呼吸失败: {e}")
        return False

    def set_physics_enabled(self, enabled: bool) -> bool:
        """设置物理效果"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'SetPhysicsEnabled'):
            try:
                self.model.SetPhysicsEnabled(enabled)
                print(f"物理效果: {'启用' if enabled else '禁用'}")
                return True
            except Exception as e:
                print(f"设置物理效果失败: {e}")
        return False

    def set_model_opacity(self, opacity: float) -> bool:
        """设置模型不透明度 (0.0-1.0)"""
        if self.model and LIVE2D_AVAILABLE and hasattr(self.model, 'SetOpacity'):
            try:
                opacity = max(0.0, min(1.0, opacity))
                self.model.SetOpacity(opacity)
                print(f"模型不透明度设置为: {opacity}")
                return True
            except Exception as e:
                print(f"设置模型不透明度失败: {e}")
        return False

    def get_model_info(self) -> Dict[str, any]:
        """获取模型信息"""
        info = {
            'model_path': self.model_config_path,
            'expressions': self.available_expressions,
            'motion_groups': self.available_motion_groups,
            'is_loaded': self.model is not None,
            'parameters': self.get_parameter_ids() if self.model else []
        }
        return info

    def save_current_state(self) -> Dict[str, any]:
        """保存当前模型状态"""
        if not self.model or not LIVE2D_AVAILABLE:
            return {}
        
        state = {
            'position': {'x': self.x(), 'y': self.y()},
            'size': {'width': self.width(), 'height': self.height()},
            'parameters': {},
            'timestamp': time.time()
        }
        
        # 保存参数值
        try:
            for param_id in self.get_parameter_ids():
                value = self.get_parameter_value(param_id)
                if value is not None:
                    state['parameters'][param_id] = value
        except Exception as e:
            print(f"保存模型状态时出错: {e}")
        
        return state

    def load_state(self, state: Dict[str, any]) -> bool:
        """加载模型状态"""
        if not self.model or not LIVE2D_AVAILABLE or not state:
            return False
        
        try:
            # 恢复位置和大小
            if 'position' in state:
                pos = state['position']
                self.move(pos['x'], pos['y'])
            
            if 'size' in state:
                size = state['size']
                self.resize(size['width'], size['height'])
            
            # 恢复参数值
            if 'parameters' in state:
                for param_id, value in state['parameters'].items():
                    self.set_parameter_value(param_id, value)
            
            print("模型状态加载成功")
            return True
        except Exception as e:
            print(f"加载模型状态时出错: {e}")
            return False
    def set_eye_tracking_enabled(self, enabled: bool):
        """设置视线追踪开关"""
        self.enable_mouse_tracking = enabled
        print(f"视线追踪: {'启用' if enabled else '禁用'}")

    def set_eye_tracking_smoothness(self, smoothness: float):
        """设置视线追踪平滑度 (0.01-1.0)"""
        self.eye_tracking_smoothness = max(0.01, min(1.0, smoothness))
        print(f"视线追踪平滑度设置为: {self.eye_tracking_smoothness}")

    def set_breathing_speed(self, speed: float):
        """设置呼吸动画速度"""
        self.breathing_speed = max(0.1, min(5.0, speed))
        print(f"呼吸动画速度设置为: {self.breathing_speed}")

    def set_idle_timer_interval(self, interval_seconds: int):
        """设置空闲动作触发间隔"""
        if interval_seconds > 0:
            self.idle_timer.stop()
            self.idle_timer.start(interval_seconds * 1000)
            print(f"空闲动作间隔设置为: {interval_seconds}秒")

    def reset_to_default_pose(self):
        """重置到默认姿势"""
        if not self.model or not LIVE2D_AVAILABLE:
            return False
        
        try:
            # 重置主要参数到默认值
            default_params = {
                'ParamAngleX': 0.0,
                'ParamAngleY': 0.0,
                'ParamAngleZ': 0.0,
                'ParamEyeBallX': 0.0,
                'ParamEyeBallY': 0.0,
                'ParamBodyAngleX': 0.0,
                'ParamBodyAngleY': 0.0,
                'ParamArmL': 0.0,
                'ParamArmR': 0.0,
                'ParamHandL': 0.0,
                'ParamHandR': 0.0,
                'ParamHairFront': 0.0,
                'ParamHairSide': 0.0,
                'ParamHairBack': 0.0
            }
            
            success_count = 0
            for param_id, value in default_params.items():
                if self.set_parameter_value(param_id, value):
                    success_count += 1
            
            # 重置表情和光晕
            self.reset_expression()
            self.set_emotion_glow('normal')
            
            print(f"重置姿势完成，成功设置 {success_count} 个参数")
            return success_count > 0
        except Exception as e:
            print(f"重置姿势时出错: {e}")
            return False

    def get_performance_info(self) -> Dict[str, any]:
        """获取性能信息"""
        info = {
            'fps': 0,
            'frame_time': 0,
            'memory_usage': 0,
            'gpu_usage': 0,
            'last_update': time.time()
        }
        
        try:
            # 计算帧率
            current_time = time.time()
            if hasattr(self, '_last_frame_time'):
                frame_time = current_time - self._last_frame_time
                if frame_time > 0:
                    info['fps'] = 1.0 / frame_time
                    info['frame_time'] = frame_time * 1000  # 毫秒
        except:
            pass
        
        return info

    def export_model_screenshot(self, file_path: str) -> bool:
        """导出模型截图"""
        try:
            # 创建QPixmap来捕获当前渲染内容
            pixmap = self.grab()
            
            # 保存截图
            success = pixmap.save(file_path)
            if success:
                print(f"截图已保存到: {file_path}")
            else:
                print(f"保存截图失败: {file_path}")
            return success
        except Exception as e:
            print(f"导出截图时出错: {e}")
            return False

    def toggle_debug_mode(self, enabled: bool):
        """切换调试模式"""
        self.debug_mode = enabled
        if enabled:
            print("调试模式已启用")
            # 可以在这里添加调试信息显示
        else:
            print("调试模式已禁用")

    def get_model_bounds(self) -> Dict[str, float]:
        """获取模型边界信息"""
        if not self.model or not LIVE2D_AVAILABLE:
            return {}
        
        try:
            # 这里需要根据live2d-py的具体API调整
            # 假设存在获取模型边界的方法
            bounds = {
                'left': 0,
                'right': self.width(),
                'top': 0,
                'bottom': self.height(),
                'center_x': self.width() / 2,
                'center_y': self.height() / 2
            }
            return bounds
        except Exception as e:
            print(f"获取模型边界时出错: {e}")
            return {}

    def apply_preset_emotion(self, emotion_name: str) -> bool:
        """应用预设情感状态"""
        emotion_presets = {
            'happy': {
                'expression': ['happy', 'smile', 'joy'],
                'glow': 'happy',
                'params': {'ParamMouthForm': 1.0, 'ParamEyeForm': 0.6}
            },
            'sad': {
                'expression': ['sad', 'cry', 'sorrow'],
                'glow': 'sad',
                'params': {'ParamMouthForm': -0.8, 'ParamEyeForm': -0.5}
            },
            'excited': {
                'expression': ['surprised', 'excited', 'amazed'],
                'glow': 'surprised',
                'params': {'ParamMouthForm': 0.8, 'ParamEyeForm': 1.0}
            },
            'sleepy': {
                'expression': ['sleepy', 'tired'],
                'glow': 'sleepy',
                'params': {'ParamEyeLOpen': 0.3, 'ParamEyeROpen': 0.3}
            }
        }
        
        if emotion_name not in emotion_presets:
            print(f"未知的情感预设: {emotion_name}")
            return False
        
        preset = emotion_presets[emotion_name]
        success = False
        
        try:
            # 设置表情
            expressions = preset['expression']
            available_expr = [expr for expr in expressions if expr in self.available_expressions]
            if available_expr:
                self.set_expression(available_expr[0])
                success = True
            
            # 设置光晕
            self.set_emotion_glow(preset['glow'])
            
            # 设置参数
            for param_id, value in preset.get('params', {}).items():
                if self.set_parameter_value(param_id, value):
                    success = True
            
            if success:
                print(f"应用情感预设: {emotion_name}")
            
        except Exception as e:
            print(f"应用情感预设时出错: {e}")
        
        return success

    def start_auto_interaction_mode(self):
        """启动自动交互模式"""
        if hasattr(self, 'auto_interaction_timer'):
            self.auto_interaction_timer.stop()
        
        self.auto_interaction_timer = QTimer(self)
        self.auto_interaction_timer.timeout.connect(self._auto_interaction_tick)
        self.auto_interaction_timer.start(30000)  # 每30秒一次自动交互
        print("自动交互模式已启动")

    def stop_auto_interaction_mode(self):
        """停止自动交互模式"""
        if hasattr(self, 'auto_interaction_timer'):
            self.auto_interaction_timer.stop()
            print("自动交互模式已停止")

    def _auto_interaction_tick(self):
        """自动交互周期性触发"""
        import random
        
        actions = [
            lambda: self.apply_preset_emotion('happy'),
            lambda: self.play_random_motion(),
            lambda: self._trigger_random_gesture(),
            lambda: self.set_random_expression(),
        ]
        
        action = random.choice(actions)
        action()

    def cleanup(self):
        """清理 Live2DWidget 资源"""
        print("清理 Live2DWidget...")
        
# 停止口型同步
        self.stop_lipsync()
        # 停止口型同步
        self.stop_lipsync()
        
        try:
            # 停止所有定时器
            if hasattr(self, 'main_update_timer') and self.main_update_timer:
                self.main_update_timer.stop()
            
            if hasattr(self, 'mouse_penetration_timer') and self.mouse_penetration_timer:
                self.mouse_penetration_timer.stop()
            
            if hasattr(self, 'idle_timer') and self.idle_timer:
                self.idle_timer.stop()
            
            if hasattr(self, 'auto_interaction_timer'): # Check if auto_interaction_timer exists
                self.auto_interaction_timer.stop()
            
            # 重置模型状态
            if self.model and LIVE2D_AVAILABLE:
                try:
                    self.reset_to_default_pose()
                    self.reset_expression()
                except: # Broad except to catch any error during reset
                    pass
            
            # 清理OpenGL资源
            if GL_AVAILABLE:
                try:
                    self.makeCurrent()  # 确保当前上下文
                    # OpenGL资源清理通常由驱动程序自动处理
                except: # Broad except for makeCurrent or other GL issues
                    pass
            
            print("Live2DWidget 清理完毕。")
            
        except Exception as e:
            print(f"清理过程中出现错误: {e}")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass

# 主程序测试入口
if __name__ == '__main__':
    if not LIVE2D_AVAILABLE:
        print("无法运行测试,因为 live2d-py 库不可用。")
        sys.exit(1)

    app = QApplication(sys.argv)

    # 设置模型路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    model_path = "linghu/芊芊.model3.json"
    
    # 尝试多个可能的模型路径
    possible_paths = [
        model_path,
        os.path.join("models", "linghu", "芊芊.model3.json"),
        os.path.join("..", "linghu", "芊芊.model3.json"),
        os.path.join(script_dir, "..", "linghu", "芊芊.model3.json")
    ]
    
    model_found = False
    for path in possible_paths:
        if os.path.exists(path):
            model_path = path
            model_found = True
            break
    
    if not model_found:
        print(f"错误: 找不到模型文件。尝试过的路径:")
        for path in possible_paths:
            print(f"  - {os.path.abspath(path)}")
        print("\n请确保模型文件路径正确，或将模型文件放在正确的位置。")
        sys.exit(1)

    print(f"使用模型路径: {os.path.abspath(model_path)}")

    # 创建Live2D窗口
    widget = Live2DWidget(model_config_path=model_path)
    
    def on_model_loaded(success):
        if success:
            print("✓ 模型加载成功!")
            print(f"✓ 可用表情: {widget.get_available_expressions()}")
            print(f"✓ 可用动作组: {widget.get_available_motions()}")
            
            # 演示功能
            def demo_functions():
                print("\n--- 开始功能演示 ---")
                
                # 等待2秒后开始演示
                QTimer.singleShot(2000, lambda: widget.set_expression(widget.get_available_expressions()[0]) if widget.get_available_expressions() else None)
                QTimer.singleShot(4000, lambda: widget._trigger_random_gesture())
                QTimer.singleShot(6000, lambda: widget.apply_preset_emotion('happy'))
                QTimer.singleShot(8000, lambda: widget.play_random_motion())
                QTimer.singleShot(10000, lambda: widget.start_auto_interaction_mode())
                
                print("功能演示已启动，观察桌宠的各种动作和表情变化")
            
            demo_functions()
            
        else:
            print("✗ 模型加载失败")
            print("请检查模型文件是否存在且格式正确")

    def on_model_clicked(part_id):
        print(f"点击了模型部位: {part_id}")

    def on_window_dragged(pos):
        print(f"窗口被拖动到: ({pos.x()}, {pos.y()})")

    # 连接信号
    widget.model_loaded_signal.connect(on_model_loaded)
    widget.model_clicked_signal.connect(on_model_clicked)
    widget.window_dragged_signal.connect(on_window_dragged)
    
    # 显示窗口
    widget.show()
    
    print("\n=== Live2D 桌宠测试程序 ===")
    print("功能说明:")
    print("- 鼠标悬停: 角色眼睛会跟随鼠标")
    print("- 点击角色: 触发不同的交互反应")
    print("- 拖动角色: 移动窗口位置")
    print("- 自动动画: 角色会自动呼吸和眨眼")
    print("- 随机动作: 每15秒触发一次随机动作")
    print("- 按 Ctrl+C 退出程序")
    print("================================\n")
    
    # 设置优雅退出
    def signal_handler(sig, frame):
        print("\n正在退出...")
        widget.cleanup()
        app.quit()
    
    import signal
    signal.signal(signal.SIGINT, signal_handler)
    
    # 启动应用
    try:
        exit_code = app.exec_()
        
        # 清理资源
        if LIVE2D_AVAILABLE and live2d:
            try:
                live2d.dispose()
                print("live2d-py 资源已释放")
            except:
                pass
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        widget.cleanup()
        sys.exit(0)
    except Exception as e:
        print(f"程序运行时出错: {e}")
        import traceback
        traceback.print_exc()
        widget.cleanup()
        sys.exit(1)