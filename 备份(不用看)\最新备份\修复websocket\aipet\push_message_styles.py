# -*- coding: utf-8 -*-
"""
推送消息样式定义
定义推送消息相关组件的样式和主题
"""

# 基础颜色定义
class Colors:
    """颜色常量定义"""
    
    # 亮色主题
    LIGHT = {
        'background': '#ffffff',
        'surface': '#f8f9fa',
        'surface_hover': '#e9ecef',
        'border': '#dee2e6',
        'border_hover': '#adb5bd',
        'text_primary': '#212529',
        'text_secondary': '#6c757d',
        'text_muted': '#868e96',
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8',
        'button_bg': '#ffffff',
        'button_border': '#ced4da',
        'button_hover': '#e9ecef',
        'button_pressed': '#dee2e6',
        'shadow': 'rgba(0, 0, 0, 0.1)'
    }
    
    # 暗色主题
    DARK = {
        'background': '#1a1d23',
        'surface': '#2d3142',
        'surface_hover': '#3d4252',
        'border': '#4f5666',
        'border_hover': '#6c757d',
        'text_primary': '#ffffff',
        'text_secondary': '#adb5bd',
        'text_muted': '#6c757d',
        'success': '#20c997',
        'error': '#e74c3c',
        'warning': '#f39c12',
        'info': '#3498db',
        'button_bg': '#3d4252',
        'button_border': '#4f5666',
        'button_hover': '#4d5262',
        'button_pressed': '#5d6272',
        'shadow': 'rgba(0, 0, 0, 0.3)'
    }


class PushMessageStyles:
    """推送消息样式类"""
    
    @staticmethod
    def get_container_style(is_dark=False):
        """获取折叠容器样式"""
        colors = Colors.DARK if is_dark else Colors.LIGHT
        
        return f"""
        QFrame#push_message_container {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            margin: 4px 0px;
        }}
        
        QFrame#push_message_container:hover {{
            border-color: {colors['border_hover']};
            background-color: {colors['surface_hover']};
        }}
        """
    
    @staticmethod
    def get_header_style(is_dark=False):
        """获取头部样式"""
        colors = Colors.DARK if is_dark else Colors.LIGHT
        
        return f"""
        QWidget#push_header {{
            background-color: transparent;
            padding: 8px;
        }}
        
        QLabel#count_label {{
            color: {colors['text_primary']};
            font-weight: bold;
            font-size: 10pt;
            background-color: {colors['info']};
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
        }}
        
        QLabel#preview_label {{
            color: {colors['text_secondary']};
            font-size: 9pt;
        }}
        
        QLabel#time_label {{
            color: {colors['text_muted']};
            font-size: 8pt;
            font-family: 'Consolas', monospace;
        }}
        """
    
    @staticmethod
    def get_item_style(is_dark=False):
        """获取消息项样式"""
        colors = Colors.DARK if is_dark else Colors.LIGHT
        
        return f"""
        QFrame.push_message_item {{
            background-color: {colors['background']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            margin: 2px 0px;
            padding: 6px;
        }}
        
        QFrame.push_message_item:hover {{
            background-color: {colors['surface_hover']};
            border-color: {colors['border_hover']};
        }}
        
        QLabel.status_indicator {{
            font-size: 12pt;
            margin: 0px 4px;
        }}
        
        QLabel.type_label {{
            color: {colors['text_primary']};
            font-weight: bold;
            font-size: 9pt;
        }}
        
        QLabel.time_label {{
            color: {colors['text_muted']};
            font-size: 8pt;
            font-family: 'Consolas', monospace;
        }}
        
        QLabel.preview_label {{
            color: {colors['text_secondary']};
            font-size: 8pt;
        }}
        
        QLabel.content_label {{
            color: {colors['text_primary']};
            font-size: 9pt;
            line-height: 1.4;
            padding: 4px 0px;
        }}
        """
    
    @staticmethod
    def get_button_style(is_dark=False):
        """获取按钮样式"""
        colors = Colors.DARK if is_dark else Colors.LIGHT
        
        return f"""
        QPushButton {{
            background-color: {colors['button_bg']};
            border: 1px solid {colors['button_border']};
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 8pt;
            color: {colors['text_primary']};
        }}
        
        QPushButton:hover {{
            background-color: {colors['button_hover']};
            border-color: {colors['border_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {colors['button_pressed']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['surface']};
            color: {colors['text_muted']};
            border-color: {colors['border']};
        }}
        
        QPushButton#toggle_button {{
            font-weight: bold;
            font-size: 10pt;
            min-width: 20px;
            min-height: 20px;
            max-width: 20px;
            max-height: 20px;
        }}
        
        QPushButton#clear_button {{
            background-color: {colors['error']};
            color: white;
            border-color: {colors['error']};
        }}
        
        QPushButton#clear_button:hover {{
            background-color: #c82333;
        }}
        
        QPushButton#copy_button {{
            background-color: {colors['info']};
            color: white;
            border-color: {colors['info']};
        }}
        
        QPushButton#copy_button:hover {{
            background-color: #138496;
        }}
        """
    
    @staticmethod
    def get_animation_style():
        """获取动画相关样式"""
        return """
        QFrame.expanding {
            border: 2px solid #007bff;
        }
        
        QFrame.collapsing {
            border: 1px solid #6c757d;
        }
        """
    
    @staticmethod
    def get_status_styles():
        """获取状态相关样式"""
        return {
            'success': {
                'border_color': '#28a745',
                'bg_color': '#d4edda',
                'text_color': '#155724'
            },
            'error': {
                'border_color': '#dc3545',
                'bg_color': '#f8d7da',
                'text_color': '#721c24'
            },
            'warning': {
                'border_color': '#ffc107',
                'bg_color': '#fff3cd',
                'text_color': '#856404'
            },
            'info': {
                'border_color': '#17a2b8',
                'bg_color': '#d1ecf1',
                'text_color': '#0c5460'
            }
        }
    
    @staticmethod
    def get_complete_stylesheet(is_dark=False):
        """获取完整样式表"""
        styles = PushMessageStyles
        
        return (
            styles.get_container_style(is_dark) +
            styles.get_header_style(is_dark) +
            styles.get_item_style(is_dark) +
            styles.get_button_style(is_dark) +
            styles.get_animation_style()
        )


class FontConfig:
    """字体配置"""
    
    # 字体族
    PRIMARY_FONT = "微软雅黑"
    MONOSPACE_FONT = "Consolas"
    
    # 字体大小
    SIZES = {
        'small': 8,
        'normal': 9,
        'medium': 10,
        'large': 11,
        'title': 12
    }
    
    # 字体权重
    WEIGHTS = {
        'normal': 400,
        'medium': 500,
        'bold': 700
    }


class LayoutConfig:
    """布局配置"""
    
    # 间距配置
    SPACING = {
        'tiny': 2,
        'small': 4,
        'normal': 6,
        'medium': 8,
        'large': 12
    }
    
    # 边距配置
    MARGINS = {
        'container': (8, 6, 8, 6),
        'item': (6, 4, 6, 4),
        'content': (4, 4, 4, 4),
        'button': (2, 2, 2, 2)
    }
    
    # 高度配置
    HEIGHTS = {
        'collapsed': 40,
        'button': 24,
        'small_button': 20,
        'separator': 1
    }
    
    # 圆角配置
    RADIUS = {
        'container': 6,
        'item': 4,
        'button': 3,
        'indicator': 10
    }


class AnimationConfig:
    """动画配置"""
    
    # 动画持续时间（毫秒）
    DURATION = {
        'fast': 150,
        'normal': 200,
        'slow': 300
    }
    
    # 缓动曲线
    EASING = {
        'expand': 'OutCubic',
        'collapse': 'InCubic',
        'smooth': 'InOutCubic'
    }