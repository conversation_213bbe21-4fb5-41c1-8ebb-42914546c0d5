import asyncio
import io
import json
import os
import queue
import re
import threading
import time
import traceback
from typing import Optional, List, Dict, Any, Tuple
from urllib.parse import urljoin, quote as url_quote

import edge_tts
import requests
from PyQt5.QtCore import QObject, pyqtSignal, QUrl, QPoint, QSize
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

from aipet.core.resource_manager import TempFileManager
from aipet.tts_config_manager import TTSConfigManager, TTSServiceInfo


class AudioPlaybackController(QObject):
    """
    一个独立的音频播放控制器，封装了QMediaPlayer的逻辑。
    """
    playback_finished_signal = pyqtSignal()

    def __init__(self, tts_manager):
        super().__init__()
        self.tts_manager = tts_manager
        self.player = tts_manager.player

    def play(self, audio_data: bytes):
        """加载并播放音频数据"""
        # 使用 AppController 中已有的方法来处理音频数据和播放
        # 这会隐式地处理口型同步等逻辑
        self.tts_manager._play_audio_data(audio_data)

    def stop(self):
        """停止播放"""
        self.player.stop()

    def is_playing(self) -> bool:
        """检查是否正在播放"""
        # 检查PyQt5和PyQt6的不同API
        if hasattr(self.player, 'playbackState'):  # PyQt6
            try:
                # 尝试导入PyQt6的枚举
                from PyQt6.QtMultimedia import QMediaPlayer as QMediaPlayer_v6
                return self.player.playbackState() == QMediaPlayer_v6.PlaybackState.PlayingState
            except ImportError:
                return False  # 如果PyQt6导入失败，则认为不在播放
        elif hasattr(self.player, 'state'):  # PyQt5
            return self.player.state() == QMediaPlayer.State.PlayingState
        return False


class TTSManager(QObject):
    """
    封装了所有TTS（文本转语音）功能的管理器。
    """
    # --- Signals ---
    tts_play_audio_signal = pyqtSignal(bytes)
    audio_chunk_ready_signal = pyqtSignal()
    speaker_reference_updated_signal = pyqtSignal(str, str) # (audio_path, text)

    def __init__(self, app_controller):
        super().__init__()
        self.app_controller = app_controller
        self.live2d_widget = app_controller.live2d_widget # 从Controller获取Live2D组件的引用
        
        # --- 初始化核心工具 ---
        aipet_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.tts_config_manager = TTSConfigManager(config_base_path=aipet_dir)
        
        # --- 中断驱动的TTS控制 ---
        self.tts_cancellation_event = threading.Event()

        # --- 初始化双队列和工作线程 ---
        self.tts_sentence_queue = queue.Queue()
        self.tts_audio_queue = queue.Queue()
        self.tts_worker_thread = threading.Thread(target=self._tts_worker, daemon=True)
        self.playback_lock = threading.Lock()

        # --- 播放器设置 ---
        self.player = QMediaPlayer()
        self.player.mediaStatusChanged.connect(self._on_media_status_changed)
        self.audio_player = AudioPlaybackController(self)
        if hasattr(self.player, 'errorOccurred'):
            self.player.errorOccurred.connect(self._on_media_error_occurred_pyqt6)
        elif hasattr(self.player, 'error'):
            self.player.error.connect(self._on_media_error_occurred_pyqt5)

        # --- GAG-TTS 相关状态 ---
        self._current_ref_audio_path: Optional[str] = None
        self._current_ref_text: Optional[str] = None
        self._current_gag_model_loaded = {"gpt": None, "sovits": None}
        
        # --- 请求配置 ---
        self.request_timeout = self.tts_config_manager.get_global_settings().get('request_timeout', 30)
        self.max_retries = self.tts_config_manager.get_global_settings().get('max_retries', 3)

        # --- 连接信号 ---
        self._connect_signals()
        
        # --- 启动工作线程 ---
        self.tts_worker_thread.start()

        # --- 初始化 ---
        self._initialize_speaker_references()

    def _connect_signals(self):
        self.tts_play_audio_signal.connect(self._play_audio_data)
        self.audio_chunk_ready_signal.connect(self._audio_playback_manager)
        self.audio_player.playback_finished_signal.connect(self._audio_playback_manager)

    def _initialize_speaker_references(self):
        """初始化当前选用音色的参考音频和文本"""
        current_service_info = self.tts_config_manager.get_current_service_info()
        if current_service_info and current_service_info.type == 'gpt_sovits':
            current_model_info = self.tts_config_manager.get_current_model_for_service(current_service_info.id)
            if current_model_info:
                self._current_ref_audio_path = current_model_info.get("reference_audio", "")
                self._current_ref_text = current_model_info.get("reference_text", "")
                print(f"初始化音色 '{current_model_info['name']}' 的参考设置为: '{self._current_ref_audio_path}', '{self._current_ref_text}'")

    def update_current_speaker_reference(self, audio_path: str, text: str):
        """由UI调用，更新并保存当前音色的参考设置"""
        current_service_info = self.tts_config_manager.get_current_service_info()
        if not (current_service_info and current_service_info.type == 'gpt_sovits'):
            return

        current_model_name = self.tts_config_manager.get_current_speaker_for_service(current_service_info.id)
        if not current_model_name:
            return

        # 更新内部状态
        self._current_ref_audio_path = audio_path
        self._current_ref_text = text

        # 调用配置管理器保存到文件
        self.tts_config_manager.update_gag_model_reference(
            service_id=current_service_info.id,
            model_name=current_model_name,
            ref_audio_path=audio_path,
            ref_text=text
        )

    def queue_text_for_speech(self, text: str):
        """
        接收完整文本，清理、切分后加入TTS流水线。
        这是取代旧有TTS触发逻辑的新入口。
        """
        if not text or not self.get_tts_enabled():
            return

        # --- 开始：中断驱动的TTS逻辑 ---
        # 1. 调用标准的中断流程，确保旧任务完全停止
        #    这会设置取消事件、清空队列并停止播放器。
        self.stop_playback(triggered_by_user_action=False)

        # 2. 清除"停止"信号旗，允许新的TTS任务开始
        self.tts_cancellation_event.clear()
        # --- 结束：中断驱动的TTS逻辑 ---

        # 3. 复用现有的文本清理逻辑
        cleaned_text = self._clean_text_for_tts(text)
        if not cleaned_text:
            return

        # 4. 切分句子 (与原计划相同，但更健壮)
        sentences = re.split(r'([,.?!;。，、？！；\n])', cleaned_text)
        processed_sentences = []
        if len(sentences) > 1:
            # 将句子和紧随其后的分隔符重新组合
            processed_sentences.extend(["".join(i) for i in zip(sentences[0::2], sentences[1::2])])
            # 添加可能存在的最后一句
            if len(sentences) % 2 == 1:
                last_sentence = sentences[-1].strip()
                if last_sentence:
                    processed_sentences.append(last_sentence)
        else:
            processed_sentences = [cleaned_text] if cleaned_text.strip() else []
            
        # 5. 将句子加入队列
        for sentence in processed_sentences:
            sentence = sentence.strip()
            if sentence:
                self.tts_sentence_queue.put(sentence)
        
        # 6. 关键：移除旧的、不稳定的触发器
        # if processed_sentences:
        #     print(f"Queued {len(processed_sentences)} sentences for TTS. Triggering playback manager.")
        #     self._audio_playback_manager()
    
    def _tts_worker(self):
        """
        后台工作线程(生产者)，从文本队列获取句子，合成音频后放入音频队列。
        """
        while True:
            # --- 开始：中断驱动的TTS逻辑 ---
            # 在处理任何新项目之前，检查是否已请求取消
            if self.tts_cancellation_event.is_set():
                # 如果已请求取消，则暂停工作，而不是终止线程
                # 这可以防止线程在一次中断后失效
                time.sleep(0.1) # 短暂休眠以避免CPU空转
                continue
            # --- 结束：中断驱动的TTS逻辑 ---
            
            try:
                # 使用超时来避免在没有任务时永久阻塞，允许定期检查取消事件
                sentence = self.tts_sentence_queue.get(timeout=0.5)
                if sentence is None:  # 哨兵值
                    break

                # 再次检查，以减少合成不必要音频的几率
                if self.tts_cancellation_event.is_set():
                    print("TTS worker: Cancellation detected after getting sentence, discarding.")
                    self.tts_sentence_queue.task_done()
                    break

                audio_data = self._synthesize_speech(sentence)
                if audio_data and not self.tts_cancellation_event.is_set():
                    self.tts_audio_queue.put(audio_data)
                    self.audio_chunk_ready_signal.emit()
                
                self.tts_sentence_queue.task_done()
            except queue.Empty:
                # 队列为空是正常情况，继续循环以响应取消事件
                continue
            except Exception as e:
                print(f"TTS worker error: {e}")
                # 发生错误时也应该完成任务，以防队列阻塞
                if 'sentence' in locals() and self.tts_sentence_queue.unfinished_tasks > 0:
                    self.tts_sentence_queue.task_done()

    def _audio_playback_manager(self):
        """
        音频播放管理器(消费者)，当一句播放完毕或被手动触发时调用。
        它检查音频队列并播放下一句。
        """
        # 使用非阻塞锁来防止中断。如果锁已被占用，则表示有音频正在播放或即将播放。
        if not self.playback_lock.acquire(blocking=False):
            return

        try:
            # --- 开始：中断驱动的TTS逻辑 ---
            if self.tts_cancellation_event.is_set():
                if self.playback_lock.locked():
                    self.playback_lock.release()
                return
            # --- 结束：中断驱动的TTS逻辑 ---
            
            if not self.tts_audio_queue.empty():
                audio_data = self.tts_audio_queue.get_nowait()

                # --- 开始：中断驱动的TTS逻辑 ---
                # 在播放前最后一次检查，确保不会播放一个刚刚被取消的音频片段
                if self.tts_cancellation_event.is_set():
                    print("Playback manager: Cancellation detected before playing audio, discarding.")
                    if self.playback_lock.locked():
                        self.playback_lock.release()
                    self.tts_audio_queue.task_done()
                    return
                # --- 结束：中断驱动的TTS逻辑 ---

                # 成功获取音频，开始播放。锁将在播放结束后由回调函数释放。
                self.audio_player.play(audio_data)
                self.tts_audio_queue.task_done()
            else:
                # 如果队列为空，则无事可做，立即释放锁。
                self.playback_lock.release()
        except queue.Empty:
            # 队列在检查后变空，这很正常，释放锁。
            self.playback_lock.release()
        except Exception as e:
            print(f"Audio playback manager error: {e}")
            # 发生错误，必须释放锁。
            self.playback_lock.release()

    # --- 结束：添加新的TTS核心方法 ---

    def stop_playback(self, triggered_by_user_action: bool = True):
        """
        停止当前的TTS播放和相关的口型同步。
        """
        try:
            print("TTSManager: Attempting to stop TTS playback...")
            self.tts_cancellation_event.set()
            
            while not self.tts_sentence_queue.empty():
                self.tts_sentence_queue.get_nowait()
            while not self.tts_audio_queue.empty():
                self.tts_audio_queue.get_nowait()

            is_actually_playing = self.is_playing()

            if not is_actually_playing:
                if triggered_by_user_action:
                     if self.app_controller.chat_window and self.tts_config_manager.get_global_settings().get("show_stop_confirmation", False):
                        self.app_controller.chat_window.display_error_message("当前没有语音在播放。", is_critical=False)
                return

            self.player.stop()
            self.player.setMedia(QMediaContent())
            
            self._stop_lipsync()
            
            if triggered_by_user_action:
                 if self.app_controller.chat_window and self.tts_config_manager.get_global_settings().get("show_stop_confirmation", False):
                     self.app_controller.chat_window.display_error_message("语音播放已停止。", is_critical=False)
                
        except Exception as e:
            print(f"TTSManager: Error occurred while stopping TTS playback: {e}")
            traceback.print_exc()

    def is_playing(self) -> bool:
        """检查TTS是否正在播放"""
        return self.audio_player.is_playing()
        
    def _clean_text_for_tts(self, text: str) -> str:
        """Cleans text for TTS by removing DailyNote blocks, image tags, and Markdown syntax."""
        if not text:
            return ""
        
        cleaned_text = text

        # 1. Remove Tool blocks - 支持多种格式
        # 格式1: 代码块包裹的工具调用
        # ```
        # <<<[TOOL_REQUEST]>>>
        # ...
        # <<<[END_TOOL_REQUEST]>>>
        # ```
        tool_block_pattern = r"```[\s\S]*?<<<\[TOOL_REQUEST\]>>>[\s\S]*?<<<\[END_TOOL_REQUEST\]>>>[\s\S]*?```"
        cleaned_text = re.sub(tool_block_pattern, "", cleaned_text, flags=re.DOTALL).strip()
        
        # 格式2: 纯文本形式的工具调用
        # <<<[TOOL_REQUEST]>>>
        # ...
        # <<<[END_TOOL_REQUEST]>>>
        start_marker_tool = "<<<[TOOL_REQUEST]>>>"
        end_marker_tool = "<<<[END_TOOL_REQUEST]>>>"
        start_index = cleaned_text.find(start_marker_tool)
        if start_index != -1:
            end_index = cleaned_text.find(end_marker_tool, start_index + len(start_marker_tool))
            if end_index != -1:
                # Found both markers, reconstruct the string
                text_before = cleaned_text[:start_index]
                text_after = cleaned_text[end_index + len(end_marker_tool):]
                cleaned_text = (text_before + text_after).strip()
                # print("DEBUG: Removed tool block via string splitting.") # Optional debug print
        
        # 2. Remove DailyNote blocks - 改进版本，支持多种格式
        # 格式1: <<<DailyNoteStart>>> ... <<<DailyNoteEnd>>>
        cleaned_text = re.sub(r"<<<DailyNoteStart>>>[\s\S]*?<<<DailyNoteEnd>>>", "", cleaned_text, flags=re.DOTALL).strip()
        
        # 格式2: ```DailyNote ... <<<DailyNoteEnd>>> ```（原有格式的兼容）
        cleaned_text = re.sub(r"```\s*DailyNote[\s\S]*?<<<DailyNoteEnd>>>\s*```", "", cleaned_text, flags=re.DOTALL).strip()
        
        # 格式3: \n<<<DailyNoteStart>>> ... <<<DailyNoteEnd>>>\n（处理换行符）
        cleaned_text = re.sub(r"\n<<<DailyNoteStart>>>[\s\S]*?<<<DailyNoteEnd>>>\n?", "", cleaned_text, flags=re.DOTALL).strip()

        # 3. Remove HTML img tags
        # This regex finds <img ... > tags. It's non-greedy for attributes.
        img_tag_pattern = r"<img[^>]*>"
        cleaned_text = re.sub(img_tag_pattern, "", cleaned_text).strip()

        # 4. Clean Markdown syntax for TTS
        # Remove code blocks (``` or ` wrapped text)
        cleaned_text = re.sub(r"```[\s\S]*?```", "", cleaned_text).strip()
        cleaned_text = re.sub(r"`[^`]*`", "", cleaned_text).strip()
        
        # Remove bold and italic formatting
        cleaned_text = re.sub(r"\*\*\*([^*]+)\*\*\*", r"\1", cleaned_text)  # ***bold+italic***
        cleaned_text = re.sub(r"\*\*([^*]+)\*\*", r"\1", cleaned_text)      # **bold**
        cleaned_text = re.sub(r"\*([^*]+)\*", r"\1", cleaned_text)          # *italic*
        cleaned_text = re.sub(r"__([^_]+)__", r"\1", cleaned_text)          # __bold__
        cleaned_text = re.sub(r"_([^_]+)_", r"\1", cleaned_text)            # _italic_
        
        # Remove headers (# ## ### etc.)
        cleaned_text = re.sub(r"^#{1,6}\s*(.*)$", r"\1", cleaned_text, flags=re.MULTILINE)
        
        # Remove list markers (* - +)
        cleaned_text = re.sub(r"^\s*[*\-+]\s+", "", cleaned_text, flags=re.MULTILINE)
        
        # Remove numbered list markers (1. 2. etc.)
        cleaned_text = re.sub(r"^\s*\d+\.\s+", "", cleaned_text, flags=re.MULTILINE)
        
        # Remove links [text](url) -> text
        cleaned_text = re.sub(r"\[([^\]]+)\]\([^)]+\)", r"\1", cleaned_text)
        
        # Remove reference links [text][ref]
        cleaned_text = re.sub(r"\[([^\]]+)\]\[[^\]]+\]", r"\1", cleaned_text)
        
        # Remove standalone asterisks that weren't part of formatting
        cleaned_text = re.sub(r"\*+", "", cleaned_text)

        # 5. Replace multiple spaces/newlines with a single space for cleaner TTS
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        if text != cleaned_text:
            print(f"DEBUG: Cleaned text for TTS. Original: '{text[:50]}...' | Cleaned: '{cleaned_text[:50]}...'")
            
        return cleaned_text

    # --- TTS Service Management ---
    def get_tts_enabled(self) -> bool:
        return self.tts_config_manager.get_tts_enabled()

    def set_tts_enabled(self, enabled: bool) -> bool:
        return self.tts_config_manager.set_tts_enabled(enabled)

    def get_available_tts_services(self) -> List[Dict]:
        services = self.tts_config_manager.get_available_services()
        return [s.__dict__ for s in services]

    def get_current_tts_service(self) -> Optional[Dict[str, str]]:
        try:
            current_service = self.tts_config_manager.get_current_service_info()
            if current_service:
                return {
                    "id": current_service.id,
                    "name": current_service.name,
                    "type": current_service.type,
                    "description": current_service.description
                }
            return None
        except Exception as e:
            print(f"获取当前TTS服务信息失败: {e}")
            return None

    def set_tts_service(self, service_id: str) -> bool:
        return self.tts_config_manager.set_current_service(service_id)

    def get_tts_service_speakers(self, service_id):
        return self.tts_config_manager.get_available_speakers_for_service(service_id)

    def get_current_speaker_for_service(self, service_id: str) -> Optional[str]:
        return self.tts_config_manager.get_current_speaker_for_service(service_id)
    def get_current_tts_speaker(self, service_id: str):
        """为兼容旧API调用而创建的别名"""
        return self.get_current_speaker_for_service(service_id)
    
    def set_current_speaker_for_service(self, service_id: str, speaker: str) -> bool:
        return self.tts_config_manager.set_current_speaker_for_service(service_id, speaker)

    def set_tts_speaker(self, service_id: str, speaker_name: str):
        success = self.tts_config_manager.set_current_speaker_for_service(service_id, speaker_name)
        if not success:
            return

        service_info = self.tts_config_manager.get_current_service_info()
        if service_info and service_info.id == service_id and service_info.type == 'gpt_sovits':
            model_info = self.tts_config_manager.get_current_model_for_service(service_id)
            if model_info:
                new_ref_audio = model_info.get("reference_audio", "")
                new_ref_text = model_info.get("reference_text", "")
                
                self._current_ref_audio_path = new_ref_audio
                self._current_ref_text = new_ref_text
                
                self.speaker_reference_updated_signal.emit(new_ref_audio, new_ref_text)
            else:
                self.speaker_reference_updated_signal.emit("", "")

    def test_tts_service(self, service_id: str, text: str, speaker: Optional[str] = None, **kwargs) -> Tuple[bool, str]:
        if not text:
            return False, "测试文本不能为空。"
        
        try:
            audio_data = self._synthesize_speech(
                text_to_speak=text,
                service_id_override=service_id,
                speaker_override=speaker,
                **kwargs
            )
            
            if audio_data:
                self.tts_play_audio_signal.emit(audio_data)
                return True, "测试成功，已开始播放。"
            else:
                return False, "语音合成失败，请检查控制台错误日志。"

        except Exception as e:
            error_message = f"测试时发生意外错误: {e}"
            traceback.print_exc()
            return False, error_message

    def get_service_config(self, service_id: str) -> Optional[Dict[str, Any]]:
        return self.tts_config_manager.get_service_config(service_id)

    def get_service_info(self, service_id: str) -> Optional[TTSServiceInfo]:
        config = self.tts_config_manager.get_service_config(service_id)
        if config:
        # 我们从config字典中提取所有需要的信息来创建TTSServiceInfo对象
           return TTSServiceInfo(
            id=config.get('id', service_id),
            name=config.get('name', 'Unknown'),
            type=config.get('service_type', 'unknown'),
            config_file=config.get('config_file', ''), # 新增
            enabled=config.get('enabled', False)      # 新增
        )
        return None

    def set_gag_service_defaults(self, service_id: str, gag_settings: Dict[str, str]) -> bool:
        return self.tts_config_manager.set_gag_service_defaults(service_id, gag_settings)
    
    # --- Synthesis Core Logic ---

    def _synthesize_speech(self, text_to_speak: str, service_id_override: Optional[str] = None, speaker_override: Optional[str] = None, **kwargs) -> Optional[bytes]:
        if not self.get_tts_enabled():
            return None

        current_service_id = service_id_override or self.tts_config_manager.current_service_id
        service_config = self.tts_config_manager.get_service_config(current_service_id)

        if not service_config:
            self.app_controller.display_error_message(f"未找到TTS服务配置: {current_service_id}", False)
            return None

        service_type = service_config.get("service_type")
        speaker_to_use = speaker_override or self.tts_config_manager.get_current_speaker_for_service(current_service_id)
        
        final_kwargs = kwargs.copy()
        final_kwargs['speaker'] = speaker_to_use

        if service_type == 'gpt_sovits':
            return self._synthesize_speech_gag(current_service_id, service_config, text_to_speak, **final_kwargs)
        elif service_type == "local_http_vits":
             return self._synthesize_speech_local_http(service_config, text_to_speak, **final_kwargs)
        elif service_type == "huggingface_space_vits":
            return self._synthesize_speech_hf_space(service_config, text_to_speak, **final_kwargs)
        elif service_type == "edge_tts_python_lib":
            return asyncio.run(self._synthesize_speech_edge_tts_online(service_config, text_to_speak, **final_kwargs))
        
        print(f"警告: 未知的TTS服务类型 '{service_type}'")
        return None

    def _synthesize_speech_gag(self, service_id: str, service_config, text_to_speak, **kwargs):
        """Synthesize speech using GAG (GPT-SoVITS) service."""
        speaker_to_use = kwargs.get('speaker')
        if speaker_to_use:
            self.tts_config_manager.set_current_speaker_for_service(service_id, speaker_to_use)
        
        current_model_config = self.tts_config_manager.get_current_model_for_service(service_id)
        if not current_model_config:
            self.app_controller.display_error_message(f"GAG服务错误: 未找到名为 '{speaker_to_use}' 的模型配置", False)
            return None
        
        gpt_path = current_model_config.get("gpt_path")
        sovits_path = current_model_config.get("sovits_path")

        try:
            # Switch GPT model
            if self._current_gag_model_loaded.get("gpt") != gpt_path:
                print(f"正在切换GPT模型: {gpt_path}")
                switch_gpt_url = urljoin(service_config.get("api_base_url"), f"/set_gpt_weights?weights_path={url_quote(gpt_path)}")
                response_gpt = self._make_request_with_retry("get", switch_gpt_url, max_retries=1, timeout=60)
                if response_gpt and response_gpt.status_code == 200 and '"success"' in response_gpt.text:
                    self._current_gag_model_loaded["gpt"] = gpt_path
                    print("✓ GPT模型切换成功")
                else:
                    error_text = response_gpt.text if response_gpt else "请求失败"
                    self.app_controller.display_error_message(f"GAG GPT模型切换失败: {error_text}", False)
                    return None

            # Switch SoVITS model
            if self._current_gag_model_loaded.get("sovits") != sovits_path:
                print(f"正在切换SoVITS模型: {sovits_path}")
                switch_sovits_url = urljoin(service_config.get("api_base_url"), f"/set_sovits_weights?weights_path={url_quote(sovits_path)}")
                response_sovits = self._make_request_with_retry("get", switch_sovits_url, max_retries=1, timeout=60)
                if response_sovits and response_sovits.status_code == 200 and '"success"' in response_sovits.text:
                    self._current_gag_model_loaded["sovits"] = sovits_path
                    print("✓ SoVITS模型切换成功")
                else:
                    error_text = response_sovits.text if response_sovits else "请求失败"
                    self.app_controller.display_error_message(f"GAG SoVITS模型切换失败: {error_text}", False)
                    return None
        except Exception as e:
            self.app_controller.display_error_message(f"GAG模型切换API请求失败: {e}", False)
            return None

        tts_params = {
            "text": text_to_speak,
            "text_lang": "zh",
            "ref_audio_path": kwargs.get("ref_audio_path") or self._current_ref_audio_path or service_config.get("default_ref_audio_path"),
            "prompt_text": kwargs.get("prompt_text") or self._current_ref_text or service_config.get("default_prompt_text"),
            "prompt_lang": kwargs.get("prompt_lang") or service_config.get("default_prompt_lang", "zh"),
            "media_type": "wav"
        }
        tts_url = urljoin(service_config.get("api_base_url"), "/tts")
        
        print(f"向GAG发送合成请求: {tts_params}")
        response = self._make_request_with_retry("get", tts_url, params=tts_params, timeout=60, stream=True)
        if response and response.status_code == 200:
            print("✓ GAG语音流获取成功")
            return response.content
        else:
            error_text = response.text if response else "请求失败"
            self.app_controller.display_error_message(f"GAG语音合成失败: {error_text}", False)
            return None

    def _synthesize_speech_local_http(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        """使用本地HTTP服务（如VITS）合成语音"""
        # This method could be expanded to handle different local http services
        # For now, it's just a placeholder for VITS or similar.
        api_url = f"{config.get('api_url', 'http://127.0.0.1:23456/voice/vits')}"
        params = {
            "text": text,
            "id": config["speakers"][kwargs.get('speaker')]
        }
        response = self._make_request_with_retry(method="GET", url=api_url, params=params)
        return response.content if response else None

    def _synthesize_speech_hf_space(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        try:
            base_url = config["api_base_url"]
            generate_endpoint = config["generate_endpoint"]
            default_params = config.get("default_params", {})
            
            hf_data_list = [
                text,
                kwargs.get('speaker', default_params.get("speaker")),
                kwargs.get('language', default_params.get("language", "ZH")),
                float(kwargs.get('noise_scale', default_params.get("noise_scale", 0.6))),
                float(kwargs.get('noise_scale_w', default_params.get("noise_scale_w", 0.668))),
                float(kwargs.get('length_scale', default_params.get("length_scale", 1.0))),
                float(kwargs.get('sdp_ratio', default_params.get("sdp_ratio", 0.2))),
                kwargs.get('format', default_params.get("format", "wav"))
            ]
            final_request_json = {"data": hf_data_list}
            generate_url = urljoin(base_url, generate_endpoint.lstrip('/'))
            
            response = self._make_request_with_retry(
                method=config.get("request_method", "POST"),
                url=generate_url,
                json=final_request_json,
                headers=config.get("request_headers", {})
            )
            
            if not response or response.status_code != 200:
                return None
            
            response_data = response.json()
            filename = None
            if isinstance(response_data.get("data"), list) and len(response_data["data"]) > 1 and isinstance(response_data["data"][1], dict):
                filename = response_data["data"][1].get("name")
            
            if not filename:
                return None

            file_endpoint_template = config["file_endpoint_template"]
            download_url = urljoin(base_url, file_endpoint_template.format(filename=filename).lstrip('/'))
            download_response = self._make_request_with_retry(method="GET", url=download_url)
            
            return download_response.content if download_response and download_response.status_code == 200 else None
        except Exception as e:
            traceback.print_exc()
            return None

    async def _synthesize_speech_edge_tts_online(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        try:
            voice = kwargs.get('speaker') or config.get("default_speaker", "zh-CN-XiaoxiaoNeural")
            rate_str = str(kwargs.get('rate', config.get("default_rate", "+0%")))
            pitch_str = str(kwargs.get('pitch', config.get("default_pitch", "+0Hz")))
            volume_str = str(kwargs.get('volume', config.get("default_volume", "+0%")))

            communicate = edge_tts.Communicate(text, voice, rate=rate_str, pitch=pitch_str, volume=volume_str)
            
            audio_bytes_io = io.BytesIO()
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_bytes_io.write(chunk["data"])

            return audio_bytes_io.getvalue()
        except Exception as e:
            print(f"Edge TTS synthesis exception: {e}")
            return None

    def _make_request_with_retry(self, method: str, url: str, max_retries: Optional[int] = None, **kwargs) -> Optional[requests.Response]:
        retry_settings = self.tts_config_manager.get_global_settings().get("retry_settings", {})
        
        # 优先使用调用时传入的重试次数，否则使用全局配置，最后使用类默认值
        if max_retries is not None:
            final_max_retries = max_retries
        else:
            final_max_retries = retry_settings.get("max_retries", self.max_retries)

        backoff_factor = retry_settings.get("backoff_factor", 2)
        retry_status_codes = retry_settings.get("retry_status_codes", [500, 502, 503, 504])
        kwargs.setdefault('timeout', self.request_timeout)

        for attempt in range(final_max_retries + 1):
            try:
                response = requests.request(method.upper(), url, **kwargs)
                
                if response.status_code == 200:
                    return response
                elif response.status_code in retry_status_codes and attempt < final_max_retries:
                    wait_time = backoff_factor ** attempt
                    time.sleep(wait_time)
                    continue
                else:
                    return response
            except (requests.Timeout, requests.ConnectionError) as e:
                if attempt < final_max_retries:
                    wait_time = backoff_factor ** attempt
                    time.sleep(wait_time)
                else:
                    return None
            except Exception as e:
                traceback.print_exc()
                return None
        return None

    # --- Playback and Lipsync ---
    def _play_audio_data(self, audio_data: bytes, start_lipsync_flag: bool = True) -> bool:
        if not audio_data:
            return False

        detected_format = self._detect_audio_format(audio_data)
        if not detected_format:
            return False

        temp_audio_path = self.app_controller.temp_file_manager.create_temp_file(data=audio_data, suffix=f".{detected_format}")
        
        # Revert to original logic: get the wav path for lipsync here
        wav_file_path_for_lipsync = self._ensure_wav_format(temp_audio_path, audio_data, detected_format) if start_lipsync_flag else None

        media_content = QMediaContent(QUrl.fromLocalFile(temp_audio_path))
        
        self.player.setMedia(media_content)
        self.player.play()

        # Revert to original logic: Start lipsync immediately after playing.
        if start_lipsync_flag and wav_file_path_for_lipsync:
            self._start_lipsync(wav_file_path_for_lipsync)
            
        return True

    def _detect_audio_format(self, audio_data: bytes) -> str:
        if not isinstance(audio_data, bytes) or len(audio_data) < 16:
            return ""
        if audio_data.startswith(b'RIFF') and audio_data[8:12] == b'WAVE':
            return "wav"
        if audio_data.startswith(b'\xff\xfb') or audio_data.startswith(b'ID3') or audio_data.startswith(b'\xff\xf3'):
            return "mp3"
        if audio_data.startswith(b'OggS'):
            return "ogg"
        if audio_data.startswith(b'fLaC'):
            return "flac"
        return ""

    def _ensure_wav_format(self, original_path: str, audio_data: bytes, detected_format: str) -> Optional[str]:
        if detected_format == "wav":
            return original_path
        
        try:
            from pydub import AudioSegment
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format=detected_format)
            output_wav_path = self.app_controller.temp_file_manager.create_temp_file(data=b'', suffix="_converted.wav")
            audio_segment.export(output_wav_path, format="wav")
            return output_wav_path
        except ImportError:
            print("错误: pydub 未安装，无法将音频转换为WAV格式进行口型同步。")
            return None
        except Exception as e:
            print(f"错误: 音频转换为WAV时出错: {e}")
            return None

    def _start_lipsync(self, wav_file_path: str):
        if not self.live2d_widget:
            print("Live2D小部件不存在，无法开始口型同步。")
            return
            
        try:
            if hasattr(self.live2d_widget, 'start_lipsync_from_file'):
                self.live2d_widget.start_lipsync_from_file(wav_file_path)
                print(f"✓ 口型同步已启动 (新接口): {wav_file_path}")
            elif hasattr(self.live2d_widget, 'start_lipsync'):
                print("尝试使用旧接口启动口型同步...")
                if self.live2d_widget.start_lipsync(wav_file_path):
                    print(f"✓ 口型同步已启动 (旧接口): {wav_file_path}")
                else:
                    print(f"✗ 口型同步启动失败 (旧接口返回False): {wav_file_path}")
            else:
                print("Live2D小部件不支持任何已知的口型同步方法。")
        except Exception as e:
            print(f"启动口型同步时发生异常: {e}")
            traceback.print_exc()

    def _stop_lipsync(self):
        if self.live2d_widget and hasattr(self.live2d_widget, 'stop_lipsync'):
            self.live2d_widget.stop_lipsync()
            print("✓ 口型同步已停止")

    def _on_media_status_changed(self, status):
        if status == QMediaPlayer.MediaStatus.EndOfMedia:
            print("TTSManager: EndOfMedia status received.")
            self._stop_lipsync()
            if hasattr(self.app_controller, 'asr_continue_event'):
                self.app_controller.asr_continue_event.set()
            
            # 播放结束，释放锁并尝试播放下一句
            if self.playback_lock.locked():
                self.playback_lock.release()
            self._audio_playback_manager()

        elif status in [QMediaPlayer.MediaStatus.InvalidMedia, QMediaPlayer.MediaStatus.NoMedia]:
            print(f"TTSManager: Invalid or No Media status received: {status}")
            self._stop_lipsync()
            if hasattr(self.app_controller, 'asr_continue_event'):
                self.app_controller.asr_continue_event.set()

            # 播放出错或无媒体，释放锁并尝试清理/播放下一句
            if self.playback_lock.locked():
                self.playback_lock.release()
            self._audio_playback_manager()

    def _on_media_error_occurred_pyqt5(self, error_code): 
        self._handle_media_error(self.player.errorString(), error_code)

    def _on_media_error_occurred_pyqt6(self): 
        self._handle_media_error(self.player.errorString(), self.player.error())
        
    def _handle_media_error(self, error_string: str, error_code_enum: Any):
        print(f"TTS MediaPlayer Error: {error_string} (Code: {error_code_enum})")
        # 发生错误，释放锁并尝试播放下一句，以防队列阻塞
        if self.playback_lock.locked():
            self.playback_lock.release()
        self._audio_playback_manager()

    def cleanup(self):
        print("TTSManager: Cleaning up...")
        self.tts_cancellation_event.set()
        self.tts_sentence_queue.put(None) # Sentinel to unblock the worker
        if self.tts_worker_thread.is_alive():
            self.tts_worker_thread.join(timeout=2)
        print("TTSManager cleanup finished.") 