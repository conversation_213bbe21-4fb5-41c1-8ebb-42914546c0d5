import os
import json
import logging
from PyQt5.QtWidgets import QWidget

logger = logging.getLogger(__name__)

class ThemeManager:
    """优化的主题管理器"""
    
    THEMES = {
        "dark": {
            # 基础颜色
            "primary": "#007AFF",
            "primary_dark": "#0051D5", 
            "primary_light": "#5AC8FA",
            "secondary": "#5856D6",
            "success": "#34C759",
            "warning": "#FF9500",
            "error": "#FF3B30",
            
            # 背景颜色
            "window_bg": "rgba(28, 28, 30, 0.95)",
            "chat_bg": "rgba(44, 44, 46, 0.95)", 
            "input_bg": "rgba(58, 58, 60, 0.95)",
            "bubble_user_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #007AFF, stop:1 #5856D6)",
            "bubble_ai_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1C1C1E, stop:1 #2C2C2E)",
            "bubble_system_bg": "rgba(255, 204, 0, 0.15)",
            
            # 文本颜色
            "text_primary": "#FFFFFF",
            "text_secondary": "rgba(255, 255, 255, 0.7)",
            "text_tertiary": "rgba(255, 255, 255, 0.5)",
            "text_inverse": "#000000",
            
            # 边框和分割线
            "border_color": "rgba(255, 255, 255, 0.1)",
            "separator_color": "rgba(255, 255, 255, 0.05)",
            
            # 状态颜色
            "online_color": "#34C759",
            "typing_color": "#007AFF",
            
            # 图标
            "theme_icon": "☀️"
        },
        
        "light": {
            # 基础颜色
            "primary": "#007AFF",
            "primary_dark": "#0051D5",
            "primary_light": "#5AC8FA", 
            "secondary": "#5856D6",
            "success": "#34C759",
            "warning": "#FF9500",
            "error": "#FF3B30",
            
            # 背景颜色
            "window_bg": "rgba(255, 255, 255, 0.95)",
            "chat_bg": "rgba(248, 248, 248, 0.95)",
            "input_bg": "rgba(242, 242, 247, 0.95)",
            "bubble_user_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #007AFF, stop:1 #5856D6)",
            "bubble_ai_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #F2F2F7, stop:1 #E5E5EA)",
            "bubble_system_bg": "rgba(255, 204, 0, 0.1)",
            
            # 文本颜色
            "text_primary": "#000000",
            "text_secondary": "rgba(0, 0, 0, 0.7)",
            "text_tertiary": "rgba(0, 0, 0, 0.5)",
            "text_inverse": "#FFFFFF",
            
            # 边框和分割线
            "border_color": "rgba(0, 0, 0, 0.1)",
            "separator_color": "rgba(0, 0, 0, 0.05)",
            
            # 状态颜色
            "online_color": "#34C759",
            "typing_color": "#007AFF",
            
            # 图标
            "theme_icon": "🌙"
        },
        
        "auto": {
            # 自动模式会根据系统主题动态切换
            "theme_icon": "🌗"
        }
    }
    
    def __init__(self):
        self.current_theme = "light"  # 默认浅色主题
        self._load_saved_theme()
        self.current_theme_config = None  # 添加当前主题缓存
        self.update_theme_cache() # 初始化时更新缓存
    
    def update_theme_cache(self):
        """更新主题缓存"""
        self.current_theme_config = self.THEMES.get(self.current_theme, self.THEMES["light"])

    def _load_saved_theme(self):
        """加载保存的主题设置"""
        try:
            theme_file = os.path.join(os.path.expanduser("~"), ".ai_pet_theme.json")
            if os.path.exists(theme_file):
                with open(theme_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.current_theme = data.get("theme", "light")
        except Exception as e:
            logger.warning(f"Failed to load theme settings: {e}")
    
    def _save_theme(self):
        """保存主题设置"""
        try:
            theme_file = os.path.join(os.path.expanduser("~"), ".ai_pet_theme.json")
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump({"theme": self.current_theme}, f)
        except Exception as e:
            logger.warning(f"Failed to save theme settings: {e}")
    
    def get_current_theme(self) -> dict:
        """直接返回缓存"""
        # 如果缓存未初始化（理论上不应该发生，因为 __init__ 调用了 update_theme_cache），则更新一次
        if self.current_theme_config is None:
            self.update_theme_cache()
        return self.current_theme_config
    
    def get_color(self, color_name: str) -> str:
        """使用缓存获取颜色"""
        # 如果缓存未初始化，则更新一次
        if self.current_theme_config is None:
            self.update_theme_cache()
        return self.current_theme_config.get(color_name, "#000000")
    
    def toggle_theme(self) -> str:
        """切换主题"""
        themes = list(self.THEMES.keys())
        if "auto" in themes:
            themes.remove("auto")  # 暂时移除自动模式
        
        current_index = themes.index(self.current_theme) if self.current_theme in themes else 0
        next_index = (current_index + 1) % len(themes)
        self.current_theme = themes[next_index]
        
        self._save_theme()
        self.update_theme_cache()  # 切换后更新缓存
        return self.current_theme
    
    def apply_window_style(self, widget: QWidget) -> str:
        """应用窗口样式"""
        theme = self.get_current_theme()
        return f"""
        QWidget {{
            background-color: {theme['window_bg']};
            color: {theme['text_primary']};
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }}
        """
    
    def apply_chat_area_style(self) -> str:
        """应用聊天区域样式"""
        theme = self.get_current_theme()
        return f"""
        QScrollArea {{
            background-color: {theme['chat_bg']};
            border: none;
            border-radius: 10px;
        }}
        QScrollBar:vertical {{
            background-color: transparent;
            width: 8px;
            border-radius: 4px;
            margin: 2px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {theme['text_tertiary']};
            border-radius: 4px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {theme['text_secondary']};
        }}
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        """
    
    def apply_input_style(self) -> str:
        """应用输入区域样式"""
        theme = self.get_current_theme()
        return f"""
        QTextEdit {{
            background-color: {theme['input_bg']};
            border: 2px solid transparent;
            border-radius: 22px;
            padding: 12px 16px;
            font-size: 14px;
            color: {theme['text_primary']};
            selection-background-color: {theme['primary']};
        }}
        QTextEdit:focus {{
            border-color: {theme['primary']};
        }}
        """ 