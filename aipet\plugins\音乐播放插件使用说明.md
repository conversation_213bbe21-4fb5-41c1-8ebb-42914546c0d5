# 🎵 音乐播放插件使用说明

## 📋 功能概述

音乐播放插件为aipet项目提供了完整的音乐播放功能，支持播放、暂停、停止、音量控制等操作。

## 🎯 支持的功能

### 1. 获取音乐列表 (`get_music_list`)
- **功能**: 获取Music目录下所有可播放的音乐文件
- **支持格式**: mp3, wav, ogg, flac, m4a
- **示例**: "显示我的音乐列表"

### 2. 播放音乐 (`play_music`)
- **功能**: 播放指定的音乐文件
- **支持**: 完全匹配、模糊匹配歌曲名
- **示例**: "播放赤伶"、"播放桃花笑"

### 3. 停止播放 (`stop_music`)
- **功能**: 停止当前播放的音乐
- **示例**: "停止播放"、"停止音乐"

### 4. 暂停/恢复 (`pause_music`)
- **功能**: 暂停当前播放或恢复已暂停的音乐
- **示例**: "暂停音乐"、"恢复播放"

### 5. 音量控制 (`set_volume`)
- **功能**: 设置播放音量 (0-100)
- **示例**: "设置音量为50"、"调大音量到80"

### 6. 播放器状态 (`get_player_status`)
- **功能**: 获取当前播放状态、歌曲信息、音量等
- **示例**: "播放器状态"、"当前播放什么"

### 7. 随机播放 (`play_random_music`)
- **功能**: 从音乐库中随机选择一首歌曲播放
- **示例**: "随机播放一首歌"、"来首随机音乐"

### 8. 播放模式设置 (`set_play_mode`)
- **功能**: 设置播放模式
- **模式**:
  - `normal`: 正常播放（播放完停止）
  - `repeat_one`: 单曲循环（重复播放当前歌曲）
  - `repeat_all`: 列表循环（播放完所有歌曲后重新开始）
- **示例**: "设置单曲循环"、"设置列表循环"、"设置正常播放"

### 9. 下一首 (`play_next`)
- **功能**: 播放下一首歌曲
- **示例**: "下一首"、"播放下一首歌"

### 10. 上一首 (`play_previous`)
- **功能**: 播放上一首歌曲
- **示例**: "上一首"、"播放上一首歌"

### 11. 播放列表 (`start_playlist`)
- **功能**: 开始播放列表播放，从第一首开始按顺序播放所有音乐
- **示例**: "开始播放列表"、"顺序播放所有音乐"

## 📁 音乐目录设置

- **默认目录**: `E:\live2d\Music`
- **支持格式**: mp3, wav, ogg, flac, m4a
- **自动扫描**: 插件会自动扫描目录下的所有音乐文件

## 🎮 使用示例

### 基本播放操作
```
用户: "播放赤伶"
AI: 🎵 正在播放: 赤伶 (mp3)

用户: "暂停"
AI: ⏸️ 音乐已暂停

用户: "继续播放"
AI: ▶️ 音乐已恢复播放

用户: "停止"
AI: ⏹️ 音乐已停止
```

### 音乐管理
```
用户: "我有哪些音乐"
AI: 🎵 可用音乐列表:
1. 人间惊鸿客-叶里 (mp3)
2. 十年人间 (mp3)
3. 叹云兮 (mp3)
...

用户: "随机播放一首"
AI: 🎵 正在播放: 大喜 (mp3)

用户: "播放器状态"
AI: 🎵 音乐播放器状态:
播放状态: 播放中
当前歌曲: 大喜 (mp3)
音量: 70%
音乐库: 12 首歌曲
```

### 音量控制
```
用户: "音量调到50"
AI: 🔊 音量已设置为: 50%

用户: "调大音量"
AI: 🔊 音量已设置为: 80%
```

### 播放模式控制
```
用户: "设置单曲循环"
AI: 🔄 播放模式已设置为: 单曲循环

用户: "设置列表循环"
AI: 🔄 播放模式已设置为: 列表循环

用户: "播放器状态"
AI: 🎵 音乐播放器状态:
播放状态: 播放中
当前歌曲: 赤伶 (mp3)
播放模式: 单曲循环
音量: 70%
音乐库: 12 首歌曲
```

### 播放列表控制
```
用户: "开始播放列表"
AI: 📋 开始播放列表: 人间惊鸿客-叶里 (mp3)
🔄 播放模式: 列表循环

用户: "下一首"
AI: ⏭️ 播放下一首: 十年人间 (mp3)

用户: "上一首"
AI: ⏮️ 播放上一首: 人间惊鸿客-叶里 (mp3)
```

## 🔧 技术细节

- **音频引擎**: pygame.mixer
- **支持格式**: 主流音频格式
- **内存管理**: 自动管理音频资源
- **错误处理**: 完善的异常处理机制
- **状态管理**: 实时跟踪播放状态

## 📝 注意事项

1. **依赖要求**: 需要安装pygame库 (`pip install pygame>=2.0.0`)
2. **音乐目录**: 确保Music目录存在且包含音乐文件
3. **文件格式**: 建议使用mp3格式以获得最佳兼容性
4. **音量范围**: 音量设置范围为0-100
5. **模糊匹配**: 播放音乐时支持部分歌曲名匹配

## 🚀 安装和配置

1. 确保pygame已安装:
   ```bash
   pip install pygame>=2.0.0
   ```

2. 将音乐文件放入Music目录:
   ```
   E:\live2d\Music\
   ├── 赤伶.mp3
   ├── 大喜.mp3
   └── ...
   ```

3. 重启aipet应用，插件将自动加载

## 🎉 完成！

音乐播放插件已经完全集成到你的aipet项目中，现在你可以通过与AI对话来控制音乐播放了！
