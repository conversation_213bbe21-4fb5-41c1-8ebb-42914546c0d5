from dataclasses import dataclass, field, asdict
from typing import Optional, Dict, Any
from datetime import datetime, timezone
import uuid

@dataclass
class AssistantConfig:
    id: str = field(default_factory=lambda: uuid.uuid4().hex)
    name: str = "New Assistant"
    model_path: str = ""
    system_prompt: str = "You are a helpful assistant."
    avatar_path: Optional[str] = None  # 新增头像路径属性
    created_time: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat())
    last_used: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat())

    def to_dict(self) -> Dict[str, Any]:
        """将实例转换为字典，以便序列化。"""
        # return asdict(self) # asdict 对于 Optional[str] = None 会直接转换
        data = {
            "id": self.id,
            "name": self.name,
            "model_path": self.model_path,
            "system_prompt": self.system_prompt,
            "avatar_path": self.avatar_path, # 添加到字典
            "created_time": self.created_time,
            "last_used": self.last_used
        }
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AssistantConfig":
        """从字典创建 AssistantConfig 实例。"""
        return cls(
            id=data.get("id", uuid.uuid4().hex),
            name=data.get("name", "Unnamed Assistant"),
            model_path=data.get("model_path", ""),
            system_prompt=data.get("system_prompt", "You are a helpful assistant."),
            avatar_path=data.get("avatar_path"),  # 从字典获取 avatar_path, 允许 None
            created_time=data.get("created_time", datetime.now(timezone.utc).isoformat()),
            last_used=data.get("last_used", datetime.now(timezone.utc).isoformat())
        ) 