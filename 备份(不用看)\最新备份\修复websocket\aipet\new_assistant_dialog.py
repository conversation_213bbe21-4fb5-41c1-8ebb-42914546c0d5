from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QTextEdit, QPushButton, QFileDialog, 
                             QDialogButtonBox, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap # 新增导入
from typing import Optional # 新增导入

# 尝试从相对路径导入，如果直接运行脚本则从当前目录导入
try:
    from .assistant_manager import AssistantManager
    from .assistant_config import AssistantConfig # 虽然不直接用，但manager可能需要
except ImportError:
    from assistant_manager import Assistant<PERSON>anager
    from assistant_config import AssistantConfig


class NewAssistantDialog(QDialog):
    def __init__(self, parent=None, assistant_manager: AssistantManager = None, assistant_to_edit: Optional[AssistantConfig] = None):
        super().__init__(parent)
        self.assistant_manager = assistant_manager
        self.assistant_to_edit = assistant_to_edit # 新增：存储待编辑的助手
        self.current_avatar_original_path: Optional[str] = None # 用于存储用户新选择的头像原始路径
        self.avatar_cleared: bool = False # 标记用户是否点击了清除头像按钮
        
        # 根据是新建还是编辑来设置窗口标题
        if self.assistant_to_edit:
            self.setWindowTitle(f"编辑助手 - {self.assistant_to_edit.name}")
        else:
            self.setWindowTitle("新建助手")
        
        self.setModal(True)
        self.setMinimumWidth(450)

        self._setup_ui()

        # 如果是编辑模式，加载现有数据
        if self.assistant_to_edit:
            self._load_data_for_editing()

    def _setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # 助手名称
        name_label = QLabel("助手名称:")
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如：我的专属助手")
        layout.addWidget(name_label)
        layout.addWidget(self.name_edit)

        # Live2D 模型路径
        model_path_label = QLabel("Live2D 模型路径 (.model3.json):")
        model_path_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("点击右侧按钮选择模型文件")
        self.model_path_edit.setReadOnly(True) # 通常路径通过对话框选择
        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self._browse_model_path)
        model_path_layout.addWidget(self.model_path_edit)
        model_path_layout.addWidget(browse_button)
        layout.addWidget(model_path_label)
        layout.addLayout(model_path_layout)

        # 系统提示词
        prompt_label = QLabel("系统提示词:")
        self.prompt_edit = QTextEdit()
        self.prompt_edit.setPlaceholderText("定义助手的行为和个性...")
        self.prompt_edit.setMinimumHeight(100)
        layout.addWidget(prompt_label)
        layout.addWidget(self.prompt_edit)

        # 头像选择区域
        avatar_section_label = QLabel("助手头像:")
        layout.addWidget(avatar_section_label)

        avatar_layout = QHBoxLayout()
        self.avatar_preview_label = QLabel("无头像")
        self.avatar_preview_label.setFixedSize(100, 100) # 预览区域大小
        self.avatar_preview_label.setAlignment(Qt.AlignCenter)
        self.avatar_preview_label.setStyleSheet("border: 1px solid gray;") # 简单边框
        avatar_layout.addWidget(self.avatar_preview_label)

        avatar_buttons_layout = QVBoxLayout()
        self.select_avatar_button = QPushButton("选择图片...")
        self.select_avatar_button.clicked.connect(self._select_avatar_image)
        avatar_buttons_layout.addWidget(self.select_avatar_button)
        
        self.clear_avatar_button = QPushButton("清除头像")
        self.clear_avatar_button.clicked.connect(self._clear_avatar)
        avatar_buttons_layout.addWidget(self.clear_avatar_button)
        avatar_buttons_layout.addStretch() # 使按钮靠上

        avatar_layout.addLayout(avatar_buttons_layout)
        avatar_layout.addStretch() # 使头像预览和按钮组靠左

        layout.addLayout(avatar_layout)

        # 按钮
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def _load_data_for_editing(self):
        if not self.assistant_to_edit:
            return
        self.name_edit.setText(self.assistant_to_edit.name)
        self.model_path_edit.setText(self.assistant_to_edit.model_path)
        self.prompt_edit.setPlainText(self.assistant_to_edit.system_prompt)
        
        # 加载现有头像
        self.current_avatar_original_path = None # 重置
        self.avatar_cleared = False # 重置
        if self.assistant_to_edit.avatar_path and self.assistant_manager:
            full_avatar_path = self.assistant_manager.get_full_avatar_path(self.assistant_to_edit.avatar_path)
            if full_avatar_path:
                pixmap = QPixmap(full_avatar_path)
                if not pixmap.isNull():
                    self.avatar_preview_label.setPixmap(pixmap.scaled(
                        self.avatar_preview_label.width(), 
                        self.avatar_preview_label.height(), 
                        Qt.KeepAspectRatio, 
                        Qt.SmoothTransformation
                    ))
                else:
                    self.avatar_preview_label.setText("头像加载失败")
            else:
                self.avatar_preview_label.setText("头像路径无效")
        else:
            self.avatar_preview_label.setText("无头像")
        
        # 重要：如果助手的ID是基于名称生成的（例如旧的 _generate_id 逻辑），
        # 那么在编辑模式下，助手名称字段应该设为只读，以防止ID的隐式更改。
        # 如果ID是UUID或者其他与名称无关的方式生成，则名称可以编辑。
        # 假设当前的 _generate_id 是基于名称的，则禁用名称编辑：
        # self.name_edit.setReadOnly(True) 
        # 或者，如果允许名称更改，确保 AssistantManager.update_assistant 知道如何处理
        # （例如，如果名称是ID的一部分，则更新ID，或者ID保持不变仅更新显示名称）。
        # 为简单起见，并且因为 _generate_id 会处理重名并加后缀，我们暂时允许编辑名称。
        # 如果 assistant_manager.update_assistant 直接使用传入的 config.id，则名称更改没问题。

    def _browse_model_path(self):
        # 尝试从 assistant_manager 获取上次的目录，或者使用用户主目录
        start_dir = ""
        if self.assistant_manager and self.assistant_manager.settings.get("last_model_dir"):
            start_dir = self.assistant_manager.settings.get("last_model_dir")
        else:
            # 导入 os 以使用 os.path.expanduser
            import os 
            start_dir = os.path.expanduser("~")

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择 Live2D 模型文件",
            start_dir,
            "Live2D Model JSON (*.model3.json);;所有文件 (*.*)"
        )
        if file_path:
            self.model_path_edit.setText(file_path)
            # 保存此次选择的目录，方便下次打开
            if self.assistant_manager:
                import os
                self.assistant_manager.settings["last_model_dir"] = os.path.dirname(file_path)
                # (可选) 立即保存设置，但这可能不是最佳时机
                # self.assistant_manager.save_config()

    def _select_avatar_image(self):
        # 导入 os 以便后续使用 (如果需要处理路径)
        # import os
        start_dir = ""
        # 你可以考虑保存和加载上次选择图片的目录，类似于 _browse_model_path
        # if self.assistant_manager and self.assistant_manager.settings.get("last_avatar_dir"):
        #     start_dir = self.assistant_manager.settings.get("last_avatar_dir")
        # else:
        #     start_dir = os.path.expanduser("~")

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择头像图片",
            start_dir, # 初始目录，可自定义
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*.*)"
        )
        if file_path:
            self.current_avatar_original_path = file_path
            self.avatar_cleared = False # 用户选择了新图片，所以清除标记无效
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                self.avatar_preview_label.setPixmap(pixmap.scaled(
                    self.avatar_preview_label.width(), 
                    self.avatar_preview_label.height(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                ))
                # if self.assistant_manager: # 保存上次目录的逻辑
                #     self.assistant_manager.settings["last_avatar_dir"] = os.path.dirname(file_path)
            else:
                self.current_avatar_original_path = None # 无效图片，重置路径
                self.avatar_preview_label.setText("图片无效")
                QMessageBox.warning(self, "图片错误", "无法加载所选的图片文件。")

    def _clear_avatar(self):
        self.current_avatar_original_path = None
        self.avatar_cleared = True
        self.avatar_preview_label.setText("无头像") # 或者清除 QPixmap
        # self.avatar_preview_label.setPixmap(QPixmap()) # 清除图片

    def accept(self):
        name = self.name_edit.text().strip()
        model_path = self.model_path_edit.text().strip()
        system_prompt = self.prompt_edit.toPlainText().strip()

        if not name:
            QMessageBox.warning(self, "输入错误", "助手名称不能为空。")
            self.name_edit.setFocus()
            return
        
        if not model_path:
            QMessageBox.warning(self, "输入错误", "Live2D 模型路径不能为空。")
            # 通常浏览按钮会强制选择，但以防万一
            return

        if not system_prompt:
            reply = QMessageBox.question(self, "确认", 
                                         "系统提示词为空，是否继续？",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                self.prompt_edit.setFocus()
                return

        if not self.assistant_manager:
            QMessageBox.critical(self, "错误", "AssistantManager 未初始化！")
            return

        if self.assistant_to_edit: # 编辑模式
            update_data = {
                "name": name,
                "model_path": model_path,
                "system_prompt": system_prompt
            }
            # 处理头像更新
            if self.current_avatar_original_path: # 用户选择了新头像文件
                update_data["avatar_original_path"] = self.current_avatar_original_path
            elif self.avatar_cleared: # 用户清除了头像
                update_data["clear_avatar"] = True
            # 如果 current_avatar_original_path 为 None且 avatar_cleared 为 False, 则不修改头像

            if self.assistant_manager.update_assistant(self.assistant_to_edit.id, update_data):
                QMessageBox.information(self, "成功", f"助手 '{name}' 已成功更新！")
                super().accept()
            else:
                QMessageBox.critical(self, "更新失败", "无法更新助手，详情请查看控制台输出。")
        else: # 创建模式
            new_assistant = self.assistant_manager.create_assistant(
                name=name, 
                model_path=model_path, 
                system_prompt=system_prompt,
                avatar_original_path=self.current_avatar_original_path # 传递原始头像路径
            )
            if new_assistant:
                QMessageBox.information(self, "成功", f"助手 '{name}' 已成功创建！")
                super().accept()
            else:
                QMessageBox.critical(self, "创建失败", "无法创建助手，请检查控制台输出获取更多信息。\n可能原因：ID已存在或保存配置失败。")


if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys
    import os

    # 这是一个简单的测试用例
    # 实际使用时，AssistantManager 实例会由 ChatWindow 传入

    class MockLive2DWidget: # 用于 AssistantManager 的测试
        def switch_model(self, model_path: str) -> bool:
            print(f"MockLive2D: Switching to {model_path}")
            return True

    class MockChatSystem: # 用于 AssistantManager 的测试
        def __init__(self):
            self.system_prompt = ""
        def update_system_prompt(self, prompt: str):
            self.system_prompt = prompt
            print(f"MockChatSystem: System prompt updated to: {prompt[:30]}...")

    # 创建一个临时的aipet目录用于测试
    if not os.path.exists("aipet"):
        os.makedirs("aipet")
        
    # 创建一个虚拟的 AssistantManager
    mock_assistant_manager = AssistantManager(config_dir="aipet")

    app = QApplication(sys.argv)

    # 测试创建模式
    print("--- 测试创建模式 ---")
    create_dialog = NewAssistantDialog(assistant_manager=mock_assistant_manager)
    if create_dialog.exec_() == QDialog.Accepted:
        print("新助手对话框已接受 (创建模式)。")
    else:
        print("新助手对话框已取消 (创建模式)。")

    all_assistants = mock_assistant_manager.get_all_assistants()
    if all_assistants:
        print("\\n当前助手列表:")
        for ast_idx, ast_val in enumerate(all_assistants):
            print(f" - [{ast_idx}] ID: {ast_val.id}, Name: {ast_val.name}, Model: {ast_val.model_path[:30]}...")
        
        # 测试编辑模式 (如果至少有一个助手)
        assistant_to_actually_edit = all_assistants[0]
        print(f"\\n--- 测试编辑模式 (编辑: {assistant_to_actually_edit.name}) ---")
        
        # AssistantManager 需要 update_assistant 方法
        # 暂时在 manager 上打个桩
        def mock_update_assistant(config_to_update: AssistantConfig) -> bool:
            print(f"Mock AssistantManager: Pretending to update {config_to_update.id} with name {config_to_update.name}")
            # 在真实实现中，这里会更新 self.assistants[config_to_update.id] 并保存
            if config_to_update.id in mock_assistant_manager.assistants:
                mock_assistant_manager.assistants[config_to_update.id] = config_to_update
                mock_assistant_manager.save_config()
                return True
            return False
        mock_assistant_manager.update_assistant = mock_update_assistant

        edit_dialog = NewAssistantDialog(assistant_manager=mock_assistant_manager, assistant_to_edit=assistant_to_actually_edit)
        if edit_dialog.exec_() == QDialog.Accepted:
            print("新助手对话框已接受 (编辑模式)。")
        else:
            print("新助手对话框已取消 (编辑模式)。")
        
        print("\\n编辑后的助手列表:")
        for ast in mock_assistant_manager.get_all_assistants():
            print(f" - ID: {ast.id}, Name: {ast.name}, Model: {ast.model_path[:30]}...")

    else:
        print("没有助手被创建，跳过编辑模式测试。")
    
    # 清理：删除临时的assistants.json和aipet目录
    if os.path.exists("aipet/assistants.json"):
        os.remove("aipet/assistants.json")
    if os.path.exists("aipet"):
        try:
            os.rmdir("aipet") # 只在目录为空时删除
        except OSError:
            print("aipet 目录不为空，未删除。")

    # sys.exit(app.exec_()) # 在测试中通常不需要事件循环 