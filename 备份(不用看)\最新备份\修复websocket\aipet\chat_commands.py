import re
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

@dataclass
class CommandResult:
    """命令执行结果"""
    success: bool
    message: str
    data: Optional[Any] = None

class ChatCommandParser:
    """聊天命令解析器"""
    
    def __init__(self):
        self.commands: Dict[str, Dict] = {}
        self._register_default_commands()
    
    def register_command(self, 
                        command: str, 
                        handler: Callable, 
                        description: str,
                        aliases: Optional[List[str]] = None,
                        usage: Optional[str] = None):
        """
        注册聊天命令
        
        Args:
            command: 命令名称（不包含/前缀）
            handler: 命令处理函数
            description: 命令描述
            aliases: 命令别名列表
            usage: 使用方法说明
        """
        self.commands[command] = {
            'handler': handler,
            'description': description,
            'aliases': aliases or [],
            'usage': usage or f"/{command}"
        }
        
        # 注册别名
        if aliases:
            for alias in aliases:
                self.commands[alias] = self.commands[command]
    
    def _register_default_commands(self):
        """注册默认的内置命令"""
        self.register_command(
            'help',
            self._handle_help,
            '显示可用命令列表',
            aliases=['h', '?'],
            usage='/help [命令名称]'
        )
    
    def parse_command(self, text: str) -> Optional[tuple]:
        """
        解析命令文本
        
        Args:
            text: 输入文本
            
        Returns:
            (command, args) 元组，如果不是命令则返回None
        """
        text = text.strip()
        
        # 检查是否以/开头
        if not text.startswith('/'):
            return None
        
        # 移除/前缀并分割命令和参数
        command_text = text[1:]
        parts = command_text.split()
        
        if not parts:
            return None
        
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        return command, args
    
    def execute_command(self, text: str, context: Any = None) -> Optional[CommandResult]:
        """
        执行命令
        
        Args:
            text: 命令文本
            context: 上下文对象（通常是AppController实例）
            
        Returns:
            CommandResult对象，如果不是命令则返回None
        """
        parsed = self.parse_command(text)
        if not parsed:
            return None
        
        command, args = parsed
        
        if command not in self.commands:
            return CommandResult(
                success=False,
                message=f"未知命令: /{command}\n输入 /help 查看可用命令"
            )
        
        try:
            handler = self.commands[command]['handler']
            return handler(args, context)
        except Exception as e:
            return CommandResult(
                success=False,
                message=f"命令执行失败: {str(e)}"
            )
    
    def _handle_help(self, args: List[str], context: Any) -> CommandResult:
        """处理help命令"""
        if args and args[0] in self.commands:
            # 显示特定命令的帮助
            cmd_info = self.commands[args[0]]
            help_text = f"命令: /{args[0]}\n"
            help_text += f"描述: {cmd_info['description']}\n"
            help_text += f"用法: {cmd_info['usage']}"
            if cmd_info['aliases']:
                help_text += f"\n别名: {', '.join(['/' + a for a in cmd_info['aliases']])}"
        else:
            # 显示所有命令
            help_text = "📋 可用命令列表:\n\n"
            
            # 去重命令（排除别名）
            unique_commands = {}
            for cmd, info in self.commands.items():
                if cmd not in info.get('aliases', []):
                    unique_commands[cmd] = info
            
            for cmd, info in unique_commands.items():
                help_text += f"🔹 /{cmd} - {info['description']}\n"
            
            help_text += "\n💡 输入 /help <命令名> 查看具体用法"
        
        return CommandResult(success=True, message=help_text)
    
    def get_available_commands(self) -> List[str]:
        """获取所有可用命令列表"""
        unique_commands = set()
        for cmd, info in self.commands.items():
            if cmd not in info.get('aliases', []):
                unique_commands.add(cmd)
        return sorted(list(unique_commands))

class TTSCommandHandler:
    """TTS相关命令处理器"""
    
    def __init__(self, command_parser: ChatCommandParser):
        self.parser = command_parser
        self._register_tts_commands()
    
    def _register_tts_commands(self):
        """注册TTS相关命令"""
        self.parser.register_command(
            'tts',
            self._handle_tts_command,
            'TTS服务管理',
            aliases=['voice'],
            usage='/tts <子命令> [参数]\n  子命令: list, current, use, test, speakers, stop'
        )
    
    def _handle_tts_command(self, args: List[str], context: Any) -> CommandResult:
        """处理TTS主命令"""
        if not args:
            return CommandResult(
                success=False,
                message="请指定TTS子命令。用法:\n"
                       "/tts list - 列出所有TTS服务\n"
                       "/tts current - 查看当前服务\n"
                       "/tts use <服务ID> - 切换服务\n"
                       "/tts test [服务ID] - 测试服务\n"
                       "/tts speakers [服务ID] - 查看说话人\n"
                       "/tts stop - 停止当前TTS播放"
            )
        
        subcommand = args[0].lower()
        sub_args = args[1:] if len(args) > 1 else []
        
        if subcommand == 'list':
            return self._handle_tts_list(sub_args, context)
        elif subcommand == 'current':
            return self._handle_tts_current(sub_args, context)
        elif subcommand == 'use' or subcommand == 'select':
            return self._handle_tts_use(sub_args, context)
        elif subcommand == 'test':
            return self._handle_tts_test(sub_args, context)
        elif subcommand == 'speakers':
            return self._handle_tts_speakers(sub_args, context)
        elif subcommand == 'stop':
            return self._handle_tts_stop(sub_args, context)
        else:
            return CommandResult(
                success=False,
                message=f"未知的TTS子命令: {subcommand}\n"
                        "输入 /tts 查看完整用法:\n"
                        "/tts list\n"
                        "/tts current\n"
                        "/tts use <服务ID>\n"
                        "/tts test [服务ID]\n"
                        "/tts speakers [服务ID]\n"
                        "/tts stop"
            )
    
    def _handle_tts_list(self, args: List[str], context: Any) -> CommandResult:
        """处理 /tts list 命令"""
        try:
            if not hasattr(context, 'get_available_tts_services'):
                return CommandResult(success=False, message="TTS功能不可用")
            
            services = context.get_available_tts_services()
            if not services:
                return CommandResult(success=False, message="没有可用的TTS服务")
            
            current_service = context.get_current_tts_service()
            current_id = current_service['id'] if current_service else None
            
            message = "🎤 可用的TTS服务:\n\n"
            for service in services:
                status = "✅ 当前" if service['id'] == current_id else "⚪"
                message += f"{status} {service['id']} - {service['name']}\n"
                message += f"   类型: {service['type']}\n"
                if service.get('description'):
                    message += f"   描述: {service['description']}\n"
                message += "\n"
            
            message += "💡 使用 /tts use <服务ID> 切换服务"
            
            return CommandResult(success=True, message=message, data=services)
            
        except Exception as e:
            return CommandResult(success=False, message=f"获取TTS服务列表失败: {str(e)}")
    
    def _handle_tts_current(self, args: List[str], context: Any) -> CommandResult:
        """处理 /tts current 命令"""
        try:
            if not hasattr(context, 'get_current_tts_service'):
                return CommandResult(success=False, message="TTS功能不可用")
            
            current_service = context.get_current_tts_service()
            if not current_service:
                return CommandResult(success=False, message="当前没有可用的TTS服务")
            
            message = f"🎤 当前TTS服务:\n\n"
            message += f"🔹 ID: {current_service['id']}\n"
            message += f"🔹 名称: {current_service['name']}\n"
            message += f"🔹 类型: {current_service['type']}\n"
            if current_service.get('description'):
                message += f"🔹 描述: {current_service['description']}\n"
            
            # 获取说话人信息
            try:
                speakers = context.get_tts_service_speakers()
                if speakers:
                    message += f"🔹 可用说话人: {len(speakers)} 个\n"
                    if len(speakers) <= 5:
                        message += f"   {', '.join(speakers)}\n"
                    else:
                        message += f"   {', '.join(speakers[:5])}, ...\n"
            except:
                pass
            
            return CommandResult(success=True, message=message, data=current_service)
            
        except Exception as e:
            return CommandResult(success=False, message=f"获取当前TTS服务信息失败: {str(e)}")
    
    def _handle_tts_use(self, args: List[str], context: Any) -> CommandResult:
        """处理 /tts use <服务ID> 命令"""
        if not args:
            return CommandResult(success=False, message="请指定要切换的TTS服务ID\n用法: /tts use <服务ID>")
        
        service_id = args[0]
        
        try:
            if not hasattr(context, 'set_tts_service'):
                return CommandResult(success=False, message="TTS功能不可用")
            
            success = context.set_tts_service(service_id)
            if success:
                # 获取服务信息用于确认消息
                current_service = context.get_current_tts_service()
                service_name = current_service['name'] if current_service else service_id
                
                return CommandResult(
                    success=True, 
                    message=f"✅ TTS服务已切换到: {service_name}",
                    data={'service_id': service_id}
                )
            else:
                return CommandResult(success=False, message=f"❌ 切换到TTS服务 '{service_id}' 失败")
            
        except Exception as e:
            return CommandResult(success=False, message=f"切换TTS服务时发生错误: {str(e)}")
    
    def _handle_tts_test(self, args: List[str], context: Any) -> CommandResult:
        """处理 /tts test [服务ID] 命令"""
        service_id = args[0] if args else None
        test_text = "这是一个TTS服务测试"
        
        try:
            if not hasattr(context, 'test_tts_service'):
                return CommandResult(success=False, message="TTS功能不可用")
            
            service_name = service_id
            if service_id:
                # 获取服务名称
                services = context.get_available_tts_services()
                for service in services:
                    if service['id'] == service_id:
                        service_name = service['name']
                        break
            else:
                # 测试当前服务
                current_service = context.get_current_tts_service()
                service_name = current_service['name'] if current_service else "当前服务"
            
            message = f"🧪 正在测试TTS服务: {service_name}..."
            
            # 执行测试
            success = context.test_tts_service(service_id, test_text)
            
            if success:
                return CommandResult(
                    success=True,
                    message=f"✅ TTS服务测试成功: {service_name}\n已播放测试音频",
                    data={'service_id': service_id, 'tested': True}
                )
            else:
                return CommandResult(
                    success=False,
                    message=f"❌ TTS服务测试失败: {service_name}"
                )
            
        except Exception as e:
            return CommandResult(success=False, message=f"TTS服务测试时发生错误: {str(e)}")
    
    def _handle_tts_speakers(self, args: List[str], context: Any) -> CommandResult:
        """处理 /tts speakers [服务ID] 命令"""
        service_id = args[0] if args else None
        
        try:
            if not hasattr(context, 'get_tts_service_speakers'):
                return CommandResult(success=False, message="TTS功能不可用")
            
            speakers = context.get_tts_service_speakers(service_id)
            
            if service_id:
                # 获取指定服务的名称
                services = context.get_available_tts_services()
                service_name = service_id
                for service in services:
                    if service['id'] == service_id:
                        service_name = service['name']
                        break
            else:
                # 当前服务
                current_service = context.get_current_tts_service()
                service_name = current_service['name'] if current_service else "当前服务"
            
            if not speakers:
                return CommandResult(
                    success=False,
                    message=f"服务 {service_name} 没有可用的说话人信息"
                )
            
            message = f"🎭 {service_name} 可用说话人 ({len(speakers)} 个):\n\n"
            
            # 分组显示，每行最多3个
            for i in range(0, len(speakers), 3):
                line_speakers = speakers[i:i+3]
                message += "🔹 " + " | ".join(line_speakers) + "\n"
            
            return CommandResult(success=True, message=message, data=speakers)
            
        except Exception as e:
            return CommandResult(success=False, message=f"获取说话人列表时发生错误: {str(e)}")
    
    def _handle_tts_stop(self, args: List[str], context: Any) -> CommandResult:
        """处理 /tts stop 命令"""
        try:
            if hasattr(context, '_stop_tts_playback'):
                # 调用AppController的停止方法，并标记为用户操作
                context._stop_tts_playback(triggered_by_user_action=True)
                # _stop_tts_playback 内部会根据配置决定是否显示用户消息，
                # 所以这里可以直接返回成功。
                return CommandResult(success=True, message="✅ TTS播放已请求停止。")
            else:
                return CommandResult(success=False, message="❌ TTS停止功能在当前上下文中不可用。")
        except Exception as e:
            return CommandResult(success=False, message=f"❌ 停止TTS播放时发生错误: {str(e)}")