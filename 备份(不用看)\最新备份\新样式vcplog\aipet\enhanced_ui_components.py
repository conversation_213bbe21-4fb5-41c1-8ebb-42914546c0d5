"""
增强UI组件 - 专注于用户交互体验优化
提供加载指示器、Toast通知、智能输入建议等功能
"""

import sys
import time
from typing import Optional, List, Dict, Any, Callable
from PyQt5.QtCore import (Qt, QTimer, QPropertyAnimation, QEasingCurve, 
                          pyqtSignal, QPoint, QRect, QSize)
from PyQt5.QtGui import (QColor, QPainter, QPen, QBrush, QFont, 
                         QFontMetrics, QPixmap, QPainterPath)
from PyQt5.QtWidgets import (QWidget, QLabel, QVBoxLayout, QHBoxLayout, 
                             QFrame, QGraphicsOpacityEffect, QListWidget, 
                             QListWidgetItem, QPushButton, QProgressBar,
                             QStackedWidget, QTextEdit)

class ModernLoadingIndicator(QWidget):
    """现代化加载指示器"""
    
    def __init__(self, parent=None, size=60):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        self.is_spinning = False
        self.setup_ui()
        self.setup_animation()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedSize(self.size, self.size)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 创建消息标签
        self.message_label = QLabel("加载中...")
        self.message_label.setAlignment(Qt.AlignCenter)
        self.message_label.setStyleSheet("""
            QLabel {
                color: #007AFF;
                font-size: 14px;
                font-weight: 500;
                background: transparent;
                margin-top: 10px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.addWidget(self.message_label)
        layout.setContentsMargins(0, 0, 0, 0)
    
    def setup_animation(self):
        """设置动画"""
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_animation)
        self.timer.setInterval(50)  # 20 FPS
    
    def update_animation(self):
        """更新动画"""
        self.angle = (self.angle + 10) % 360
        self.update()
    
    def paintEvent(self, event):
        """绘制加载圆圈"""
        if not self.is_spinning:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 计算圆圈位置
        center = QPoint(self.width() // 2, self.height() // 2 - 20)
        radius = min(self.width(), self.height()) // 4
        
        # 绘制背景圆圈
        painter.setPen(QPen(QColor(200, 200, 200, 80), 3))
        painter.drawEllipse(center, radius, radius)
        
        # 绘制旋转的加载弧
        painter.setPen(QPen(QColor(0, 122, 255), 3))
        painter.drawArc(
            center.x() - radius, center.y() - radius,
            radius * 2, radius * 2,
            self.angle * 16, 120 * 16  # 120度弧
        )
    
    def start_loading(self, message="加载中..."):
        """开始加载动画"""
        self.message_label.setText(message)
        self.is_spinning = True
        self.timer.start()
        self.show()
    
    def stop_loading(self):
        """停止加载动画"""
        self.is_spinning = False
        self.timer.stop()
        self.hide()

class ToastNotification(QWidget):
    """Toast通知组件"""
    
    TYPE_INFO = "info"
    TYPE_SUCCESS = "success"
    TYPE_WARNING = "warning"
    TYPE_ERROR = "error"
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_animation()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(50)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        
        # 图标标签
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(20, 20)
        self.icon_label.setAlignment(Qt.AlignCenter)
        
        # 消息标签
        self.message_label = QLabel()
        self.message_label.setWordWrap(True)
        
        layout.addWidget(self.icon_label)
        layout.addWidget(self.message_label)
        layout.addStretch()
    
    def setup_animation(self):
        """设置动画"""
        # 淡入动画
        self.fade_in_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.fade_in_effect)
        
        self.fade_in_animation = QPropertyAnimation(self.fade_in_effect, b"opacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 自动隐藏定时器
        self.auto_hide_timer = QTimer(self)
        self.auto_hide_timer.setSingleShot(True)
        self.auto_hide_timer.timeout.connect(self.hide_with_animation)
    
    def show_toast(self, message: str, toast_type: str = TYPE_INFO, duration: int = 3000):
        """显示Toast通知"""
        # 设置样式和图标
        self._apply_style(toast_type)
        self.message_label.setText(message)
        
        # 定位到屏幕右上角
        if self.parent():
            parent_rect = self.parent().geometry()
            self.move(parent_rect.right() - self.width() - 20, parent_rect.top() + 80)
        
        # 显示动画
        self.show()
        self.fade_in_animation.start()
        
        # 设置自动隐藏
        if duration > 0:
            self.auto_hide_timer.start(duration)
    
    def _apply_style(self, toast_type: str):
        """应用样式"""
        styles = {
            self.TYPE_INFO: {
                "bg_color": "rgba(0, 122, 255, 0.9)",
                "text_color": "white", 
                "icon": "ℹ️"
            },
            self.TYPE_SUCCESS: {
                "bg_color": "rgba(52, 199, 89, 0.9)",
                "text_color": "white",
                "icon": "✅"
            },
            self.TYPE_WARNING: {
                "bg_color": "rgba(255, 149, 0, 0.9)", 
                "text_color": "white",
                "icon": "⚠️"
            },
            self.TYPE_ERROR: {
                "bg_color": "rgba(255, 59, 48, 0.9)",
                "text_color": "white",
                "icon": "❌"
            }
        }
        
        style = styles.get(toast_type, styles[self.TYPE_INFO])
        
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {style['bg_color']};
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
            QLabel {{
                color: {style['text_color']};
                font-size: 14px;
                background: transparent;
            }}
        """)
        
        self.icon_label.setText(style['icon'])
    
    def hide_with_animation(self):
        """带动画的隐藏"""
        fade_out_animation = QPropertyAnimation(self.fade_in_effect, b"opacity")
        fade_out_animation.setDuration(200)
        fade_out_animation.setStartValue(1.0)
        fade_out_animation.setEndValue(0.0)
        fade_out_animation.finished.connect(self.hide)
        fade_out_animation.start()

class SmartInputSuggestions(QWidget):
    """智能输入建议组件"""
    
    suggestion_selected = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.suggestions_data = []
        self.setup_ui()
        self.load_common_suggestions()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowFlags(Qt.Popup)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setMaximumHeight(200)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(2)
        
        # 建议列表
        self.suggestions_list = QListWidget()
        self.suggestions_list.setStyleSheet("""
            QListWidget {
                background-color: rgba(248, 248, 248, 0.95);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                padding: 4px;
            }
            QListWidget::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
                color: #333;
                font-size: 14px;
            }
            QListWidget::item:hover {
                background-color: rgba(0, 122, 255, 0.1);
            }
            QListWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
        """)
        
        self.suggestions_list.itemClicked.connect(self._on_suggestion_clicked)
        layout.addWidget(self.suggestions_list)
    
    def load_common_suggestions(self):
        """加载常用建议"""
        self.common_suggestions = [
            "帮我分析一下这个问题",
            "请详细解释一下",
            "这是什么意思？",
            "如何解决这个问题？",
            "能举个例子吗？",
            "总结一下要点",
            "有什么建议吗？",
            "继续之前的话题",
            "换个角度思考",
            "请简化说明"
        ]
    
    def show_suggestions(self, input_text: str, position: QPoint):
        """显示建议列表"""
        suggestions = self._get_relevant_suggestions(input_text)
        
        if not suggestions:
            self.hide()
            return
        
        self.suggestions_list.clear()
        for suggestion in suggestions[:8]:  # 最多显示8个建议
            item = QListWidgetItem(suggestion)
            self.suggestions_list.addItem(item)
        
        # 调整位置和大小
        self.adjustSize()
        self.move(position)
        self.show()
    
    def _get_relevant_suggestions(self, input_text: str) -> List[str]:
        """获取相关建议"""
        if len(input_text.strip()) < 2:
            return self.common_suggestions[:5]
        
        # 简单的关键词匹配
        relevant = []
        input_lower = input_text.lower()
        
        for suggestion in self.common_suggestions:
            # 如果输入文本包含建议的关键词，或建议包含输入的关键词
            if (any(word in suggestion.lower() for word in input_lower.split()) or
                any(word in input_lower for word in suggestion.lower().split())):
                relevant.append(suggestion)
        
        # 如果没有相关建议，返回常用建议
        if not relevant:
            relevant = self.common_suggestions[:5]
        
        return relevant
    
    def _on_suggestion_clicked(self, item):
        """处理建议点击"""
        self.suggestion_selected.emit(item.text())
        self.hide()

class ProgressIndicator(QWidget):
    """进度指示器组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(40)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 8, 20, 8)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 6px;
                background-color: rgba(0, 0, 0, 0.1);
                text-align: center;
                height: 12px;
            }
            QProgressBar::chunk {
                border-radius: 6px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007AFF, stop:1 #5856D6);
            }
        """)
        
        # 状态标签
        self.status_label = QLabel("准备中...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                background: transparent;
            }
        """)
        
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
    
    def set_progress(self, value: int, status: str = ""):
        """设置进度"""
        self.progress_bar.setValue(value)
        if status:
            self.status_label.setText(status)
    
    def set_indeterminate(self, status: str = "处理中..."):
        """设置为不确定进度"""
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(0)
        self.status_label.setText(status)
    
    def reset(self):
        """重置进度条"""
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.status_label.setText("准备中...")

class QuickActionBar(QWidget):
    """快捷操作栏"""
    
    action_triggered = pyqtSignal(str, str)  # action_id, action_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.actions = {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(50)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(248, 248, 248, 0.9);
                border-top: 1px solid rgba(0, 0, 0, 0.1);
            }
        """)
        
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(12, 8, 12, 8)
        self.layout.setSpacing(8)
        self.layout.addStretch()  # 开始时添加弹性空间
    
    def add_action(self, action_id: str, icon: str, text: str, data: str = ""):
        """添加快捷操作"""
        if action_id in self.actions:
            return
        
        button = QPushButton(f"{icon} {text}")
        button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 122, 255, 0.1);
                border: 1px solid rgba(0, 122, 255, 0.3);
                border-radius: 16px;
                padding: 6px 12px;
                font-size: 12px;
                color: #007AFF;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: rgba(0, 122, 255, 0.2);
            }
            QPushButton:pressed {
                background-color: rgba(0, 122, 255, 0.3);
            }
        """)
        
        button.clicked.connect(lambda: self.action_triggered.emit(action_id, data))
        
        # 插入到弹性空间之前
        self.layout.insertWidget(self.layout.count() - 1, button)
        self.actions[action_id] = button
        
        self.show()
    
    def remove_action(self, action_id: str):
        """移除快捷操作"""
        if action_id in self.actions:
            button = self.actions[action_id]
            self.layout.removeWidget(button)
            button.deleteLater()
            del self.actions[action_id]
        
        # 如果没有操作了，隐藏整个栏
        if not self.actions:
            self.hide()
    
    def clear_actions(self):
        """清除所有操作"""
        for action_id in list(self.actions.keys()):
            self.remove_action(action_id)

class StatusBar(QWidget):
    """状态栏组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(30)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(248, 248, 248, 0.8);
                border-top: 1px solid rgba(0, 0, 0, 0.05);
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 4, 12, 4)
        layout.setSpacing(8)
        
        # 连接状态
        self.connection_label = QLabel("🟢 已连接")
        self.connection_label.setStyleSheet("""
            QLabel {
                color: #34C759;
                font-size: 11px;
                background: transparent;
            }
        """)
        
        # 模型信息
        self.model_label = QLabel("模型: GPT-4")
        self.model_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                background: transparent;
            }
        """)
        
        # 统计信息
        self.stats_label = QLabel("对话: 0")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                background: transparent;
            }
        """)
        
        layout.addWidget(self.connection_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.model_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.stats_label)
        layout.addStretch()
    
    def update_connection_status(self, connected: bool):
        """更新连接状态"""
        if connected:
            self.connection_label.setText("🟢 已连接")
            self.connection_label.setStyleSheet("QLabel { color: #34C759; font-size: 11px; }")
        else:
            self.connection_label.setText("🔴 断开连接")
            self.connection_label.setStyleSheet("QLabel { color: #FF3B30; font-size: 11px; }")
    
    def update_model_info(self, model_name: str):
        """更新模型信息"""
        self.model_label.setText(f"模型: {model_name}")
    
    def update_stats(self, conversation_count: int):
        """更新统计信息"""
        self.stats_label.setText(f"对话: {conversation_count}")