{"Version": 3, "Meta": {"PhysicsSettingCount": 97, "TotalInputCount": 313, "TotalOutputCount": 251, "VertexCount": 337, "Fps": 59, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "[幸]〇X"}, {"Id": "PhysicsSetting2", "Name": "[幸]〇Y"}, {"Id": "PhysicsSetting3", "Name": "[幸]〇Ybreath"}, {"Id": "PhysicsSetting4", "Name": "[幸]〇Z"}, {"Id": "PhysicsSetting5", "Name": "[幸]〇Z(Shoulder)"}, {"Id": "PhysicsSetting6", "Name": "〇BDX"}, {"Id": "PhysicsSetting7", "Name": "〇BDY"}, {"Id": "PhysicsSetting8", "Name": "〇BDZ"}, {"Id": "PhysicsSetting9", "Name": "〇HipX"}, {"Id": "PhysicsSetting10", "Name": "〇HipY"}, {"Id": "PhysicsSetting11", "Name": "〇HipZ"}, {"Id": "PhysicsSetting12", "Name": "[幸]〇PX"}, {"Id": "PhysicsSetting13", "Name": "[幸]〇PY"}, {"Id": "PhysicsSetting14", "Name": "ShoulderZ_ForwardCalculate"}, {"Id": "PhysicsSetting15", "Name": "ShoulderZ_BackwardCalculate"}, {"Id": "PhysicsSetting16", "Name": "ShoulderZ_SortCalculate"}, {"Id": "PhysicsSetting17", "Name": "ShoulderZ_InverseOutput"}, {"Id": "PhysicsSetting18", "Name": "HipZ_ForwardCalculate"}, {"Id": "PhysicsSetting19", "Name": "HipZ_BackwardCalculate"}, {"Id": "PhysicsSetting20", "Name": "HipZ_SortCalculate"}, {"Id": "PhysicsSetting21", "Name": "HipZ_InverseOutput"}, {"Id": "PhysicsSetting22", "Name": "HipX_ForwardCalculate"}, {"Id": "PhysicsSetting23", "Name": "HipX_BackwardCalculate"}, {"Id": "PhysicsSetting24", "Name": "HipX_SortCalculate"}, {"Id": "PhysicsSetting25", "Name": "HipX_InverseOutput"}, {"Id": "PhysicsSetting26", "Name": "ChestZ_ForwardCalculate"}, {"Id": "PhysicsSetting27", "Name": "ChestZ_BackwardCalculate"}, {"Id": "PhysicsSetting28", "Name": "ChestZ_SortCalculate"}, {"Id": "PhysicsSetting29", "Name": "ChestZ_InverseOutput"}, {"Id": "PhysicsSetting30", "Name": "ChestY_ForwardCalculate"}, {"Id": "PhysicsSetting31", "Name": "ChestY_BackwardCalculate"}, {"Id": "PhysicsSetting32", "Name": "ChestY_SortCalculate"}, {"Id": "PhysicsSetting33", "Name": "ChestY_InverseOutput"}, {"Id": "PhysicsSetting34", "Name": "ChestX_ForwardCalculate"}, {"Id": "PhysicsSetting35", "Name": "ChestX_BackwardCalculate"}, {"Id": "PhysicsSetting36", "Name": "ChestX_SortCalculate"}, {"Id": "PhysicsSetting37", "Name": "ChestX_InverseOutput"}, {"Id": "PhysicsSetting38", "Name": "ChestXG_Result"}, {"Id": "PhysicsSetting39", "Name": "ChestYG_Result"}, {"Id": "PhysicsSetting40", "Name": "ChestZG_Result"}, {"Id": "PhysicsSetting41", "Name": "HipXG_Result"}, {"Id": "PhysicsSetting42", "Name": "HipZG_Result"}, {"Id": "PhysicsSetting43", "Name": "ShoulderZG_Result"}, {"Id": "PhysicsSetting44", "Name": "eyex"}, {"Id": "PhysicsSetting45", "Name": "HeadXBS"}, {"Id": "PhysicsSetting46", "Name": "eyey"}, {"Id": "PhysicsSetting47", "Name": "眉-微笑"}, {"Id": "PhysicsSetting48", "Name": "口微笑"}, {"Id": "PhysicsSetting49", "Name": "口开合"}, {"Id": "PhysicsSetting50", "Name": "口微笑(2)"}, {"Id": "PhysicsSetting51", "Name": "口开合(2)"}, {"Id": "PhysicsSetting52", "Name": "左眼 表情"}, {"Id": "PhysicsSetting53", "Name": "右眼 表情"}, {"Id": "PhysicsSetting54", "Name": "左眼 眨眼 抖动"}, {"Id": "PhysicsSetting55", "Name": "右眼 眨眼 抖动"}, {"Id": "PhysicsSetting56", "Name": "左眼 高光"}, {"Id": "PhysicsSetting57", "Name": "右眼 高光"}, {"Id": "PhysicsSetting58", "Name": "CheeksWiggleX"}, {"Id": "PhysicsSetting59", "Name": "CheeksWiggleY"}, {"Id": "PhysicsSetting60", "Name": "左猫耳"}, {"Id": "PhysicsSetting61", "Name": "右猫耳"}, {"Id": "PhysicsSetting62", "Name": "前发 X"}, {"Id": "PhysicsSetting63", "Name": "前发 Y"}, {"Id": "PhysicsSetting64", "Name": "侧发 X"}, {"Id": "PhysicsSetting65", "Name": "侧发 Y"}, {"Id": "PhysicsSetting66", "Name": "后侧发 X"}, {"Id": "PhysicsSetting67", "Name": "后侧发 Y"}, {"Id": "PhysicsSetting68", "Name": "后发 X"}, {"Id": "PhysicsSetting69", "Name": "后发 X(2)"}, {"Id": "PhysicsSetting70", "Name": "后发 X(3)"}, {"Id": "PhysicsSetting71", "Name": "后发 Y"}, {"Id": "PhysicsSetting72", "Name": "胸 X"}, {"Id": "PhysicsSetting73", "Name": "胸 Y"}, {"Id": "PhysicsSetting74", "Name": "胸 X角度"}, {"Id": "PhysicsSetting75", "Name": "胳膊 X"}, {"Id": "PhysicsSetting76", "Name": "胳膊 Y"}, {"Id": "PhysicsSetting77", "Name": "袖子 X"}, {"Id": "PhysicsSetting78", "Name": "袖子 Y"}, {"Id": "PhysicsSetting79", "Name": "裙子 X1"}, {"Id": "PhysicsSetting80", "Name": "裙子 X2(2)"}, {"Id": "PhysicsSetting81", "Name": "裙子 X2(3)"}, {"Id": "PhysicsSetting82", "Name": "裙子 Y1"}, {"Id": "PhysicsSetting83", "Name": "头 配饰1 X"}, {"Id": "PhysicsSetting84", "Name": "头 配饰1 Y"}, {"Id": "PhysicsSetting85", "Name": "头 配饰2 X"}, {"Id": "PhysicsSetting86", "Name": "头 配饰2 Y"}, {"Id": "PhysicsSetting87", "Name": "头 配饰3 X"}, {"Id": "PhysicsSetting88", "Name": "头 配饰3 Y"}, {"Id": "PhysicsSetting89", "Name": "衣服 X1"}, {"Id": "PhysicsSetting90", "Name": "衣服 X2"}, {"Id": "PhysicsSetting91", "Name": "衣服 Y1"}, {"Id": "PhysicsSetting92", "Name": "衣服 Y2"}, {"Id": "PhysicsSetting93", "Name": "上衣 配饰1 X"}, {"Id": "PhysicsSetting94", "Name": "上衣 配饰1 X(2)"}, {"Id": "PhysicsSetting95", "Name": "腰 配饰1 X"}, {"Id": "PhysicsSetting96", "Name": "腰 配饰1 Y"}, {"Id": "PhysicsSetting97", "Name": "舌头form"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HeadAngleX"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BSHeadAngleX"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HeadAngleYBS"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BSHeadAnglePuckerX"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 14.5}, "Mobility": 0.69, "Delay": 0.66, "Acceleration": 1.21, "Radius": 14.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "PositionY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HeadAngleY"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BSHeadAngleY"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BSHeadAnglePuckerY"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HeadAngleYBS"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ShoulderY"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.93, "Acceleration": 1.76, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.6, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 54, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HeadAngleY"}, "VertexIndex": 1, "Scale": 30, "Weight": 8, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6.9}, "Mobility": 0.44, "Delay": 0.87, "Acceleration": 2.3, "Radius": 6.9}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.07, "Delay": 0.55, "Acceleration": 2.04, "Radius": 3.1}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HeadAngleZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5.8}, "Mobility": 0.53, "Delay": 0.88, "Acceleration": 0.9, "Radius": 5.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ShoulderZ_input"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.59, "Delay": 0.95, "Acceleration": 3.03, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "PositionX"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 65, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestX_input"}, "VertexIndex": 1, "Scale": 34, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "PositionY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 14, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 3, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestY_input"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.4}, "Mobility": 0.85, "Delay": 0.38, "Acceleration": 3.28, "Radius": 9.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "PositionX"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestZ_input"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HipZ_input"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "PositionX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipX_input"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "PositionY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipAngleY"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HipAngleYDown2"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HipAngleYUp2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 16.6}, "Mobility": 0.92, "Delay": 0.41, "Acceleration": 1.86, "Radius": 16.6}, {"Position": {"X": 0, "Y": 26.6}, "Mobility": 0.81, "Delay": 0.49, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "PositionX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 68, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipZ_input"}, "VertexIndex": 1, "Scale": 30, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 18.3}, "Mobility": 0.87, "Delay": 0.67, "Acceleration": 0.9, "Radius": 18.3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "PositionX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "VertexIndex": 1, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "HipAngleYUp2"}, "VertexIndex": 2, "Scale": 30, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HipAngleYDown2"}, "VertexIndex": 3, "Scale": 30, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.4, "Delay": 0.8, "Acceleration": 3, "Radius": 10}, {"Position": {"X": 0, "Y": 16.5}, "Mobility": 0.76, "Delay": 0.76, "Acceleration": 1.46, "Radius": 6.5}, {"Position": {"X": 0, "Y": 27.1}, "Mobility": 0.73, "Delay": 0.7, "Acceleration": 0.96, "Radius": 10.6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57, "Default": 0, "Maximum": 57}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "PositionY"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param345"}, "Weight": 0, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "VertexIndex": 1, "Scale": 30, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 16.6}, "Mobility": 0.7, "Delay": 0.44, "Acceleration": 7.23, "Radius": 16.6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ShoulderZ_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderZ_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ShoulderZPhysicsRAM"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ShoulderZ_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderZ_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ShoulderZ_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ShoulderZPhysicsRAM"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderZ_Global"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ShoulderZPhysicsRAM"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": 0, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ShoulderZPhysicsRAM"}, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderZ_invertToggle"}, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ShoulderZ_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -172, "Default": 0, "Maximum": 172}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "HipZ_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipZ_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipZPhysicsRAM"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "HipZ_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipZ_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipZ_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "HipZPhysicsRAM"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipZ_Global"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipZPhysicsRAM"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": 0, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "HipZPhysicsRAM"}, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipZ_invertToggle"}, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipZ_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -172, "Default": 0, "Maximum": 172}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "HipX_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipX_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipXPhysicsRAM"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "HipX_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipX_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipX_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "HipXPhysicsRAM"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipX_Global"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipXPhysicsRAM"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": 0, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "HipXPhysicsRAM"}, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipX_invertToggle"}, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipX_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -172, "Default": 0, "Maximum": 172}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestZ_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestZ_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestZPhysicsRAM"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestZ_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestZ_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestZ_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestZPhysicsRAM"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestZ_Global"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestZPhysicsRAM"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": 0, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestZPhysicsRAM"}, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestZ_invertToggle"}, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestZ_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -172, "Default": 0, "Maximum": 172}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestY_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestY_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestYPhysicsRAM"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestY_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestY_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestY_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestYPhysicsRAM"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestY_Global"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestYPhysicsRAM"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": 0, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestYPhysicsRAM"}, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestY_invertToggle"}, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestY_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -172, "Default": 0, "Maximum": 172}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestX_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestX_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestXPhysicsRAM"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestX_input"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestX_invertToggle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestX_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestXPhysicsRAM"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestX_Global"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestXPhysicsRAM"}, "VertexIndex": 1, "Scale": 60, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": 0, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestXPhysicsRAM"}, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestX_invertToggle"}, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestX_Global"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -172, "Default": 0, "Maximum": 172}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestX_Global"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "BodyAngleX"}, "VertexIndex": 1, "Scale": 29.007, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmOpen"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 14.5}, "Mobility": 0.73, "Delay": 0.99, "Acceleration": 0.96, "Radius": 14.5}, {"Position": {"X": 0, "Y": 24.5}, "Mobility": 0.62, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestY_Global"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "BodyAngleY"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BodyAngleYBS"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 14.5}, "Mobility": 0.87, "Delay": 0.88, "Acceleration": 0.9, "Radius": 14.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ChestZ_Global"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "BodyAngleZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 14.5}, "Mobility": 0.87, "Delay": 0.88, "Acceleration": 0.9, "Radius": 14.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "HipX_Global"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipAngleX"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HipAngleXBS"}, "VertexIndex": 2, "Scale": 30, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.3, "Acceleration": 5, "Radius": 10}, {"Position": {"X": 0, "Y": 15.8}, "Mobility": 0.56, "Delay": 0.9, "Acceleration": 0.85, "Radius": 5.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "HipZ_Global"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HipAngleZ"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HipAngleYDown2"}, "VertexIndex": 2, "Scale": 30, "Weight": 66, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "HipAngleYUp2"}, "VertexIndex": 3, "Scale": 30, "Weight": 33, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 14.5}, "Mobility": 0.87, "Delay": 0.88, "Acceleration": 0.9, "Radius": 14.5}, {"Position": {"X": 0, "Y": 22.9}, "Mobility": 0.83, "Delay": 0.82, "Acceleration": 0.78, "Radius": 8.4}, {"Position": {"X": 0, "Y": 30.8}, "Mobility": 0.67, "Delay": 0.48, "Acceleration": 0.67, "Radius": 7.9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "ShoulderZ_Global"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ShoulderZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 0.8, "Acceleration": 3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.5, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting44", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6.7}, "Mobility": 0.4, "Delay": 3.4, "Acceleration": 8, "Radius": 6.7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -62, "Default": 0, "Maximum": 62}}}, {"Id": "PhysicsSetting45", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "BSHeadAngleX"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6.7}, "Mobility": 0.4, "Delay": 3.4, "Acceleration": 8, "Radius": 6.7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -62, "Default": 0, "Maximum": 62}}}, {"Id": "PhysicsSetting46", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6.7}, "Mobility": 0.4, "Delay": 0.95, "Acceleration": 8, "Radius": 6.7}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 30}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting47", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "EYESmile"}, "VertexIndex": 1, "Scale": 1, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.55, "Delay": 1.48, "Acceleration": 0.71, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting48", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 45, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRY"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "EyeSquintL"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "EYESmile"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LEyeBlinkA"}, "VertexIndex": 1, "Scale": 30, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "REyeBlinkA"}, "VertexIndex": 1, "Scale": 30, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6.4}, "Mobility": 0.39, "Delay": 1.75, "Acceleration": 2.85, "Radius": 6.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -58.2, "Default": -1.4, "Maximum": 78.2}}}, {"Id": "PhysicsSetting49", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "JawOpen"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ToothOpen"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.8}}}, {"Id": "PhysicsSetting50", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMouthForm2"}, "VertexIndex": 1, "Scale": 1.2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10.8}, "Mobility": 0.87, "Delay": 0.88, "Acceleration": 0.9, "Radius": 10.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -58.2, "Default": 0, "Maximum": 58.2}}}, {"Id": "PhysicsSetting51", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMouthOpenY2"}, "VertexIndex": 1, "Scale": 1.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.3}, "Mobility": 0.87, "Delay": 0.82, "Acceleration": 0.9, "Radius": 8.3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.8}}}, {"Id": "PhysicsSetting52", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 70, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "EYESmile"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 26, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 9, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LExpA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LExpB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.89, "Delay": 0.9, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting53", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 70, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "EYESmile"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRY"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 26, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 9, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "RExpA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "RExpB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.89, "Delay": 0.9, "Acceleration": 0.8, "Radius": 4}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.94, "Delay": 0.9, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting54", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLAngle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LEyeBlinkA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LEyeBlinkB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.7, "Radius": 6}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.72, "Delay": 1.13, "Acceleration": 1.14, "Radius": 8}, {"Position": {"X": 0, "Y": 13.4}, "Mobility": 0.59, "Delay": 1.06, "Acceleration": 1.15, "Radius": 5.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -54, "Default": 0, "Maximum": 54}}}, {"Id": "PhysicsSetting55", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRAngle"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "REyeBlinkA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "REyeBlinkB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.72, "Delay": 1.13, "Acceleration": 1.14, "Radius": 8}, {"Position": {"X": 0, "Y": 13.4}, "Mobility": 0.59, "Delay": 1.06, "Acceleration": 1.15, "Radius": 5.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -54, "Default": 0, "Maximum": 54}}}, {"Id": "PhysicsSetting56", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 12, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LHighlightA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "LHighlightB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LHighlightC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.96, "Delay": 0.8, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.96, "Delay": 0.8, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.86, "Delay": 0.8, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.73, "Delay": 0.8, "Acceleration": 1, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting57", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 12, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "RHighlightA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "RHighlightB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "RHighlightC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.96, "Delay": 0.8, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.96, "Delay": 0.8, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.86, "Delay": 0.8, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.73, "Delay": 0.8, "Acceleration": 1, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting58", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "CheeksWiggleX"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 4.4}, "Mobility": 0.91, "Delay": 0.78, "Acceleration": 1.15, "Radius": 4.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting59", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "CheeksWiggleY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.8, "Delay": 0.74, "Acceleration": 2.16, "Radius": 6}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting60", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 12, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 29, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "EarLA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "EarLB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "EarLC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 11.9}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 1.9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting61", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 12, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 29, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "EarRA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "EarRB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "EarRC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 11.9}, "Mobility": 0.7, "Delay": 0.7, "Acceleration": 3, "Radius": 1.9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting62", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "FrontHairXA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "FrontHairXB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "FrontHairXC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.86, "Delay": 0.9, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting63", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 18, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 72, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "FrontHairYA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "FrontHairYB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "FrontHairYC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.83, "Delay": 0.95, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.83, "Delay": 0.95, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.83, "Delay": 0.95, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.83, "Delay": 0.95, "Acceleration": 0.8, "Radius": 5}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.83, "Delay": 0.95, "Acceleration": 0.8, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting64", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 2, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 4, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 39, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "SideHairXA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SideHairXB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SideHairXC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SideHairXD"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.85, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting65", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 26, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 75, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "SideHairYA"}, "VertexIndex": 1, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SideHairYB"}, "VertexIndex": 2, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SideHairYC"}, "VertexIndex": 3, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SideHairYD"}, "VertexIndex": 4, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.84, "Delay": 0.8, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.84, "Delay": 0.8, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.84, "Delay": 0.8, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.81, "Delay": 0.8, "Acceleration": 1.86, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.81, "Delay": 0.8, "Acceleration": 1.01, "Radius": 6}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting66", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 17, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 13, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 3, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 50, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LateSideHairZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairXA"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairXB"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairXC"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairXD"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 5}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 30}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting67", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LateSideHairYA"}, "VertexIndex": 1, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairYB"}, "VertexIndex": 2, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairYC"}, "VertexIndex": 3, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateSideHairYD"}, "VertexIndex": 4, "Scale": 22, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 0.6, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.56, "Delay": 0.6, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.67, "Delay": 0.6, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.78, "Delay": 0.6, "Acceleration": 3, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.6, "Acceleration": 3, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -58, "Default": 0, "Maximum": 58}}}, {"Id": "PhysicsSetting68", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 38, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 3, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 6, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LateHairZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairXA"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairXB"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairXC"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairXD"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairXE"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.4, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting69", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 3, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 6, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 5, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 24, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 13, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh350"}, "VertexIndex": 1, "Scale": 45.008, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh350"}, "VertexIndex": 2, "Scale": 44.987, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh350"}, "VertexIndex": 3, "Scale": 45.014, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh350"}, "VertexIndex": 4, "Scale": 44.985, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh350"}, "VertexIndex": 5, "Scale": 45.021, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh351"}, "VertexIndex": 1, "Scale": 44.978, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh351"}, "VertexIndex": 2, "Scale": 45.028, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh351"}, "VertexIndex": 3, "Scale": 45.018, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh351"}, "VertexIndex": 4, "Scale": 44.968, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh351"}, "VertexIndex": 5, "Scale": 45.032, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh352"}, "VertexIndex": 1, "Scale": 44.952, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh352"}, "VertexIndex": 2, "Scale": 45.572, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh352"}, "VertexIndex": 3, "Scale": 44.977, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh352"}, "VertexIndex": 4, "Scale": 45.053, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh352"}, "VertexIndex": 5, "Scale": 45.241, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.4, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 6}], "Normalization": {"Position": {"Minimum": -24, "Default": 0, "Maximum": 24}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting70", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 25, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 3, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 6, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 5, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 24, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 13, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh349"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh349"}, "VertexIndex": 2, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh349"}, "VertexIndex": 3, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh349"}, "VertexIndex": 4, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh349"}, "VertexIndex": 5, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.4, "Radius": 6}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 100}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -24, "Default": 0, "Maximum": 24}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting71", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 13, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 8, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 43, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "LateHairYA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairYB"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairYC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "LateHairYD"}, "VertexIndex": 4, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "LateHairYE"}, "VertexIndex": 5, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.73, "Delay": 0.9, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.7, "Delay": 0.98, "Acceleration": 0.86, "Radius": 3}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.75, "Delay": 0.95, "Acceleration": 0.94, "Radius": 4}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.78, "Delay": 0.91, "Acceleration": 0.99, "Radius": 5}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.81, "Delay": 0.9, "Acceleration": 1.12, "Radius": 6}, {"Position": {"X": 0, "Y": 21}, "Mobility": 0.95, "Delay": 0.84, "Acceleration": 1.22, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting72", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 95, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 5, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestXA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ChestXB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ChestXC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.83, "Delay": 0.9, "Acceleration": 0.7, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.83, "Delay": 0.9, "Acceleration": 0.7, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.83, "Delay": 0.9, "Acceleration": 0.7, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.83, "Delay": 0.9, "Acceleration": 0.7, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting73", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 20, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ChestYA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ChestYB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ChestYC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.83, "Delay": 0.6, "Acceleration": 3, "Radius": 3}, {"Position": {"X": 0, "Y": 4}, "Mobility": 0.83, "Delay": 0.6, "Acceleration": 3, "Radius": 4}, {"Position": {"X": 0, "Y": 7.1}, "Mobility": 0.83, "Delay": 0.6, "Acceleration": 3, "Radius": 3.1}, {"Position": {"X": 0, "Y": 9.1}, "Mobility": 0.92, "Delay": 0.54, "Acceleration": 3.29, "Radius": 2}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting74", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh403"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh403"}, "VertexIndex": 2, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh403"}, "VertexIndex": 3, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.9, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.9, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting75", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 67, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "PositionX"}, "Weight": 3, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderZ"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 24, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ArmOpen"}, "VertexIndex": 1, "Scale": 1, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmAngleX1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmAngleX2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmAngleX3"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmAngleX4"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.6, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting76", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestYA"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 15, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ArmAngleY1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmAngleY2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ArmAngleY3"}, "VertexIndex": 3, "Scale": 1, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.96, "Delay": 0.6, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.96, "Delay": 0.6, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.96, "Delay": 0.6, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.96, "Delay": 0.6, "Acceleration": 1, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting77", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 62, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 19, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "SleeveXA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SleeveXB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SleeveXC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh448"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh448"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh448"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh452"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh452"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh452"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh452"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.78, "Delay": 1, "Acceleration": 1, "Radius": 7}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -57.6, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting78", "Input": [{"Source": {"Target": "Parameter", "Id": "ShoulderY"}, "Weight": 5, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderYy"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 5, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ArmAngleY3"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "SleeveYA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SleeveYB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SleeveYC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 1, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.7, "Delay": 1, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.7, "Delay": 1, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.7, "Delay": 1, "Acceleration": 1.2, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.6, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting79", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 26, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "SkirtXA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SkirtXB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SkirtXC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}, {"Position": {"X": 0, "Y": 4}, "Mobility": 0.84, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.84, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.84, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.84, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.84, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.84, "Delay": 0.83, "Acceleration": 1.5, "Radius": 4}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting80", "Input": [{"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 13, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleXBS"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleXBS"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 26, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.75, "Delay": 0.7, "Acceleration": 1.4, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting81", "Input": [{"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 5, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 13, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleXBS"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HipAngleXBS"}, "Weight": 10, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.75, "Delay": 0.7, "Acceleration": 1.4, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 12}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting82", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "SkirtYA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SkirtYB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "SkirtYC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.7, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 6.1}, "Mobility": 0.83, "Delay": 0.9, "Acceleration": 0.6, "Radius": 6.1}, {"Position": {"X": 0, "Y": 11.5}, "Mobility": 0.83, "Delay": 0.95, "Acceleration": 0.55, "Radius": 5.4}, {"Position": {"X": 0, "Y": 15.6}, "Mobility": 0.83, "Delay": 0.96, "Acceleration": 0.5, "Radius": 4.1}, {"Position": {"X": 0, "Y": 18.4}, "Mobility": 0.83, "Delay": 0.94, "Acceleration": 0.43, "Radius": 2.8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting83", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HOrnament1XA"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament1XB"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament1XC"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.85, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.88, "Delay": 0.85, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.88, "Delay": 0.85, "Acceleration": 1.2, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 1.2, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting84", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HOrnament1YA"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament1YB"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting85", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 8, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleXBS"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 2, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HOrnament2XA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament2XB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament2XC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy6"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy7"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy8"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.94, "Delay": 0.8, "Acceleration": 1.4, "Radius": 0}, {"Position": {"X": 0, "Y": 3.5}, "Mobility": 0.94, "Delay": 0.95, "Acceleration": 1.4, "Radius": 3.5}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.94, "Delay": 0.92, "Acceleration": 1.28, "Radius": 3.5}, {"Position": {"X": 0, "Y": 10.5}, "Mobility": 0.94, "Delay": 0.82, "Acceleration": 1.2, "Radius": 3.5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting86", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestYA"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HOrnament2YA"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament2YB"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament2YC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting87", "Input": [{"Source": {"Target": "Parameter", "Id": "HeadAngleX"}, "Weight": 85, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 15, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamLilyPy"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy5"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy7"}, "VertexIndex": 2, "Scale": 30, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy8"}, "VertexIndex": 3, "Scale": 30, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy11"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy12"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamLilyPy13"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.73, "Delay": 0.93, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.73, "Delay": 0.93, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.73, "Delay": 0.89, "Acceleration": 0.97, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.84, "Delay": 0.78, "Acceleration": 0.98, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.92, "Delay": 0.71, "Acceleration": 1, "Radius": 6}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.96, "Delay": 0.84, "Acceleration": 1.02, "Radius": 6}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.8, "Delay": 0.93, "Acceleration": 1.33, "Radius": 6}], "Normalization": {"Position": {"Minimum": -8, "Default": 0, "Maximum": 8}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}, {"Id": "PhysicsSetting88", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "HeadAngleY"}, "Weight": 80, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "HOrnament3YA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament3YB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament3YC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "HOrnament3YD"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.73, "Delay": 0.9, "Acceleration": 2, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.73, "Delay": 0.9, "Acceleration": 2, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.73, "Delay": 0.9, "Acceleration": 2, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.73, "Delay": 0.9, "Acceleration": 2, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.73, "Delay": 0.9, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting89", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleZ"}, "Weight": 16, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 19, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ChestXA"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ClothesXA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesXB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesXC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 3}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.8, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.6, "Default": 0, "Maximum": 57.5}}}, {"Id": "PhysicsSetting90", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ClothesX1"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesX2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesX3"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting91", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ShoulderY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "BodyAngleYBS"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 5, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param348"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ClothesYA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesYB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesYC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.8, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.8, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.91, "Delay": 0.8, "Acceleration": 0.8, "Radius": 5}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.8, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting92", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ClothesY1"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesY2"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ClothesY3"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.85, "Acceleration": 0.85, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting93", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "BOrnament3XA"}, "VertexIndex": 1, "Scale": 30, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BOrnament3XB"}, "VertexIndex": 2, "Scale": 30, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BOrnament3XC"}, "VertexIndex": 3, "Scale": 30, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BOrnament3XD"}, "VertexIndex": 4, "Scale": 30, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 4}, {"Position": {"X": 0, "Y": 4}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 4}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 4}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 4}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 4}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.7, "Delay": 1, "Acceleration": 2, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting94", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 78, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "BOrnament3XA"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BOrnament3XB"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BOrnament3XC"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "BOrnament3XD"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 3, "Radius": 7}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 3, "Radius": 7}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 3, "Radius": 7}, {"Position": {"X": 0, "Y": 21}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 3, "Radius": 7}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 3, "Radius": 7}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.95, "Delay": 0.6, "Acceleration": 3, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting95", "Input": [{"Source": {"Target": "Parameter", "Id": "BodyAngleX"}, "Weight": 17, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRZBS"}, "Weight": 55, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "WOrnament1XA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament1XB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament1YA"}, "VertexIndex": 3, "Scale": 30, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament1YB"}, "VertexIndex": 4, "Scale": 30, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament2XA"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament2XB"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.7, "Delay": 0.8, "Acceleration": 2, "Radius": 5}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 30}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting96", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "BodyAngleY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "HipAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleRY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "WOrnament1YA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament1YB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament2YA"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament2YB"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "WOrnament2YC"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -35, "Default": 0, "Maximum": 35}}}, {"Id": "PhysicsSetting97", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamMouthForm3"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.1, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -57.3, "Default": 0, "Maximum": 57.3}}}]}