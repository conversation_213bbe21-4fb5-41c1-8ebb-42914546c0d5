{"Version": 3, "Meta": {"Duration": 5.15, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 36, "TotalSegmentCount": 191, "TotalPointCount": 479, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 1.317, 30, 0, 2.6, 60, 0, 3.85, 90, 0, 5.15, 120]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, -15, 0, 0.683, 0, 1, 1.111, 11.882, 1.539, 19.935, 1.967, 30, 0, 3.233, 60, 0, 4.5, 90, 0, 5.15, 105]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 5.15, 30]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 5.15, 30]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 1, 0.567, 0, 1.133, 16.844, 1.7, 30, 1, 2.272, 43.285, 2.844, 46.754, 3.417, 60, 0, 5.15, 90]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.189, 0, 0.378, -30, 0.567, -30, 1, 0.789, -30, 1.011, 30, 1.233, 30, 1, 1.461, 30, 1.689, -30, 1.917, -30, 1, 2.139, -30, 2.361, 30, 2.583, 30, 1, 2.811, 30, 3.039, -30, 3.267, -30, 1, 3.483, -30, 3.7, 0, 3.917, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 0.15, 0, 1, 0.344, 0, 0.539, -30, 0.733, -30, 1, 0.961, -30, 1.189, 30, 1.417, 30, 1, 1.644, 30, 1.872, -30, 2.1, -30, 1, 2.322, -30, 2.544, 30, 2.767, 30, 1, 2.994, 30, 3.222, -30, 3.45, -30, 1, 3.672, -30, 3.894, 0, 4.117, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 0, 0.3, 0, 1, 0.506, 0, 0.711, -30, 0.917, -30, 1, 1.144, -30, 1.372, 30, 1.6, 30, 1, 1.828, 30, 2.056, -30, 2.283, -30, 1, 2.506, -30, 2.728, 30, 2.95, 30, 1, 3.178, 30, 3.406, -30, 3.633, -30, 1, 3.856, -30, 4.078, 0, 4.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 0.45, 0, 1, 0.667, 0, 0.883, -30, 1.1, -30, 1, 1.317, -30, 1.533, 30, 1.75, 30, 1, 1.994, 30, 2.239, -30, 2.483, -30, 1, 2.689, -30, 2.894, 30, 3.1, 30, 1, 3.344, 30, 3.589, -30, 3.833, -30, 1, 4.05, -30, 4.267, 0, 4.483, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 0.65, 0, 1, 0.861, 0, 1.072, -30, 1.283, -30, 1, 1.494, -30, 1.706, 30, 1.917, 30, 1, 2.167, 30, 2.417, -30, 2.667, -30, 1, 2.867, -30, 3.067, 30, 3.267, 30, 1, 3.522, 30, 3.778, -30, 4.033, -30, 1, 4.25, -30, 4.467, 0, 4.683, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 0.583, 0, 1, 0.794, 0, 1.006, 30, 1.217, 30, 1, 1.422, 30, 1.628, -30, 1.833, -30, 1, 2.011, -30, 2.189, 30, 2.367, 30, 1, 2.522, 30, 2.678, -30, 2.833, -30, 1, 2.989, -30, 3.144, 0, 3.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 0.583, 0, 1, 0.794, 0, 1.006, 30, 1.217, 30, 1, 1.422, 30, 1.628, -30, 1.833, -30, 1, 2.011, -30, 2.189, 30, 2.367, 30, 1, 2.522, 30, 2.678, -30, 2.833, -30, 1, 2.989, -30, 3.144, 0, 3.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 0.583, 0, 1, 0.794, 0, 1.006, 30, 1.217, 30, 1, 1.422, 30, 1.628, -30, 1.833, -30, 1, 2.011, -30, 2.189, 30, 2.367, 30, 1, 2.522, 30, 2.678, -30, 2.833, -30, 1, 2.989, -30, 3.144, 0, 3.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 0.583, 0, 1, 0.794, 0, 1.006, 30, 1.217, 30, 1, 1.422, 30, 1.628, -30, 1.833, -30, 1, 2.011, -30, 2.189, 30, 2.367, 30, 1, 2.522, 30, 2.678, -30, 2.833, -30, 1, 2.989, -30, 3.144, 0, 3.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 0.583, 0, 1, 0.794, 0, 1.006, 30, 1.217, 30, 1, 1.422, 30, 1.628, -30, 1.833, -30, 1, 2.011, -30, 2.189, 30, 2.367, 30, 1, 2.522, 30, 2.678, -30, 2.833, -30, 1, 2.989, -30, 3.144, 0, 3.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 0.583, 0, 1, 0.95, 0, 1.317, 15, 1.683, 15, 1, 2.317, 15, 2.95, 15, 3.583, 15, 1, 3.917, 15, 4.25, 30, 4.583, 30, 0, 5.15, 30]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -30, 1, 0.5, -30, 1, 30, 1.5, 30, 1, 1.828, 30, 2.156, 30, 2.483, 30, 1, 2.978, 30, 3.472, -30, 3.967, -30, 0, 5.15, -30]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 1.083, 0, 1, 1.617, 0, 2.15, 30, 2.683, 30, 1, 3.222, 30, 3.761, 0, 4.3, 0, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -30, 1, 0.428, -30, 0.856, 30, 1.283, 30, 1, 1.694, 30, 2.106, -30, 2.517, -30, 1, 2.95, -30, 3.383, 30, 3.817, 30, 1, 4.261, 30, 4.706, -30, 5.15, -30]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 0, 1.35, 0, 0, 3.867, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.817, 0, 1.633, 40, 2.45, 40, 1, 2.539, 40, 2.628, 40, 2.717, 40, 1, 3.456, 40, 4.194, 80, 4.933, 80, 0, 5.15, 80]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 0.3, 0, 0, 4.167, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 1.967, 0, 0, 3.75, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 0.95, 0, 0, 3, 40, 0, 4.683, 80, 0, 5.15, 80]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 2.217, 0, 0, 4.5, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 2.167, 30, 0, 5.15, 30]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 0, 2.333, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 2.567, 0, 0, 5.117, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 0, 3.75, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 0, 1.15, 0, 0, 4.817, 40, 0, 5.15, 40]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 1, 0.589, 0, 1.178, 30, 1.767, 30, 1, 2.228, 30, 2.689, 30, 3.15, 30, 1, 3.739, 30, 4.328, 60, 4.917, 60, 0, 5.15, 60]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, -5.775, 1, 0.006, -5.775, 0.011, -5.778, 0.017, -5.778, 1, 0.572, -5.778, 1.128, 5.84, 1.683, 5.84, 1, 2.144, 5.84, 2.606, -6.149, 3.067, -6.149, 1, 3.656, -6.149, 4.244, 5.85, 4.833, 5.85, 1, 4.939, 5.85, 5.044, -5.775, 5.15, -5.775]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 1.307, 1, 0.072, 1.307, 0.144, -3.619, 0.217, -3.619, 1, 0.344, -3.619, 0.472, 0.901, 0.6, 0.901, 1, 0.722, 0.901, 0.844, -1.222, 0.967, -1.222, 1, 1.111, -1.222, 1.256, 0.243, 1.4, 0.243, 1, 1.472, 0.243, 1.544, 0.082, 1.617, 0.082, 1, 1.789, 0.082, 1.961, 1.414, 2.133, 1.414, 1, 2.278, 1.414, 2.422, 0.202, 2.567, 0.202, 1, 2.633, 0.202, 2.7, 0.356, 2.767, 0.356, 1, 2.944, 0.356, 3.122, -1.103, 3.3, -1.103, 1, 3.422, -1.103, 3.544, -0.153, 3.667, -0.153, 1, 3.778, -0.153, 3.889, -0.826, 4, -0.826, 1, 4.172, -0.826, 4.344, -0.032, 4.517, -0.032, 1, 4.522, -0.032, 4.528, -0.032, 4.533, -0.032, 1, 4.544, -0.032, 4.556, -0.033, 4.567, -0.033, 1, 4.761, -0.033, 4.956, 1.307, 5.15, 1.307]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh75", "Segments": [0, 0, 1, 0.5, 0, 1, -5.485, 1.5, -5.485, 1, 1.639, -5.485, 1.778, -3.329, 1.917, -3.329, 1, 1.928, -3.329, 1.939, -3.335, 1.95, -3.335, 1, 1.956, -3.335, 1.961, -3.305, 1.967, -3.305, 1, 1.978, -3.305, 1.989, -3.311, 2, -3.311, 1, 2.344, -3.311, 2.689, 4.325, 3.033, 4.325, 1, 3.122, 4.325, 3.211, 4.024, 3.3, 4.024, 1, 3.306, 4.024, 3.311, 4.038, 3.317, 4.038, 1, 3.928, 4.038, 4.539, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh75", "Segments": [0, 0, 1, 0.322, 0, 0.644, 0.015, 0.967, 0.015, 1, 1.006, 0.015, 1.044, 0.009, 1.083, 0.009, 1, 1.161, 0.009, 1.239, 1.649, 1.317, 1.649, 1, 1.433, 1.649, 1.55, -1.885, 1.667, -1.885, 1, 1.778, -1.885, 1.889, 1.058, 2, 1.058, 1, 2.128, 1.058, 2.256, -1.136, 2.383, -1.136, 1, 2.617, -1.136, 2.85, 0.349, 3.083, 0.349, 1, 3.089, 0.349, 3.094, 0.345, 3.1, 0.345, 1, 3.106, 0.345, 3.111, 0.346, 3.117, 0.346, 1, 3.217, 0.346, 3.317, -0.136, 3.417, -0.136, 1, 3.422, -0.136, 3.428, -0.134, 3.433, -0.134, 1, 3.439, -0.134, 3.444, -0.14, 3.45, -0.14, 1, 4.017, -0.14, 4.583, 0, 5.15, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh75", "Segments": [0, 0, 1, 0.489, 0, 0.978, 1.238, 1.467, 1.238, 1, 1.583, 1.238, 1.7, -2.049, 1.817, -2.049, 1, 1.939, -2.049, 2.061, 1.7, 2.183, 1.7, 1, 2.306, 1.7, 2.428, -1.729, 2.55, -1.729, 1, 2.678, -1.729, 2.806, 0.792, 2.933, 0.792, 1, 3.106, 0.792, 3.278, -0.136, 3.45, -0.136, 1, 3.456, -0.136, 3.461, -0.136, 3.467, -0.136, 1, 4.028, -0.136, 4.589, 0, 5.15, 0]}]}