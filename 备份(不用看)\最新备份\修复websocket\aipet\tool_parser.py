# ai_桌宠/tool_parser.py
import re
import json
from typing import Dict, Any, Optional, List, Tuple

class ToolCallParser:
    """
    工具调用解析器。
    能够从文本中解析出由AI生成的工具调用请求。
    """

    def __init__(self):
        # 正则表达式模式列表，用于匹配不同格式的工具调用
        # 顺序很重要，更具体或更优先的模式应该放在前面
        self.patterns = [
            # 格式1: [TOOL:tool_name] {"arg1": "value1", "arg2": "value2"}
            # 匹配工具名（可以包含点，字母数字下划线）和 JSON 对象参数
            # JSON 对象匹配：大括号内可以是任意字符，非贪婪匹配，直到遇到最后一个 '}'
            # 这个模式相对宽松，假设参数部分是合法的JSON对象。
            (r'\[TOOL:\s*([a-zA-Z0-9_.]+)\s*\]\s*(\{.*?\})', self._parse_json_args),

            # 格式2: <<<[TOOL_REQUEST]>>> tool_name:{"args": "values"} <<<[END_TOOL_REQUEST]>>>
            # 匹配工具名和冒号后的JSON对象参数
            (r'<<<\[TOOL_REQUEST\]>>>\s*([a-zA-Z0-9_.]+)\s*:\s*(\{.*?\})\s*<<<\[END_TOOL_REQUEST\]>>>', self._parse_json_args),

            # 格式3: 函数调用格式 tool_name({"arg_name": "arg_value"})
            # 匹配函数名（字母数字下划线，可带点）和括号内包裹的JSON对象参数
            # 这个模式假设参数总是一个单独的JSON对象。
            (r'([a-zA-Z_][a-zA-Z0-9_.]*)\s*\(\s*(\{.*?\})\s*\)', self._parse_json_args),
            
            # 格式4: (备用/简化) [TOOL:tool_name] (不含参数，或参数后续处理)
            # (r'\[TOOL:\s*([a-zA-Z0-9_.]+)\s*\]', lambda arg_str: {}), # 如果允许无参数调用

            # 格式5: XML/HTML 风格的工具调用 (示例，需要根据实际AI输出调整)
            # <tool_call name="tool.name"><param name="arg1">value1</param></tool_call>
            # 这个比较复杂，暂时不直接用简单正则实现，可以考虑后续用XML解析库
        ]
        # 注意：上述JSON参数的正则 `(\{.*?\})` 是一个简化版本，它会匹配从第一个 '{' 到最后一个 '}' 的内容（非贪婪）。
        # 对于嵌套的JSON或包含特殊字符的JSON字符串，它可能不够健壮。
        # 一个更健壮的方法是先粗略匹配，然后用json.loads尝试解析，如果失败则认为不是有效的工具调用。

    def _parse_json_args(self, args_str: str) -> Optional[Dict[str, Any]]:
        """尝试将参数字符串解析为JSON字典。"""
        try:
            # 移除可能的外部引号（如果AI有时会这样生成）
            # args_str = args_str.strip()
            # if args_str.startswith("'") and args_str.endswith("'"):
            #     args_str = args_str[1:-1]
            # if args_str.startswith('"') and args_str.endswith('"'):
            #    args_str = args_str[1:-1]
            
            # AI有时可能生成不标准的JSON，如使用单引号或尾随逗号。
            # 简单的替换可能有助于处理一些常见情况，但不是万能的。
            # args_str_cleaned = args_str.replace("'", '"')
            # 移除尾随逗号的逻辑会更复杂。

            args = json.loads(args_str)
            if isinstance(args, dict):
                return args
            else:
                # 如果解析结果不是字典，但AI可能把参数直接作为值（如 tool_name("value") 而非 tool_name({"param":"value"}))
                # 这种情况下，我们需要约定一个默认的参数名，例如 "value" 或 "input"
                # print(f"ToolCallParser: 参数解析结果不是字典: {args_str} -> {args}. 可能需要包装。")
                return {"_raw_value": args} # 或者返回 None，强制要求参数为字典
        except json.JSONDecodeError as e:
            print(f"ToolCallParser: 解析工具参数JSON失败: '{args_str}'. 错误: {e}")
            # 可以尝试一些修复，例如处理AI可能生成的Python字典格式 (eval不安全，不推荐)
            # 或者更复杂的JSON修复逻辑。
        return None


    def parse_tool_calls(self, text: str) -> List[Tuple[str, Dict[str, Any]]]:
        """
        从给定文本中解析出所有符合预设格式的工具调用。
        返回一个列表，每个元素是一个元组 (tool_name, arguments_dict)。
        """
        tool_calls: List[Tuple[str, Dict[str, Any]]] = []
        processed_spans = [] # 用于记录已处理的文本范围，避免重复解析

        # 对文本进行迭代，允许多次匹配不同模式
        # 但要注意，一个成功的匹配应该消耗掉对应的文本，防止被后续模式再次匹配
        # 简单的做法是，一旦一个模式成功匹配并提取了工具调用，就从文本中移除这部分，
        # 或者标记这部分已被处理。
        # 为了简单起见，我们先假设工具调用之间不会互相嵌套或严重重叠。

        remaining_text = text
        for pattern_regex, args_parser_func in self.patterns:
            # 使用 finditer 来获取所有不重叠的匹配项及其位置
            for match in re.finditer(pattern_regex, remaining_text, re.MULTILINE | re.DOTALL):
                # 检查此匹配是否与已处理的区域重叠
                current_span = match.span()
                is_overlapping = False
                for proc_span in processed_spans:
                    if max(proc_span[0], current_span[0]) < min(proc_span[1], current_span[1]):
                        is_overlapping = True
                        break
                if is_overlapping:
                    continue # 跳过已处理区域内的匹配

                tool_name_match_group_index = 1 # 通常工具名在第一个捕获组
                args_match_group_index = 2    # 参数字符串在第二个捕获组

                tool_name = match.group(tool_name_match_group_index).strip()
                
                args_str = ""
                if len(match.groups()) >= args_match_group_index:
                    args_str = match.group(args_match_group_index).strip()
                
                if not args_str and args_parser_func == self._parse_json_args: # 如果期望JSON但为空
                    arguments = {} # 默认空参数
                elif args_str:
                    arguments = args_parser_func(args_str)
                else: # args_str为空，且args_parser_func可能不处理空字符串（如特定lambda）
                    arguments = {}


                if arguments is not None: # args_parser_func 返回 None 表示解析失败
                    tool_calls.append((tool_name, arguments))
                    processed_spans.append(current_span)
                    # print(f"ToolCallParser: 解析到工具调用: {tool_name}, 参数: {arguments}, 范围: {current_span}")
                # else:
                    # print(f"ToolCallParser: 参数解析失败或不符合要求: {tool_name} with '{args_str}'")
        
        # 如果需要按出现顺序排序（如果不同模式的匹配顺序导致乱序）
        # tool_calls.sort(key=lambda tc: text.find(tc[0])) # 简单按工具名首次出现位置排序
        # 上述 processed_spans 的方法应该能大致保证顺序，但如果模式复杂，可能需要更精确的排序。

        if tool_calls:
            print(f"ToolCallParser: 从文本中解析到以下工具调用: {tool_calls}")
        return tool_calls

    def has_tool_calls(self, text: str) -> bool:
        """检查文本是否包含任何可解析的工具调用。"""
        # 只需要找到第一个匹配即可
        for pattern_regex, _ in self.patterns:
            if re.search(pattern_regex, text, re.MULTILINE | re.DOTALL):
                # 为了更精确，可以尝试完整解析一次
                if self.parse_tool_calls(text): # 如果能解析出至少一个有效的
                    return True
        return False

    def remove_tool_calls(self, text: str) -> str:
        """
        从文本中移除所有已识别的工具调用标记及其参数。
        返回清理后的文本。
        """
        result = text
        # 移除时需要小心，确保只移除被成功解析为工具调用的部分
        # 一个简单的方法是迭代地查找和替换，但这可能不完美
        # 更准确的方法是基于 parse_tool_calls 识别出的具体匹配范围来移除

        # 迭代替换，直到没有更多匹配（可能效率不高，但简单）
        # 重要的是，替换的顺序和内容要准确，避免破坏未被调用的文本
        
        # 获取所有工具调用的原始文本匹配
        all_raw_matches = []
        for pattern_regex, _ in self.patterns:
            for match in re.finditer(pattern_regex, result, re.MULTILINE | re.DOTALL):
                # 验证这是否是一个可以成功解析的工具调用
                tool_name = match.group(1).strip()
                args_str = match.group(2).strip() if len(match.groups()) >= 2 else ""
                
                args_parser_func = self._parse_json_args # 假设都是JSON解析
                if self.patterns[self.patterns.index((pattern_regex, _))][1] : # 获取对应的解析函数
                   args_parser_func = self.patterns[self.patterns.index((pattern_regex, _))][1]

                parsed_args = args_parser_func(args_str) if args_str else {} # 空参数也尝试解析或返回空字典
                
                if parsed_args is not None: # 只有成功解析的才算
                    all_raw_matches.append((match.group(0), match.start())) # (原始匹配文本, 开始位置)
        
        # 按出现位置倒序排序，这样移除时不会影响后续匹配的索引
        all_raw_matches.sort(key=lambda x: x[1], reverse=True)
        
        cleaned_text = list(text) # 将文本转为列表以便按索引操作
        
        # 创建一个标记数组，标记哪些字符应该被移除
        remove_mask = [False] * len(text)
        for raw_match_text, start_pos in all_raw_matches:
            end_pos = start_pos + len(raw_match_text)
            # 标记这个范围
            is_already_masked_in_part = False
            for i in range(start_pos, end_pos):
                if remove_mask[i]:
                    is_already_masked_in_part = True
                    break
            if not is_already_masked_in_part: # 如果这个范围没有被更早（更大）的匹配覆盖
                for i in range(start_pos, end_pos):
                    remove_mask[i] = True

        # 构建新字符串
        final_text_parts = []
        for i, char in enumerate(text):
            if not remove_mask[i]:
                final_text_parts.append(char)
        
        result = "".join(final_text_parts)

        # 移除可能产生的多余空行或前后空格
        lines = result.splitlines()
        cleaned_lines = [line.strip() for line in lines if line.strip()]
        return "\n".join(cleaned_lines)
if __name__ == '__main__':
    parser = ToolCallParser()
    test_texts = [
        "你好啊！我想让你帮我 [TOOL:filesystem.read_file] {\"path\": \"data/test.txt\", \"encoding\": \"utf-8\"} 这个文件。",
        "请先 <<<[TOOL_REQUEST]>>> browser.navigate:{\"url\": \"https://example.com\"} <<<[END_TOOL_REQUEST]>>> 然后告诉我页面标题。",
        "计算一下 add({\"a\": 1, \"b\": 2.5}) 的结果。",
        "混合调用：[TOOL:tool.one] {\"p1\": \"v1\"} 接着是普通文本，然后 another_tool({\"key\": \"value\"})。",
        "这是一个没有工具调用的句子。",
        "错误的参数格式：[TOOL:bad.args] {path: 'no_quotes'}", # json.loads 会失败
        "嵌套（不标准，但测试parser）：[TOOL:outer.tool] {\"param_outer\": \"val_outer\", \"inner_call\": \"[TOOL:inner.tool] {\\\"param_inner\\\": \\\"val_inner\\\"}\"}",
        "AI说：[TOOL:filesystem.list_files] {\"directory\": \".\"} 然后 [TOOL:filesystem.create_file] {\"path\": \"new.txt\", \"content\": \"Hello\"}",
        "文本开始 [TOOL:test.start] {} 中间文本 [TOOL:test.end] {} 文本结束",
        "AAA <<<[TOOL_REQUEST]>>> tool.A:{\"a\":1} <<<[END_TOOL_REQUEST]>>> BBB func_B({\"b\":2}) CCC [TOOL:tool.C] {\"c\":3} DDD",
        "无参数调用: [TOOL:simple.action] {}"
    ]

    for i, text in enumerate(test_texts):
        print(f"\n--- 测试文本 {i+1} ---")
        print(f"原始文本: \"{text}\"")
        
        calls = parser.parse_tool_calls(text)
        if calls:
            print("解析到的工具调用:")
            for name, args in calls:
                print(f"  工具: {name}, 参数: {args}")
        else:
            print("未解析到工具调用。")
            
        print(f"是否包含工具调用: {parser.has_tool_calls(text)}")
        
        cleaned_text = parser.remove_tool_calls(text)
        print(f"清理后文本: \"{cleaned_text}\"")