import sys
import re
import html
import os
import json
from typing import Optional, Any, Dict, List
import threading
import requests
import tempfile
import uuid
import logging
from datetime import datetime
import configparser
from chat_commands import ChatCommandParser, TTSCommandHandler, CommandResult
# from sse_client import SSEClientWrapper
from websocket_client import VCPLogClient, VCPLogMessageHandler
from push_message_widget import CollapsiblePushMessageWidget
from PyQt5.QtCore import (Qt, pyqtSignal, QEvent, QObject, QSize, QUrl, QTimer, 
                          QPoint, QBuffer, QIODevice, QPropertyAnimation, QEasingCurve,
                          QRect, QParallelAnimationGroup, QSequentialAnimationGroup)
from PyQt5.QtGui import (QMouseEvent, QColor, QPalette, QPixmap, QImageReader, QImage,
                         QTextCursor, QTextImageFormat, QTextDocument, QFont,
                         QTextBlockFormat, QTextCharFormat, QKeyEvent, QBrush,
                         QFontMetrics, QPainter, QPainterPath, QLinearGradient,
                         QRadialGradient, QIcon, QKeySequence)
from PyQt5.QtCore import QRectF
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
                             QApplication, QFileDialog, QLabel, QInputDialog, QLineEdit,
                             QListWidget, QListWidgetItem, QDialog, QDialogButtonBox,
                             QSizePolicy, QMenu, QAction, QFrame, QScrollArea,
                             QGraphicsDropShadowEffect, QStackedWidget, QActionGroup,
                             QGraphicsOpacityEffect, QComboBox, QShortcut, QSpacerItem,
                             QToolButton, QProgressBar)
from PyQt5.QtMultimedia import QAudioInput, QAudioFormat, QAudioDeviceInfo, QAudio
import base64
import gc
import hashlib
import shutil
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_companion_config():
    """加载游戏伴侣配置"""
    config = {
        'interval_seconds': 70,  # 默认值
        'prompt': '[智能画面伴侣] 请观察屏幕内容并给出简洁的分析和建议。'  # 默认简短提示词
    }
    
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'companion_config.env')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析间隔时间
            import re
            interval_match = re.search(r'COMPANION_INTERVAL_SECONDS=(\d+)', content)
            if interval_match:
                config['interval_seconds'] = int(interval_match.group(1))
            
            # 解析提示词（提取三重引号之间的内容）
            prompt_match = re.search(r'COMPANION_PROMPT="""(.*?)"""', content, re.DOTALL)
            if prompt_match:
                config['prompt'] = prompt_match.group(1).strip()
                
        logger.info(f"游戏伴侣配置加载: 间隔{config['interval_seconds']}秒")
    except Exception as e:
        logger.warning(f"加载游戏伴侣配置失败，使用默认设置: {e}")
    
    return config

class ThemeManager:
    """优化的主题管理器"""
    
    THEMES = {
        "dark": {
            # 基础颜色
            "primary": "#007AFF",
            "primary_dark": "#0051D5", 
            "primary_light": "#5AC8FA",
            "secondary": "#5856D6",
            "success": "#34C759",
            "warning": "#FF9500",
            "error": "#FF3B30",
            
            # 背景颜色
            "window_bg": "rgba(28, 28, 30, 0.95)",
            "chat_bg": "rgba(44, 44, 46, 0.95)", 
            "input_bg": "rgba(58, 58, 60, 0.95)",
            "bubble_user_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #007AFF, stop:1 #5856D6)",
            "bubble_ai_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1C1C1E, stop:1 #2C2C2E)",
            "bubble_system_bg": "rgba(255, 204, 0, 0.15)",
            
            # 文本颜色
            "text_primary": "#FFFFFF",
            "text_secondary": "rgba(255, 255, 255, 0.7)",
            "text_tertiary": "rgba(255, 255, 255, 0.5)",
            "text_inverse": "#000000",
            
            # 边框和分割线
            "border_color": "rgba(255, 255, 255, 0.1)",
            "separator_color": "rgba(255, 255, 255, 0.05)",
            
            # 状态颜色
            "online_color": "#34C759",
            "typing_color": "#007AFF",
            
            # 图标
            "theme_icon": "☀️"
        },
        
        "light": {
            # 基础颜色
            "primary": "#007AFF",
            "primary_dark": "#0051D5",
            "primary_light": "#5AC8FA", 
            "secondary": "#5856D6",
            "success": "#34C759",
            "warning": "#FF9500",
            "error": "#FF3B30",
            
            # 背景颜色
            "window_bg": "rgba(255, 255, 255, 0.95)",
            "chat_bg": "rgba(248, 248, 248, 0.95)",
            "input_bg": "rgba(242, 242, 247, 0.95)",
            "bubble_user_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #007AFF, stop:1 #5856D6)",
            "bubble_ai_bg": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #F2F2F7, stop:1 #E5E5EA)",
            "bubble_system_bg": "rgba(255, 204, 0, 0.1)",
            
            # 文本颜色
            "text_primary": "#000000",
            "text_secondary": "rgba(0, 0, 0, 0.7)",
            "text_tertiary": "rgba(0, 0, 0, 0.5)",
            "text_inverse": "#FFFFFF",
            
            # 边框和分割线
            "border_color": "rgba(0, 0, 0, 0.1)",
            "separator_color": "rgba(0, 0, 0, 0.05)",
            
            # 状态颜色
            "online_color": "#34C759",
            "typing_color": "#007AFF",
            
            # 图标
            "theme_icon": "🌙"
        },
        
        "auto": {
            # 自动模式会根据系统主题动态切换
            "theme_icon": "🌗"
        }
    }
    
    def __init__(self):
        self.current_theme = "light"  # 默认浅色主题
        self._load_saved_theme()
        self.current_theme_config = None  # 添加当前主题缓存
        self.update_theme_cache() # 初始化时更新缓存
    
    def update_theme_cache(self):
        """更新主题缓存"""
        self.current_theme_config = self.THEMES.get(self.current_theme, self.THEMES["light"])

    def _load_saved_theme(self):
        """加载保存的主题设置"""
        try:
            theme_file = os.path.join(os.path.expanduser("~"), ".ai_pet_theme.json")
            if os.path.exists(theme_file):
                with open(theme_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.current_theme = data.get("theme", "light")
        except Exception as e:
            logger.warning(f"Failed to load theme settings: {e}")
    
    def _save_theme(self):
        """保存主题设置"""
        try:
            theme_file = os.path.join(os.path.expanduser("~"), ".ai_pet_theme.json")
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump({"theme": self.current_theme}, f)
        except Exception as e:
            logger.warning(f"Failed to save theme settings: {e}")
    
    def get_current_theme(self) -> dict:
        """直接返回缓存"""
        # 如果缓存未初始化（理论上不应该发生，因为 __init__ 调用了 update_theme_cache），则更新一次
        if self.current_theme_config is None:
            self.update_theme_cache()
        return self.current_theme_config
    
    def get_color(self, color_name: str) -> str:
        """使用缓存获取颜色"""
        # 如果缓存未初始化，则更新一次
        if self.current_theme_config is None:
            self.update_theme_cache()
        return self.current_theme_config.get(color_name, "#000000")
    
    def toggle_theme(self) -> str:
        """切换主题"""
        themes = list(self.THEMES.keys())
        if "auto" in themes:
            themes.remove("auto")  # 暂时移除自动模式
        
        current_index = themes.index(self.current_theme) if self.current_theme in themes else 0
        next_index = (current_index + 1) % len(themes)
        self.current_theme = themes[next_index]
        
        self._save_theme()
        self.update_theme_cache()  # 切换后更新缓存
        return self.current_theme
    
    def apply_window_style(self, widget: QWidget) -> str:
        """应用窗口样式"""
        theme = self.get_current_theme()
        return f"""
        QWidget {{
            background-color: {theme['window_bg']};
            color: {theme['text_primary']};
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
            font-size: 14px;
        }}
        """
    
    def apply_chat_area_style(self) -> str:
        """应用聊天区域样式"""
        theme = self.get_current_theme()
        return f"""
        QScrollArea {{
            background-color: {theme['chat_bg']};
            border: none;
            border-radius: 10px;
        }}
        QScrollBar:vertical {{
            background-color: transparent;
            width: 8px;
            border-radius: 4px;
            margin: 2px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {theme['text_tertiary']};
            border-radius: 4px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {theme['text_secondary']};
        }}
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        """
    
    def apply_input_style(self) -> str:
        """应用输入区域样式"""
        theme = self.get_current_theme()
        return f"""
        QTextEdit {{
            background-color: {theme['input_bg']};
            border: 2px solid transparent;
            border-radius: 22px;
            padding: 12px 16px;
            font-size: 14px;
            color: {theme['text_primary']};
            selection-background-color: {theme['primary']};
        }}
        QTextEdit:focus {{
            border-color: {theme['primary']};
        }}
        """

class AnimationManager:
    """动画管理器"""
    
    @staticmethod
    def create_fade_in(widget: QWidget, duration: int = 300) -> QPropertyAnimation:
        """创建淡入动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_slide_in(widget: QWidget, direction: str = "bottom", duration: int = 400) -> QPropertyAnimation:
        """创建滑入动画"""
        start_pos = widget.pos()
        
        if direction == "bottom":
            end_pos = start_pos
            start_pos = QPoint(start_pos.x(), start_pos.y() + 50)
        elif direction == "right":
            end_pos = start_pos  
            start_pos = QPoint(start_pos.x() + 50, start_pos.y())
        else:
            end_pos = start_pos
        
        widget.move(start_pos)
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.OutBack)
        return animation
    
    @staticmethod
    def create_scale_in(widget: QWidget, duration: int = 200) -> QPropertyAnimation:
        """创建缩放动画"""
        # 这里需要自定义属性来实现缩放，PyQt5中比较复杂
        # 简化版本，使用透明度代替
        return AnimationManager.create_fade_in(widget, duration)

class TypingIndicator(QWidget):
    """打字指示器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_animation()
        
    def setup_ui(self):
        """设置UI"""
        self.setFixedHeight(50)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # AI头像
        avatar_label = QLabel("🤖")
        avatar_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                background-color: rgba(0, 122, 255, 0.1);
                border-radius: 16px;
                padding: 6px;
                margin-right: 10px;
            }
        """)
        
        # 打字文本
        typing_label = QLabel("AI正在输入")
        typing_label.setStyleSheet("""
            QLabel {
                color: rgba(0, 122, 255, 0.8);
                font-size: 13px;
                font-weight: 500;
            }
        """)
        
        # 动画点
        self.dots_container = QWidget()
        dots_layout = QHBoxLayout(self.dots_container)
        dots_layout.setContentsMargins(0, 0, 0, 0)
        dots_layout.setSpacing(4)
        
        self.dots = []
        for i in range(3):
            dot = QLabel("●")
            dot.setStyleSheet("""
                QLabel {
                    color: #007AFF;
                    font-size: 16px;
                }
            """)
            self.dots.append(dot)
            dots_layout.addWidget(dot)
        
        layout.addWidget(avatar_label)
        layout.addWidget(typing_label)
        layout.addWidget(self.dots_container)
        layout.addStretch()
        
    def setup_animation(self):
        """设置动画"""
        self.animations = []
        
        for i, dot in enumerate(self.dots):
            effect = QGraphicsOpacityEffect()
            dot.setGraphicsEffect(effect)
            
            animation = QPropertyAnimation(effect, b"opacity")
            animation.setDuration(600)
            animation.setStartValue(0.3)
            animation.setEndValue(1.0)
            animation.setLoopCount(-1)  # 无限循环
            animation.setEasingCurve(QEasingCurve.InOutSine)
            
            # 错开动画时间
            QTimer.singleShot(i * 200, animation.start)
            self.animations.append(animation)
    
    def start_animation(self):
        """开始动画"""
        for animation in self.animations:
            if animation.state() != QPropertyAnimation.Running:
                animation.start()
    
    def stop_animation(self):
        """停止动画"""
        for animation in self.animations:
            animation.stop()
class MessageBubble(QFrame):
    """现代化消息气泡"""
    
    # 定义信号用于线程安全的图片加载
    image_loaded_signal = pyqtSignal(object, object)  # placeholder, pixmap
    
    def __init__(self, sender: str, content: str = "", timestamp: datetime = None, parent=None, is_companion_mode: bool = False):
        super().__init__(parent)
        self.sender = sender.strip()
        self.content = content # 原始传入内容，可能为空
        self.timestamp = timestamp or datetime.now()
        self.is_user = self.sender.lower() == "user"
        self.is_ai = self.sender.lower() in ["ai", "assistant"]
        self.is_system = self.sender.lower() in ["system", "systemerror", "系统工具"]
        self.is_companion_mode = is_companion_mode  # 陪玩模式标识
        
        # 优化建议添加的属性
        self.current_content = None # 用于 update_content 优化
        self.last_image_path = None # 用于 set_image 优化
        self._added_images = set() # 用于跟踪已添加的图片，避免重复

        # UI组件
        self.content_label = None  # 将在解析内容时动态创建
        self.content_container = None  # 混合内容容器
        self.content_layout = None  # 混合内容布局
        self.image_label = None  # 兼容性引用
        self.time_label = None
        self.avatar_label = None
        
        # 主题管理器引用
        self.theme_manager = None
        self._find_theme_manager()
        
        self.setup_ui()
        self.apply_styling()
        self.setup_animations()
        
        # 连接信号到槽函数，确保UI更新在主线程中进行
        self.image_loaded_signal.connect(self._handle_image_loaded)
    
    def _find_theme_manager(self):
        """查找主题管理器"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'theme_manager'):
                self.theme_manager = parent.theme_manager
                break
            parent = parent.parent()
    
    def setup_ui(self):
        """设置UI布局 - 优化缓存计算"""
        # 在开头添加空内容检查 (优化建议 1)
        if not self.content and not self.image_label: # 检查初始文本内容和是否有图片标签
             # 如果初始内容为空且没有图片，可以先设置一个最小高度或直接返回
             # 这里我们假设如果内容为空，初始高度应为0，除非后续添加了图片
             # 注意：如果后续调用 set_image，需要确保高度能正确更新
             # 一个更稳妥的方式是允许初始布局，但在 update_content 和 set_image 中处理显隐
             # 暂时按建议，如果初始 content 为空，先不进行复杂布局计算
             # 但这可能导致初始为空，后续 update_content 时布局跳动，需要测试验证
             # 考虑到 MessageBubble 的动画，初始高度为0可能更符合动画效果
             self.setFixedHeight(0) # 尝试设置固定高度为0
             # return # 如果返回，下面的布局代码将不执行

        self.setContentsMargins(0, 0, 0, 0)
        
        # 主容器
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 8, 15, 8)
        
        if self.is_user:
            # 用户消息：右对齐
            main_layout.addStretch()
            self.bubble_container = self.create_user_bubble()
            main_layout.addWidget(self.bubble_container)
        else:
            # AI/系统消息：左对齐
            self.bubble_container = self.create_ai_bubble()
            main_layout.addWidget(self.bubble_container)
            main_layout.addStretch()
    
    def create_user_bubble(self) -> QWidget:
        """创建用户消息气泡"""
        container = QFrame()
        container.setObjectName("userBubbleContainer")
        container.setMaximumWidth(300)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(6)
        
        # 混合内容容器（支持文本和图片混合显示）
        self.content_container = QWidget()
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        self.content_container.setObjectName("userContentContainer")
        
        # 为了兼容性，保留 content_label 和 image_label 引用
        self.content_label = None  # 将在解析内容时动态创建
        self.image_label = self.content_container  # 兼容性引用
        
        # 时间标签
        self.time_label = QLabel(self.timestamp.strftime("%H:%M"))
        self.time_label.setAlignment(Qt.AlignRight)
        self.time_label.setObjectName("userTimeLabel")
        
        layout.addWidget(self.content_container)
        layout.addWidget(self.time_label)
        
        # 设置初始内容
        if self.content:
            self.update_content(self.content)
        
        return container
    
    def create_ai_bubble(self) -> QWidget:
        """创建AI/系统消息气泡"""
        container = QFrame()
        container.setObjectName("aiBubbleContainer")
        container.setMaximumWidth(350)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)
        
        # 头部（头像+发送者名称）
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        # 头像
        self.avatar_label = QLabel()
        self.avatar_label.setFixedSize(32, 32)
        self.avatar_label.setAlignment(Qt.AlignCenter)
        self.avatar_label.setObjectName("avatarLabel")
        
        if self.is_ai:
            self.avatar_label.setText("🤖")
        elif self.is_system:
            self.avatar_label.setText("⚙️")
        else:
            self.avatar_label.setText("💬")
        
        # 发送者标签
        sender_label = QLabel(self.sender)
        sender_label.setObjectName("senderLabel")
        
        header_layout.addWidget(self.avatar_label)
        header_layout.addWidget(sender_label)
        header_layout.addStretch()
        
        # 混合内容容器（支持文本和图片混合显示）
        self.content_container = QWidget()
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        self.content_container.setObjectName("aiContentContainer")
        
        # 为了兼容性，保留 content_label 和 image_label 引用
        self.content_label = None  # 将在解析内容时动态创建
        self.image_label = self.content_container  # 兼容性引用
        
        # 底部（时间戳）
        bottom_layout = QHBoxLayout()
        self.time_label = QLabel(self.timestamp.strftime("%H:%M"))
        self.time_label.setObjectName("aiTimeLabel")
        bottom_layout.addStretch()
        bottom_layout.addWidget(self.time_label)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.content_container)
        layout.addLayout(bottom_layout)
        
        # 设置初始内容
        if self.content:
            self.update_content(self.content)
        
        return container
    
    def apply_styling(self):
        """应用样式"""
        if not self.theme_manager:
            logger.warning("ThemeManager not found for MessageBubble.")
            return

        theme = self.theme_manager.get_current_theme()
        
        if self.is_user:
            # 用户消息样式
            user_text_color = "#FFFFFF" # 白色文本，适合深色背景
            user_time_color = "rgba(255, 255, 255, 0.8)"

            style = f"""
            QFrame#userBubbleContainer {{
                background: {theme['bubble_user_bg']}; /* 蓝色渐变背景 */
                border-radius: 18px;
                border: none;
            }}
            QLabel#userContentLabel {{
                color: {user_text_color};       /* 文字颜色设为白色 */
                background-color: transparent; /* 关键：确保 QLabel 背景透明 */
                font-size: 14px;
                line-height: 1.4;
                padding: 2px; /* 可选：微调内边距，防止文字太贴边 */
            }}
            QLabel#userTimeLabel {{
                color: {user_time_color};
                background-color: transparent; /* 时间标签背景也设为透明 */
                font-size: 11px;
            }}
            QWidget#userContentContainer {{
                background-color: transparent; /* 内容容器背景设为透明 */
            }}
            QLabel#mixedContentLabel, QLabel#imagePlaceholder {{
                background-color: transparent; /* 混合内容标签背景也设为透明 */
            }}
            """
        else: # AI 或系统消息
            # AI/系统消息的文本颜色通常是 theme['text_primary']
            # 其气泡背景 theme['bubble_ai_bg'] 或 theme['bubble_system_bg'] 通常是浅色或半透明深色
            # 我们也应该确保这些 QLabel 的背景是透明的，以显示 bubble_container 的背景

            ai_text_color = theme['text_primary']
            ai_time_color = theme['text_tertiary']
            sender_name_color = theme['text_secondary']

            bg_color = theme['bubble_ai_bg']
            if self.is_system:
                bg_color = theme['bubble_system_bg']
            
            style = f"""
            QFrame#aiBubbleContainer {{
                background: {bg_color}; /* AI/系统气泡背景 */
                border-radius: 18px;
                border: 1px solid {theme['border_color']};
            }}
            QLabel#avatarLabel {{ /* 头像的背景可以保持原样 */
                background-color: {theme['primary']};
                border-radius: 16px;
                font-size: 16px;
                color: {theme['text_inverse']};
            }}
            QLabel#senderLabel {{
                color: {sender_name_color};
                background-color: transparent; /* 发送者名称背景透明 */
                font-size: 12px;
                font-weight: 600;
            }}
            QLabel#aiContentLabel {{
                color: {ai_text_color};
                background-color: transparent; /* AI 内容标签背景透明 */
                font-size: 14px;
                line-height: 1.4;
                padding: 2px; /* 可选 */
            }}
            QLabel#aiTimeLabel {{
                color: {ai_time_color};
                background-color: transparent; /* AI 时间标签背景透明 */
                font-size: 11px;
            }}
            QWidget#aiContentContainer {{
                background-color: transparent; /* AI 内容容器背景透明 */
            }}
            QLabel#mixedContentLabel, QLabel#imagePlaceholder {{
                background-color: transparent; /* 混合内容标签背景透明 */
            }}
            """
        
        self.setStyleSheet(style)
    
    def setup_animations(self):
        """设置动画"""
        # 进入动画
        self.setMaximumHeight(0)
        self.expand_animation = QPropertyAnimation(self, b"maximumHeight")
        self.expand_animation.setDuration(300)
        self.expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 延迟启动动画
        QTimer.singleShot(50, self.start_enter_animation)
    
    def start_enter_animation(self):
        """开始进入动画"""
        # 获取实际高度
        self.setMaximumHeight(16777215)  # 移除高度限制
        actual_height = self.sizeHint().height()
        self.setMaximumHeight(0)  # 重新设置为0
        
        # 设置动画
        self.expand_animation.setStartValue(0)
        self.expand_animation.setEndValue(actual_height + 10)  # 加一点余量
        self.expand_animation.finished.connect(lambda: self.setMaximumHeight(16777215))
        self.expand_animation.start()
    
    def update_content(self, new_content: str):
        """更新内容 - 支持文本和图片混合显示"""
        if not hasattr(self, 'content_layout'):
            return
        
        # 只在内容实际改变时更新
        if new_content == self.current_content:
            return
            
        self.current_content = new_content

        # 清空现有内容
        self._clear_content_container()
        
        # 解析并按顺序显示混合内容
        self._parse_and_display_mixed_content(new_content)
        
        # 更新大小
        self.adjustSize()
        
        # 如果有动画正在运行，更新目标高度
        if hasattr(self, 'expand_animation') and self.expand_animation.state() == QPropertyAnimation.Running:
            new_height = self.sizeHint().height()
            self.expand_animation.setEndValue(new_height + 10)
    
    def _update_streaming_text(self, text_content: str):
        """流式更新文本内容（不重建混合内容）"""
        if not hasattr(self, 'content_layout'):
            return
            
        # 在流式更新过程中，寻找或创建一个纯文本标签来显示累积的文本
        streaming_label = None
        
        # 查找是否已经有流式文本标签
        for i in range(self.content_layout.count()):
            widget = self.content_layout.itemAt(i).widget()
            if widget and widget.objectName() == "streamingTextLabel":
                streaming_label = widget
                break
        
        # 如果没有找到，创建一个新的流式文本标签
        if not streaming_label:
            streaming_label = QLabel()
            streaming_label.setWordWrap(True)
            streaming_label.setTextFormat(Qt.RichText)
            streaming_label.setOpenExternalLinks(True)
            streaming_label.setTextInteractionFlags(Qt.TextSelectableByMouse | Qt.LinksAccessibleByMouse)
            streaming_label.setObjectName("streamingTextLabel")
            
            # 设置样式
            if self.is_user:
                streaming_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
            else:
                theme = self.theme_manager.get_current_theme() if self.theme_manager else {}
                text_color = theme.get('text_primary', '#000000')
                streaming_label.setStyleSheet(f"color: {text_color}; background-color: transparent;")
            
            # 添加到内容容器的开头
            self.content_layout.insertWidget(0, streaming_label)
            
            # 为兼容性保留第一个文本标签的引用
            if not self.content_label:
                self.content_label = streaming_label
        
        # 处理并更新文本内容
        processed_content = self._process_content(text_content)
        streaming_label.setText(processed_content)
        
        # 重新调整大小
        self.adjustSize()
    
    def _clean_incomplete_img_tags(self, content: str) -> str:
        """清理不完整的img标签，尝试修复截断的标签"""
        import re
        
        
        
        # 查找不完整的img标签模式
        # 1. 查找以 <img 开始但没有 > 结尾的标签
        incomplete_start_pattern = r'<img[^>]*$'
        
        # 2. 查找可能的img标签片段，尝试重构完整标签
        # 模式：寻找可能的图片URL片段
        url_pattern = r'(https?://[^\s<>"]+(?:\.(?:jpg|jpeg|png|gif|webp|bmp))?)'
        
        # 先移除明显不完整的开始标签
        cleaned_content = re.sub(incomplete_start_pattern, '', content)
        
        # 查找所有可能的图片URL
        urls = re.findall(url_pattern, cleaned_content)
        
        for url in urls:
            # 检查URL前后是否有img标签的片段
            # 如果找到裸露的URL（前面可能有不完整的img src="），尝试构建完整标签
            url_pos = cleaned_content.find(url)
            if url_pos > 0:
                before_url = cleaned_content[max(0, url_pos-20):url_pos]
                after_url = cleaned_content[url_pos+len(url):url_pos+len(url)+10]
                
                # 检查是否是被截断的img标签
                if 'src="' in before_url or 'src=\'' in before_url:                    # 构建完整的img标签
                    complete_tag = f'<img src="{url}">'
                    # 替换原来可能不完整的结构
                    pattern = r'<img[^>]*src=["\']?' + re.escape(url) + r'["\']?[^>]*>?'                    if not re.search(pattern, cleaned_content):
                        # 如果没有找到完整标签，则构建一个
                        cleaned_content = cleaned_content.replace(url, complete_tag)
        
        return cleaned_content
    
    def _finalize_mixed_content(self, final_content: str):
        """智能完成混合内容构建，避免重复清空已加载的图片"""
        if not hasattr(self, 'content_layout'):
            return
            
        import re
        
        # 清理可能的不完整img标签
        cleaned_content = self._clean_incomplete_img_tags(final_content)
        
        # 解析最终内容中的图片
        img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
        final_images = re.findall(img_pattern, cleaned_content)
        
        # 检查当前已加载的图片
        existing_images = set()
        streaming_text_widget = None
        
        for i in range(self.content_layout.count()):
            widget = self.content_layout.itemAt(i).widget()
            if widget:
                if widget.objectName() == "streamingTextLabel":
                    streaming_text_widget = widget
                elif widget.objectName() == "imagePlaceholder":
                    img_url = widget.property("img_url")
                    if img_url:
                        existing_images.add(img_url)
        
        # 如果需要的图片都已存在，只更新文本
        if set(final_images).issubset(existing_images):
            # 只更新流式文本标签的内容为去除图片的最终文本
            final_text = self._remove_img_tags(cleaned_content)
            if streaming_text_widget:
                processed_content = self._process_content(final_text)
                streaming_text_widget.setText(processed_content)
        else:
            # 有新图片需要加载，使用完整重建
            self.update_content(cleaned_content)
        
        # 重新调整大小
        self.adjustSize()
    
    def _process_content(self, content: str) -> str:
        """处理内容，支持简单的markdown和表情"""
        if not content:
            return ""
        
        # HTML转义
        processed = html.escape(content)
        
        # 换行处理
        processed = processed.replace('\n', '<br>')
        
        # 简单的markdown支持
        # 粗体
        processed = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', processed)
        # 斜体
        processed = re.sub(r'\*(.*?)\*', r'<i>\1</i>', processed)
        # 代码
        processed = re.sub(r'`(.*?)`', r'<code style="background-color: rgba(128,128,128,0.2); padding: 2px 4px; border-radius: 3px;">\1</code>', processed)
        
        # 链接处理
        url_pattern = r'(https?://[^\s<>"]+)'
        processed = re.sub(url_pattern, r'<a href="\1" style="color: #007AFF;">\1</a>', processed)
        
        return processed
    
    def _remove_img_tags(self, content: str) -> str:
        """移除HTML img标签（包括不完整的标签）- MessageBubble版本"""
        import re
        # 移除完整的img标签
        content = re.sub(r'<img[^>]*>', '', content)
        # 移除不完整的img标签开始部分
        content = re.sub(r'<img[^>]*$', '', content)
        return content
    
    def _clear_content_container(self):
        """清空内容容器"""
        if hasattr(self, 'content_layout') and self.content_layout:
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
    
    def _parse_and_display_mixed_content(self, content: str):
        """解析并按顺序显示文本和图片的混合内容"""
        if not content.strip():
            return
          import re
        
        # 使用正则表达式分割内容，保留img标签的位置信息
        img_pattern = r'(<img[^>]*src=["\']([^"\']+)["\'][^>]*>)'
        parts = re.split(img_pattern, content)
          # parts 的结构：[text, img_tag, img_url, text, img_tag, img_url, ...]
        i = 0
        while i < len(parts):
            part = parts[i]
            if not part.strip():
                i += 1
                continue
                
            if part.startswith('<img'):
                # 这是一个图片标签，下一个元素应该是URL
                if i + 1 < len(parts):
                    img_url = parts[i + 1]
                    
                    placeholder = self._create_image_placeholder(img_url)
                    self.content_layout.addWidget(placeholder)
                    
                    # 异步加载图片
                    if img_url:
                        self._load_image_for_placeholder(placeholder, img_url)
                    
                    i += 2  # 跳过img_tag和img_url
                else:
                    i += 1
            elif not part.startswith('http'):  # 跳过单独的URL部分
                # 这是文本内容
                text_content = self._process_content(part)
                if text_content.strip():
                    
                    text_label = QLabel()
                    text_label.setWordWrap(True)
                    text_label.setTextFormat(Qt.RichText)
                    text_label.setOpenExternalLinks(True)
                    text_label.setTextInteractionFlags(Qt.TextSelectableByMouse | Qt.LinksAccessibleByMouse)
                    text_label.setText(text_content)
                    text_label.setObjectName("mixedContentLabel")
                    
                    # 设置样式
                    if self.is_user:
                        text_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
                    else:
                        theme = self.theme_manager.get_current_theme() if self.theme_manager else {}
                        text_color = theme.get('text_primary', '#000000')
                        text_label.setStyleSheet(f"color: {text_color}; background-color: transparent;")
                    
                    self.content_layout.addWidget(text_label)
                    
                    # 为兼容性保留第一个文本标签的引用
                    if not self.content_label:
                        self.content_label = text_label
                
                i += 1
            else:
                i += 1
    
    def _create_image_placeholder(self, img_url: str) -> QLabel:
        """为图片创建占位符"""
        placeholder = QLabel("🖼️ 加载中...")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                color: #888;
                background-color: rgba(128, 128, 128, 0.1);
                border: 1px dashed #ccc;
                border-radius: 8px;
                padding: 10px;
                min-height: 60px;
                min-width: 100px;
            }
        """)
        placeholder.setObjectName("imagePlaceholder")
        
        # 保存图片URL到占位符
        placeholder.setProperty("img_url", img_url)
        
        # 添加一个标记，用于验证占位符的有效性
        placeholder.setProperty("creation_time", time.time())
        
        return placeholder
    
    def _is_placeholder_valid(self, placeholder: QLabel) -> bool:
        """检查占位符是否仍然有效"""
        try:
            if not placeholder:
                return False
            if not hasattr(placeholder, 'setPixmap'):
                return False
            if placeholder.isHidden():
                return False            # 检查创建时间，如果超过5分钟就认为无效
            creation_time = placeholder.property("creation_time")
            if creation_time and time.time() - creation_time > 300:
                return False
            return True
        except Exception:
            return False
    
    def _load_image_for_placeholder(self, placeholder: QLabel, img_url: str):
        """为占位符异步加载图片"""
        # 先验证占位符有效性
        if not placeholder or not hasattr(placeholder, 'setText'):
            return
        
        def load_and_replace():
            
            def show_error(error_msg: str):
                """显示错误信息"""
                def update_error():
                    try:
                        # 使用新的有效性检查方法
                        if self._is_placeholder_valid(placeholder):
                            placeholder.setText(f"🖼️ {error_msg}")
                            placeholder.setStyleSheet("""
                                QLabel {
                                    color: #FF6B6B;
                                    background-color: rgba(255, 107, 107, 0.1);
                                    border: 1px solid #FF6B6B;
                                    border-radius: 8px;
                                    padding: 10px;
                                }
                            """)                        else:
                            pass
                    except Exception:
                        pass
                
                QTimer.singleShot(0, update_error)
            
            pixmap = None            try:
                # 测试本地服务器连接
                if "localhost" in img_url or "127.0.0.1" in img_url:
                    try:
                        import socket
                        import urllib.parse
                        parsed = urllib.parse.urlparse(img_url)
                        host = parsed.hostname or 'localhost'
                        port = parsed.port or 6005
                        
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(5)
                        result = sock.connect_ex((host, port))
                        sock.close()
                        
                        if result != 0:
                            logger.error(f"本地服务器连接失败: {host}:{port}, 错误码: {result}")
                    except Exception as conn_test_error:
                        logger.error(f"🔥🔥🔥 本地服务器连接测试异常: {conn_test_error}")
                        print(f"🔥🔥🔥 本地服务器连接测试异常: {conn_test_error}")
                
                logger.info(f"🔥🔥🔥 发起网络请求: {img_url}")
                print(f"🔥🔥🔥 发起网络请求: {img_url}")
                
                logger.info(f"🔥🔥🔥 开始发起requests.get请求")
                print(f"🔥🔥🔥 开始发起requests.get请求")
                
                response = requests.get(img_url, timeout=15, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'                })
                
                if response.status_code != 200:
                    show_error(f"HTTP {response.status_code}")
                    return
                
                # 验证下载内容
                if len(response.content) == 0:
                    show_error("图片内容为空")
                    return                  # 直接从内存创建QPixmap
                pixmap = QPixmap()
                success = pixmap.loadFromData(response.content)
                  if not success or pixmap.isNull():
                    show_error("图片格式错误")
                    return
                
                # 在主线程中更新UI
                def update_ui():
                    try:
                        # 使用新的有效性检查方法
                        if not self._is_placeholder_valid(placeholder):
                            return
                        
                        # 再次验证pixmap有效性
                        if not pixmap or pixmap.isNull():
                            show_error("图片无效")
                            return
                        
                        # 缩放图片
                        scaled_pixmap = pixmap
                        if pixmap.width() > 150:
                            scaled_pixmap = pixmap.scaledToWidth(150, Qt.SmoothTransformation)
                        
                        # 添加圆角 - 增加错误处理
                        try:
                            rounded_pixmap = self._create_rounded_pixmap(scaled_pixmap, 12)
                            if rounded_pixmap.isNull():
                                rounded_pixmap = scaled_pixmap
                        except Exception:
                            rounded_pixmap = scaled_pixmap
                        
                        # 更新占位符
                        placeholder.setPixmap(rounded_pixmap)
                        placeholder.setText("")  # 清除"加载中"文本
                        placeholder.setStyleSheet("background-color: transparent; border: none;")
                        placeholder.adjustSize()
                        
                        # 触发父容器重新布局
                        try:
                            parent = placeholder.parent()
                            layout_updated = False
                            while parent and not layout_updated:
                                if hasattr(parent, 'adjustSize'):
                                    parent.adjustSize()
                                    layout_updated = True
                                elif hasattr(parent, 'updateGeometry'):
                                    parent.updateGeometry()
                                    layout_updated = True
                                parent = parent.parent()
                        except Exception:
                            pass
                        
                    except Exception:
                        show_error("显示失败")
                
                # 使用信号发射来实现线程安全的UI更新
                self.image_loaded_signal.emit(placeholder, pixmap)
                  except requests.exceptions.Timeout:
                show_error("加载超时")
            except requests.exceptions.ConnectionError:
                show_error("连接失败")
            except Exception:
                show_error("加载失败")
            finally:
                # 清理资源
                if 'response' in locals():
                    try:
                        response.close()
                    except:
                        pass
        
        # 启动异步加载线程
        threading.Thread(target=load_and_replace, daemon=True).start()
    
    def _handle_image_loaded(self, placeholder, pixmap):
        """槽函数：在主线程中处理图片加载完成的信号"""
        try:
            # 使用新的有效性检查方法
            if not self._is_placeholder_valid(placeholder):
                return
            
            # 再次验证pixmap有效性
            if not pixmap or pixmap.isNull():
                placeholder.setText("图片无效")
                return
            
                        # 缩放图片
            scaled_pixmap = pixmap
            if pixmap.width() > 150:
                scaled_pixmap = pixmap.scaledToWidth(150, Qt.SmoothTransformation)
            
            # 添加圆角 - 增加错误处理
            try:
                rounded_pixmap = self._create_rounded_pixmap(scaled_pixmap, 12)
                if rounded_pixmap.isNull():
                    rounded_pixmap = scaled_pixmap
            except Exception:
                rounded_pixmap = scaled_pixmap
            
            # 更新占位符
            placeholder.setPixmap(rounded_pixmap)
            placeholder.setText("")  # 清除"加载中"文本
            placeholder.setStyleSheet("background-color: transparent; border: none;")
            placeholder.adjustSize()
              # 触发父容器重新布局
            try:
                parent = placeholder.parent()
                layout_updated = False
                while parent and not layout_updated:
                    if hasattr(parent, 'adjustSize'):
                        parent.adjustSize()
                        layout_updated = True
                    elif hasattr(parent, 'updateGeometry'):
                        parent.updateGeometry()
                        layout_updated = True
                    parent = parent.parent()
            except Exception:
                pass
            
        except Exception:
            placeholder.setText("显示失败")

    def set_image(self, image_path: str, max_width: int = 200):
        """兼容性方法：添加图片到消息末尾（用于历史记录等场景）"""
        if not hasattr(self, 'content_layout') or not os.path.exists(image_path):
            logger.warning(f"Cannot add image: layout missing or file not found: {image_path}")
            return

        try:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 缩放图片
                if pixmap.width() > max_width:
                    pixmap = pixmap.scaledToWidth(max_width, Qt.SmoothTransformation)
                
                # 添加圆角效果
                rounded_pixmap = self._create_rounded_pixmap(pixmap, 12)
                
                # 创建图片标签并添加到内容容器末尾
                image_label = QLabel()
                image_label.setAlignment(Qt.AlignCenter)
                image_label.setPixmap(rounded_pixmap)
                image_label.adjustSize()
                image_label.setObjectName("standaloneImageLabel")
                
                self.content_layout.addWidget(image_label)
                
                # 重新调整大小
                self.adjustSize()
                
                logger.info(f"Standalone image added successfully: {image_path}")
                
        except Exception as e:
            logger.error(f"Failed to add standalone image: {e}")
    
    def _create_rounded_pixmap(self, pixmap: QPixmap, radius: int) -> QPixmap:
        """创建圆角图片"""
        try:
            if pixmap.isNull() or pixmap.width() == 0 or pixmap.height() == 0:
                logger.error("🔥 输入的pixmap无效")
                return pixmap
            
            size = pixmap.size()
            rounded = QPixmap(size)
            rounded.fill(Qt.transparent)
            
            painter = QPainter(rounded)
            if not painter.isActive():
                logger.error("🔥 QPainter初始化失败")
                return pixmap
            
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
            
            # 创建圆角路径
            path = QPainterPath()
            rect = QRectF(0, 0, size.width(), size.height())
            path.addRoundedRect(rect, radius, radius)
            
            # 设置裁剪路径并绘制
            painter.setClipPath(path)
            painter.drawPixmap(0, 0, pixmap)
            painter.end()
            
            if rounded.isNull():
                logger.error("🔥 圆角图片创建后为空")
                return pixmap
            
            logger.info(f"🔥 圆角图片创建成功，尺寸: {rounded.width()}x{rounded.height()}")
            return rounded
            
        except Exception as e:
            logger.error(f"🔥 创建圆角图片时出错: {e}")
            import traceback
            traceback.print_exc()
            return pixmap  # 出错时返回原图
    
    def clear_images(self):
        """清除所有内容（包括文本和图片）"""
        if hasattr(self, 'content_layout') and self.content_layout:
            # 移除所有内容
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            
            # 重置content_label引用
            self.content_label = None
            
            # 清理已添加图片的记录
            if hasattr(self, '_added_images'):
                self._added_images.clear()
                
            logger.debug("All mixed content cleared from message bubble")
    
    def add_typing_effect(self, text: str, speed: int = 30):
        """添加打字机效果"""
        if not self.content_label:
            return
        
        self.typing_text = text
        self.typing_index = 0
        
        self.typing_timer = QTimer()
        self.typing_timer.timeout.connect(self._type_next_character)
        self.typing_timer.start(speed)
    
    def _type_next_character(self):
        """打字机效果的下一个字符"""
        if self.typing_index < len(self.typing_text):
            current_text = self.typing_text[:self.typing_index + 1]
            self.update_content(current_text)
            self.typing_index += 1
        else:
            self.typing_timer.stop()
    
    def highlight_briefly(self, duration: int = 1000):
        """短暂高亮"""
        if not self.bubble_container:
            return
        
        # 保存原始样式
        original_style = self.bubble_container.styleSheet()
        
        # 应用高亮样式
        highlight_style = original_style + """
        QFrame {
            border: 2px solid #007AFF;
        }
        """
        self.bubble_container.setStyleSheet(highlight_style)
        
        # 定时恢复
        QTimer.singleShot(duration, lambda: self.bubble_container.setStyleSheet(original_style))


class ModernButton(QPushButton):
    """现代化按钮"""
    
    def __init__(self, text: str = "", icon: str = "", parent=None):
        super().__init__(parent)
        self.icon_text = icon
        self.button_text = text
        self.default_style = ""
        self.hover_style = ""
        self.pressed_style = ""
        self.is_primary = False
        
        self.update_button_text()
        self.setup_animations()
    
    def update_button_text(self):
        """更新按钮文本"""
        if self.icon_text and self.button_text:
            self.setText(f"{self.icon_text} {self.button_text}")
        elif self.icon_text:
            self.setText(self.icon_text)
        else:
            self.setText(self.button_text)
    
    def setup_animations(self):
        """设置动画效果"""
        self.setCursor(Qt.PointingHandCursor)
        
        # 缩放动画
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(100)
        self.scale_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def set_style_type(self, style_type: str, theme_manager=None):
        """设置按钮样式类型"""
        if not theme_manager:
            return
        
        theme = theme_manager.get_current_theme()
        
        if style_type == "primary":
            self.is_primary = True
            self.setStyleSheet(f"""
                QPushButton {{
                    background: {theme['bubble_user_bg']};
                    border: none;
                    border-radius: 22px;
                    color: {theme['text_inverse']};
                    font-size: 14px;
                    font-weight: 600;
                    padding: 12px 24px;
                }}
                QPushButton:hover {{
                    background: {theme['primary_dark']};
                }}
                QPushButton:pressed {{
                    background: {theme['primary_dark']};
                }}
                QPushButton:disabled {{
                    opacity: 0.5;
                }}
            """)
        
        elif style_type == "secondary":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {theme['input_bg']};
                    border: 1px solid {theme['border_color']};
                    border-radius: 18px;
                    color: {theme['text_primary']};
                    font-size: 14px;
                    padding: 8px 16px;
                }}
                QPushButton:hover {{
                    background-color: {theme['border_color']};
                    border-color: {theme['primary']};
                }}
                QPushButton:pressed {{
                    background-color: {theme['primary']};
                    color: {theme['text_inverse']};
                }}
            """)
        
        elif style_type == "tool":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(120, 120, 128, 0.12);
                    border: none;
                    border-radius: 18px;
                    color: {theme['primary']};
                    font-size: 16px;
                    padding: 8px;
                }}
                QPushButton:hover {{
                    background-color: {theme['primary']};
                    color: {theme['text_inverse']};
                }}
                QPushButton:pressed {{
                }}
            """)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        # 可以在这里添加悬停效果
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        # 可以在这里重置效果
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        super().mousePressEvent(event)
        # 添加按下效果
        if hasattr(self, 'scale_animation'):
            current_geo = self.geometry()
            pressed_geo = QRect(
                current_geo.x() + 1,
                current_geo.y() + 1, 
                current_geo.width() - 2,
                current_geo.height() - 2
            )
            self.scale_animation.setStartValue(current_geo)
            self.scale_animation.setEndValue(pressed_geo)
            self.scale_animation.start()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        super().mouseReleaseEvent(event)
        # 恢复大小
        if hasattr(self, 'scale_animation'):
            pressed_geo = self.scale_animation.endValue()
            normal_geo = QRect(
                pressed_geo.x() - 1,
                pressed_geo.y() - 1,
                pressed_geo.width() + 2, 
                pressed_geo.height() + 2
            )
            self.scale_animation.setStartValue(pressed_geo)
            self.scale_animation.setEndValue(normal_geo)
            self.scale_animation.start()


class SmartScrollArea(QScrollArea):
    """智能滚动区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.auto_scroll = True
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 监听滚动条变化
        self.verticalScrollBar().rangeChanged.connect(self._on_range_changed)
        self.verticalScrollBar().valueChanged.connect(self._on_scroll_changed)
    
    def _on_range_changed(self, min_val, max_val):
        """滚动范围改变时 - 添加节流"""
        if not self.auto_scroll:
            return
            
        # 添加节流机制，避免频繁滚动 (优化建议 5)
        current_time = time.time()
        if hasattr(self, "last_scroll_time") and current_time - self.last_scroll_time < 0.1: # 100ms节流
            return
            
        self.last_scroll_time = current_time
        self.scroll_to_bottom() # 调用原来的滚动方法
    
    def _on_scroll_changed(self, value):
        """滚动位置改变时"""
        scrollbar = self.verticalScrollBar()
        # 如果用户手动滚动到非底部位置，禁用自动滚动
        if value < scrollbar.maximum() - 50:  # 50像素的容差
            self.auto_scroll = False
        else:
            self.auto_scroll = True
    
    def scroll_to_bottom(self, animated: bool = True):
        """滚动到底部"""
        scrollbar = self.verticalScrollBar()
        
        if animated and hasattr(self, 'scroll_animation'):
            # 停止现有动画
            if self.scroll_animation.state() == QPropertyAnimation.Running:
                self.scroll_animation.stop()
            
            # 创建新的滚动动画
            self.scroll_animation.setStartValue(scrollbar.value())
            self.scroll_animation.setEndValue(scrollbar.maximum())
            self.scroll_animation.start()
        else:
            # 直接滚动
            scrollbar.setValue(scrollbar.maximum())
    
    def enable_scroll_animation(self):
        """启用滚动动画"""
        scrollbar = self.verticalScrollBar()
        self.scroll_animation = QPropertyAnimation(scrollbar, b"value")
        self.scroll_animation.setDuration(300)
        self.scroll_animation.setEasingCurve(QEasingCurve.OutCubic)
class ModernInputArea(QWidget):
    """现代化输入区域"""
    
    sendMessage = pyqtSignal(str, str, str)  # text, image_path, attachment_path
    voiceRecordingToggled = pyqtSignal(bool)
    gameCompanionModeToggled = pyqtSignal(bool)  # 游戏伴侣模式切换信号
    
    def __init__(self, parent=None, theme_manager=None, app_controller=None): # <--- 增加 app_controller 参数
        super().__init__(parent)
        self.theme_manager = theme_manager
        self.app_controller = app_controller # <--- 保存 app_controller 引用
        self.selected_image_path = None
        self.selected_attachment_path = None
        self.is_recording = False
        
        # 输入历史
        self.input_history = []
        self.history_index = -1
        self.max_history = 50
        
        self.setup_ui()
        self.setup_shortcuts()
        
    def setup_ui(self):
        """设置UI"""
        # 添加输入历史缓存 (优化建议 2)
        self.history_cache = {}

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 20)
        layout.setSpacing(12)
        
        # 文件预览区域
        self.preview_area = self.create_preview_area()
        layout.addWidget(self.preview_area)
        
        # 工具栏
        self.toolbar = self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 输入区域
        self.input_container = self.create_input_container()
        layout.addWidget(self.input_container)
        
        # 应用样式
        self.apply_styling()
    
    def create_preview_area(self) -> QWidget:
        """创建文件预览区域"""
        container = QWidget()
        container.hide()  # 默认隐藏
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(10)
        
        # 预览标签
        self.preview_icon = QLabel()
        self.preview_icon.setFixedSize(32, 32)
        self.preview_icon.setAlignment(Qt.AlignCenter)
        
        self.preview_text = QLabel()
        self.preview_text.setWordWrap(True)
        
        # 移除按钮
        self.remove_preview_btn = ModernButton("✕")
        self.remove_preview_btn.setFixedSize(24, 24)
        self.remove_preview_btn.clicked.connect(self.clear_attachments)
        
        layout.addWidget(self.preview_icon)
        layout.addWidget(self.preview_text, 1)
        layout.addWidget(self.remove_preview_btn)
        
        return container
    
    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 工具按钮配置
        tools = [
            ("📎", "附件", self.select_attachment, "attachment"),
            ("🖼️", "图片", self.select_image, "image"),
            ("🎤", "语音输入", self.toggle_asr_button_action, "asr_voice_input"),
            ("😊", "表情", self.show_emoji_picker, "emoji"),
            ("📸", "截图", self.take_screenshot, "screenshot"),
            ("🎮", "游戏伴侣", self.toggle_game_companion_mode, "game_companion"),
        ]
        
        self.tool_buttons = {}
        for icon, tooltip, callback, key in tools:
            btn = ModernButton(icon=icon)
            btn.setFixedSize(36, 36)
            btn.setToolTip(tooltip)
            btn.clicked.connect(callback)
            
            if self.theme_manager:
                btn.set_style_type("tool", self.theme_manager)
            
            layout.addWidget(btn)
            self.tool_buttons[key] = btn
        
        layout.addStretch()
        
        # 模型选择器
        self.model_selector = self.create_model_selector()
        layout.addWidget(self.model_selector)
        
        # 游戏伴侣按钮特殊配置
        self.game_companion_button = self.tool_buttons.get("game_companion")
        if self.game_companion_button:
            self.game_companion_button.setCheckable(True)  # 设为可切换按钮
        
        return toolbar
    
    def create_model_selector(self) -> QComboBox:
        """创建模型选择器"""
        selector = QComboBox()
        selector.setMinimumWidth(120)
        selector.setToolTip("选择AI模型")
        
        # 从app_controller获取可用模型
        if hasattr(self.parent(), 'app_controller') and self.parent().app_controller:
            models = getattr(self.parent().app_controller, 'available_models', [])
            current_model = getattr(self.parent().app_controller.config, 'api_model_name', '')
            
            for model in models:
                selector.addItem(model)
                
            # 设置当前模型
            if current_model in models:
                selector.setCurrentText(current_model)
        
        return selector
    
    def create_input_container(self) -> QWidget:
        """创建输入容器"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)
        
        # 输入框
        self.input_field = QTextEdit()
        self.input_field.setPlaceholderText("输入消息... (Enter发送, Shift+Enter换行)") # 更新提示文本
        self.input_field.setMaximumHeight(120)
        self.input_field.setMinimumHeight(44)
        self.input_field.textChanged.connect(self.on_text_changed)
        self.input_field.installEventFilter(self)
        
        # 发送按钮
        self.send_button = ModernButton(icon="➤")
        self.send_button.setFixedSize(44, 44)
        self.send_button.setToolTip("发送消息 (Ctrl+Enter)")
        self.send_button.clicked.connect(self.send_message)
        self.send_button.setEnabled(False)
        
        if self.theme_manager:
            self.send_button.set_style_type("primary", self.theme_manager)
        
        layout.addWidget(self.input_field, 1)
        layout.addWidget(self.send_button)
        
        return container
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+Enter 发送 (现在由 eventFilter 处理 Enter，此快捷键可以移除或保留作为备用)
        # send_shortcut = QShortcut(QKeySequence("Ctrl+Return"), self) # 移除 Ctrl+Enter 快捷键
        # send_shortcut.activated.connect(self.send_message)
        
        # Ctrl+Shift+V 粘贴图片
        paste_image_shortcut = QShortcut(QKeySequence("Ctrl+Shift+V"), self)
        paste_image_shortcut.activated.connect(self.paste_image_from_clipboard)
        
        # Ctrl+L 清空输入
        clear_shortcut = QShortcut(QKeySequence("Ctrl+L"), self)
        clear_shortcut.activated.connect(self.clear_input)
    
    def eventFilter(self, obj, event):
        if obj is self.input_field and event.type() == QEvent.KeyPress:
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                if event.modifiers() == Qt.NoModifier:
                    # 检查输入框是否有内容，避免发送空消息 (如果send_message本身不处理)
                    if self.input_field.toPlainText().strip():
                        self.send_message()
                    return True  # 事件已处理，阻止默认换行
                elif event.modifiers() == Qt.ShiftModifier:
                    # Shift+Enter: 允许默认行为 (插入新行)
                    return super().eventFilter(obj, event) # 或者直接 return False
            
            # 处理历史记录导航 (上箭头和下箭头)
            if event.key() == Qt.Key_Up and (event.modifiers() == Qt.NoModifier or event.modifiers() == Qt.ShiftModifier) : # Shift+Up也导航
                self.navigate_history_up()
                return True # 事件已处理
            elif event.key() == Qt.Key_Down and (event.modifiers() == Qt.NoModifier or event.modifiers() == Qt.ShiftModifier): # Shift+Down也导航
                self.navigate_history_down()
                return True # 事件已处理

        return super().eventFilter(obj, event)

    def apply_styling(self):
        """应用样式"""
        if not self.theme_manager:
            return
        
        theme = self.theme_manager.get_current_theme()
        
        # 主容器样式
        self.setStyleSheet(f"""
            ModernInputArea {{
                background-color: {theme['window_bg']};
                border-radius: 20px;
            }}
        """)
        
        # 预览区域样式
        self.preview_area.setStyleSheet(f"""
            QWidget {{
                background-color: {theme['bubble_system_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 12px;
            }}
            QLabel {{
                color: {theme['text_primary']};
                font-size: 12px;
            }}
        """)
        
        # 输入框样式
        self.input_field.setStyleSheet(f"""
            QTextEdit {{
                background-color: {theme['input_bg']};
                border: 2px solid transparent;
                border-radius: 22px;
                padding: 12px 16px;
                font-size: 14px;
                color: {theme['text_primary']};
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
                selection-background-color: {theme['primary']};
            }}
            QTextEdit:focus {{
                border-color: {theme['primary']};
            }}
            QTextEdit QScrollBar:vertical {{
                background-color: transparent;
                width: 8px;
                border-radius: 4px;
            }}
            QTextEdit QScrollBar::handle:vertical {{
                background-color: {theme['text_tertiary']};
                border-radius: 4px;
                min-height: 20px;
            }}
        """)
        
        # 模型选择器样式
        self.model_selector.setStyleSheet(f"""
            QComboBox {{
                background-color: {theme['input_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 15px;
                padding: 6px 12px;
                font-size: 12px;
                color: {theme['text_primary']};
                min-width: 100px;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {theme['text_secondary']};
                margin-right: 5px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {theme['window_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                selection-background-color: {theme['primary']};
                color: {theme['text_primary']};
            }}
        """)
    
    def eventFilter(self, obj, event):
        """事件过滤器，实现Enter发送，Shift+Enter换行"""
        if obj is self.input_field and event.type() == QEvent.KeyPress:
            key = event.key()
            modifiers = event.modifiers()

            if key == Qt.Key_Return or key == Qt.Key_Enter:
                if modifiers == Qt.NoModifier: # 单独Enter键
                    if self.input_field.toPlainText().strip(): # 确保有内容才发送
                        self.send_message()
                    event.accept() # 接受事件
                    return True  # 事件已处理，阻止默认的换行行为
                elif modifiers == Qt.ShiftModifier: # Shift + Enter
                    # 允许默认行为 (插入新行)
                    # 不调用 event.accept()，也不返回 True，让父类处理或QTextEdit自行处理
                    return super().eventFilter(obj, event)
            
            # 上下箭头浏览历史 (保留原有逻辑)
            elif key == Qt.Key_Up and (modifiers == Qt.NoModifier or modifiers == Qt.ShiftModifier):
                self.browse_history(-1)
                event.accept()
                return True
            elif key == Qt.Key_Down and (modifiers == Qt.NoModifier or modifiers == Qt.ShiftModifier):
                self.browse_history(1)
                event.accept()
                return True
        
        return super().eventFilter(obj, event) # 其他事件交由父类处理
    
    def on_text_changed(self):
        """文本改变时的处理"""
        text = self.input_field.toPlainText().strip()
        
        # 动态调整高度
        self.adjust_input_height()
        
        # 更新发送按钮状态
        has_content = bool(text) or bool(self.selected_image_path) or bool(self.selected_attachment_path)
        self.send_button.setEnabled(has_content)
        
        # 重置历史浏览索引
        self.history_index = -1
    
    def adjust_input_height(self):
        """动态调整输入框高度"""
        doc = self.input_field.document()
        height = doc.size().height() + 24  # 加上padding
        height = max(44, min(120, height))
        
        # 平滑高度过渡
        if hasattr(self, 'height_animation'):
            self.height_animation.stop()
        
        self.height_animation = QPropertyAnimation(self.input_field, b"minimumHeight")
        self.height_animation.setDuration(150)
        self.height_animation.setStartValue(self.input_field.minimumHeight())
        self.height_animation.setEndValue(int(height))
        self.height_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.height_animation.start()
    
    def browse_history(self, direction: int):
        """浏览输入历史 - 使用缓存优化"""
        if not self.input_history:
            return
            
        # 使用缓存避免重复计算 (优化建议 2)
        # 注意：这里的缓存逻辑可能过于简单，如果历史记录很多，缓存会很大
        # 并且缓存键可能不是最优的，因为索引会变。
        # 一个更简单的优化可能是只缓存上一次和下一次的结果，或者完全移除这个缓存，
        # 因为浏览历史的操作频率通常不高，计算成本也相对较低。
        # 暂时按照建议实现，但标记为潜在问题。
        
        # 尝试从缓存获取
        # cache_key = f"{self.history_index}_{direction}" # 这个 key 在索引变化后会失效
        # 更可靠的 key 可能是目标索引
        target_index = self.history_index + direction
        target_index = max(-1, min(len(self.input_history) - 1, target_index))
        cache_key = str(target_index) # 使用目标索引作为 key

        if cache_key in self.history_cache:
            text = self.history_cache[cache_key]
            self.input_field.setText(text)
            # 将光标移到末尾
            cursor = self.input_field.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.input_field.setTextCursor(cursor)
            self.history_index = target_index # 更新当前索引
            return

        # --- 原有计算逻辑 ---
        # 保存当前输入（如果是第一次浏览历史）
        if self.history_index == -1:
            current_text = self.input_field.toPlainText().strip()
            if current_text:
                self.current_input = current_text
        
        # 更新索引
        self.history_index += direction
        self.history_index = max(-1, min(len(self.input_history) - 1, self.history_index))
        
        # 设置文本
        if self.history_index == -1:
            # 恢复当前输入
            text = getattr(self, 'current_input', '')
        else:
            text = self.input_history[-(self.history_index + 1)] # 注意：这里用的是更新前的 history_index
        
        # --- 计算结束 ---

        # 缓存结果
        self.history_cache[cache_key] = text

        self.input_field.setText(text)
        self.history_index = target_index # 更新当前索引

        # 将光标移到末尾
        cursor = self.input_field.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.input_field.setTextCursor(cursor)
    
    def send_message(self):
        """发送消息"""
        text = self.input_field.toPlainText().strip()
        
        if not text and not self.selected_image_path and not self.selected_attachment_path:
            return
        
        # 添加到历史记录
        if text and (not self.input_history or self.input_history[-1] != text):
            self.input_history.append(text)
            if len(self.input_history) > self.max_history:
                self.input_history.pop(0)
        
        # 发出信号
        self.sendMessage.emit(text, self.selected_image_path or "", self.selected_attachment_path or "")
        
        # 清空输入
        self.clear_input()
    
    def clear_input(self):
        """清空输入"""
        self.input_field.clear()
        self.clear_attachments()
        self.history_index = -1
    
    def select_image(self):
        """选择图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.webp);;所有文件 (*.*)"
        )
        
        if file_path:
            self.set_image_attachment(file_path)
    
    def select_attachment(self):
        """选择附件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择附件", 
            "",
            "所有文件 (*.*)"
        )
        
        if file_path:
            self.set_file_attachment(file_path)
    
    def set_image_attachment(self, file_path: str):
        """设置图片附件"""
        self.selected_image_path = file_path
        self.selected_attachment_path = None  # 清除其他附件
        self.update_preview()
    
    def set_file_attachment(self, file_path: str):
        """设置文件附件"""
        self.selected_attachment_path = file_path
        self.selected_image_path = None  # 清除图片
        self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        if self.selected_image_path:
            self.preview_icon.setText("🖼️")
            filename = os.path.basename(self.selected_image_path)
            self.preview_text.setText(f"图片: {filename}")
            self.preview_area.show()
        elif self.selected_attachment_path:
            self.preview_icon.setText("📎")
            filename = os.path.basename(self.selected_attachment_path)
            self.preview_text.setText(f"附件: {filename}")
            self.preview_area.show()
        else:
            self.preview_area.hide()
        
        # 更新发送按钮状态
        self.on_text_changed()
    
    def clear_attachments(self):
        """清除附件"""
        self.selected_image_path = None
        self.selected_attachment_path = None
        self.preview_area.hide()
        self.on_text_changed()
    
    def toggle_voice_recording(self):
        """切换语音录制"""
        self.is_recording = not self.is_recording
        
        btn = self.tool_buttons.get("voice")
        if btn:
            if self.is_recording:
                btn.setText("⏹️")
                btn.setToolTip("停止录音")
            else:
                btn.setText("🎤")
                btn.setToolTip("开始录音")
        
        self.voiceRecordingToggled.emit(self.is_recording)

    def toggle_asr_button_action(self):
        """ASR语音输入按钮点击处理"""
        if self.app_controller and hasattr(self.app_controller, 'toggle_asr_listening'):
            # AppController 会处理状态切换和UI更新信号的发射
            self.app_controller.toggle_asr_listening()
        else:
            logger.warning("ModernInputArea: AppController不可用，无法切换ASR。")
    
    def show_emoji_picker(self):
        """显示表情选择器"""
        emojis = [
            "😊", "😂", "🤣", "😍", "😘", "😗", "😙", "😚", "🙂", "🤗",
            "🤔", "😐", "😑", "😶", "🙄", "😏", "😣", "😥", "😮", "🤐",
            "😯", "😪", "😫", "🥱", "😴", "😌", "😛", "😜", "😝", "🤤",
            "👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉",
            "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔",
            "💯", "💢", "💥", "💫", "💦", "💨", "🕳️", "💭", "🗯️", "💬"
        ]
        
        # 创建表情选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择表情")
        dialog.setModal(True)
        dialog.resize(300, 200)
        
        layout = QVBoxLayout(dialog)
        
        # 表情网格
        scroll_area = QScrollArea()
        emoji_widget = QWidget()
        emoji_layout = QHBoxLayout(emoji_widget)
        emoji_layout.setSpacing(5)
        
        # 每行10个表情
        row_layout = None
        for i, emoji in enumerate(emojis):
            if i % 10 == 0:
                if row_layout:
                    emoji_layout.addLayout(row_layout)
                row_layout = QVBoxLayout()
            
            btn = QPushButton(emoji)
            btn.setFixedSize(30, 30)
            btn.clicked.connect(lambda checked, e=emoji: self.insert_emoji(e, dialog))
            row_layout.addWidget(btn)
        
        if row_layout:
            emoji_layout.addLayout(row_layout)
        
        scroll_area.setWidget(emoji_widget)
        layout.addWidget(scroll_area)
        
        dialog.exec_()
    
    def insert_emoji(self, emoji: str, dialog: QDialog):
        """插入表情"""
        cursor = self.input_field.textCursor()
        cursor.insertText(emoji)
        self.input_field.setFocus()
        dialog.accept()
    
    def take_screenshot(self):
        """截图功能"""
        print("DEBUG: ModernInputArea.take_screenshot() 方法被调用了！") # <--- 调试打印1
        
        parent_widget = self.parent() # ModernInputArea 的父组件应该是 ChatWindow
        # 在 ModernInputArea 的 __init__ 中，app_controller 已经作为参数传入并保存为 self.app_controller
        # 因此，我们应该直接使用 self.app_controller
        
        if hasattr(self, 'app_controller') and self.app_controller is not None:
            print(f"DEBUG: ModernInputArea 有 app_controller 实例: {type(self.app_controller)}")
            if hasattr(self.app_controller, 'trigger_screenshot_capture'):
                print("DEBUG: app_controller 有 trigger_screenshot_capture 方法，准备调用。")
                self.app_controller.trigger_screenshot_capture()
            else:
                print("ERROR: app_controller 实例上没有找到 trigger_screenshot_capture 方法！")
        else:
            print("ERROR: ModernInputArea 实例上没有找到 app_controller (self.app_controller is None or missing)！")
            # 作为备选，检查父组件，但这不应该是主要逻辑
            if parent_widget:
                print(f"DEBUG: ModernInputArea 的父组件是: {type(parent_widget)}")
                app_controller_instance_on_parent = getattr(parent_widget, 'app_controller', None)
                if app_controller_instance_on_parent:
                    print(f"DEBUG: 在父组件上找到了 app_controller 实例: {type(app_controller_instance_on_parent)}")
                    if hasattr(app_controller_instance_on_parent, 'trigger_screenshot_capture'):
                        print("DEBUG: 父组件的 app_controller 有 trigger_screenshot_capture 方法，准备调用。")
                        app_controller_instance_on_parent.trigger_screenshot_capture()
                    else:
                        print("ERROR: 父组件的 app_controller 实例上没有找到 trigger_screenshot_capture 方法！")
                else:
                    print("ERROR: 在父组件上也没有找到 app_controller 实例！")
            else:
                print("ERROR: ModernInputArea 没有父组件！这不正常。")
    
    def toggle_game_companion_mode(self):
        """切换游戏伴侣模式"""
        if not self.game_companion_button:
            return
            
        is_checked = self.game_companion_button.isChecked()
        if is_checked:
            self.game_companion_button.setText("🛑")  # 按下时显示停止图标
            self.game_companion_button.setToolTip("停止游戏伴侣模式")
        else:
            self.game_companion_button.setText("🎮")  # 弹起时显示游戏手柄图标
            self.game_companion_button.setToolTip("开启游戏伴侣模式")
        
        # 发送信号到ChatWindow
        self.gameCompanionModeToggled.emit(is_checked)
    
    def paste_image_from_clipboard(self):
        """从剪贴板粘贴图片"""
        clipboard = QApplication.clipboard()
        mime_data = clipboard.mimeData()
        
        if mime_data.hasImage():
            image = clipboard.image()
            if not image.isNull():
                # 保存到临时文件
                temp_path = tempfile.mktemp(suffix='.png')
                if image.save(temp_path):
                    self.set_image_attachment(temp_path)
        elif mime_data.hasUrls():
            # 处理文件拖拽
            urls = mime_data.urls()
            if urls:
                file_path = urls[0].toLocalFile()
                if file_path and os.path.exists(file_path):
                    # 判断是否为图片
                    img_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
                    if any(file_path.lower().endswith(ext) for ext in img_extensions):
                        self.set_image_attachment(file_path)
                    else:
                        self.set_file_attachment(file_path)

    def __del__(self):
        """析构函数 - 确保资源清理 (优化建议 8)"""
        if hasattr(self, 'adjust_timer'): # 检查是否有 adjust_timer 属性
            try:
                self.adjust_timer.stop()
                # self.adjust_timer.deleteLater() # QTimer 通常不需要手动 deleteLater，会被父对象管理
                logger.debug("Adjust timer stopped in ModernInputArea.__del__")
            except Exception as e:
                logger.warning(f"Error stopping adjust_timer in ModernInputArea.__del__: {e}")
        # 清理其他可能需要释放的资源，例如 history_cache
        self.history_cache.clear()
        logger.debug("ModernInputArea resources cleaned up.")

class ChatWindow(QWidget):
    """现代化聊天窗口"""
    
    # 信号定义 - 保持与原始代码兼容
    send_composed_message_signal = pyqtSignal(str, object, object)
    system_prompt_changed_signal = pyqtSignal(str)
    topic_selected_signal = pyqtSignal(str)
    new_topic_requested_signal = pyqtSignal()
    model_selected_signal = pyqtSignal(str)
    topic_deleted_signal = pyqtSignal(str)  # 新增：删除话题信号
    window_hidden_signal = pyqtSignal()
    update_image_signal = pyqtSignal(str, str)  # message_id, image_path
    
    def __init__(self, parent=None, app_controller=None):
        super().__init__(parent)
        self.app_controller = app_controller
        
        # 窗口状态
        self.is_dragging = False
        self.drag_start_position = None
        self.is_recording = False
        
        # 消息管理
        self.active_message_elements: Dict[str, Any] = {}
        self.last_history_sender_was_ai: bool = False
        self.processed_images = {}
        
        # 组件
        self.theme_manager = ThemeManager()
        self.animation_manager = AnimationManager()
        self.typing_indicator = None
        
        # SSE客户端 - 默认连接
        # self.sse_client = SSEClientWrapper(self)
        self.sse_url = "http://localhost:6005/companion/sse"
        self.sse_connected = False
        
        # VCPLog客户端 - 用于接收VCP工具调用日志推送
        self.vcplog_client = VCPLogClient("ws://localhost:6005", "123456", self.app_controller)  # 更新为正确的服务器地址和VCP_Key
        self.vcplog_client.vcp_log_received.connect(self.handle_vcp_log_message)
        self.vcplog_client.agent_message_received.connect(self.handle_agent_message)  # 🆕 新增Agent消息处理
        self.vcplog_client.connection_status_changed.connect(self.handle_vcplog_status_change)
        self.vcplog_client.error_occurred.connect(self.handle_vcplog_error)
        
        # UI组件
        # 初始化命令解析器
        self.command_parser = ChatCommandParser()
        self.tts_command_handler = TTSCommandHandler(self.command_parser)
        self.message_container = None
        self.message_layout = None
        self.input_area = None
        self.scroll_area = None
        self.status_label = None
        self.push_message_widget = None  # 推送消息折叠组件
        
        # 游戏伴侣模式相关初始化
        self.companion_config = load_companion_config()  # 加载配置
        self.game_companion_timer = QTimer(self)
        self.game_companion_timer.timeout.connect(self._trigger_timed_companion_action)
        self.is_game_companion_active = False
        self.companion_interval_seconds = self.companion_config['interval_seconds']  # 从配置文件读取
        
        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_animations()
        self.connect_signals()
        
        # 应用主题
        self.apply_theme()
        
        # 连接图片更新信号
        self.update_image_signal.connect(self._update_image_in_bubble_safe)
        
        # 自动连接SSE
        self._auto_connect_sse()
        
        # self.app_controller 引用已由构造函数参数 app_controller 处理

    def set_app_controller(self, app_controller):
        """设置AppController引用"""
        self.app_controller = app_controller

    def update_asr_button_state(self, is_asr_on: bool):
        """由AppController调用，更新ASR按钮的视觉状态"""
        if self.input_area and hasattr(self.input_area, 'tool_buttons'):
            asr_btn = self.input_area.tool_buttons.get("asr_voice_input")
            if asr_btn:
                if is_asr_on:
                    asr_btn.setText("🎙️") # 正在录音/监听的图标
                    asr_btn.setToolTip("停止语音输入")
                    # 可以添加闪烁等视觉效果
                else:
                    asr_btn.setText("🎤") # 默认图标
                    asr_btn.setToolTip("开始语音输入")
            else:
                logger.warning("ChatWindow: 未找到ASR按钮 (asr_voice_input) 来更新状态。")
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WA_NoSystemBackground, True)
        self.setAutoFillBackground(False)
        
        # 设置窗口大小和位置
        self.resize(450, 650)
        self.center_on_screen()
        
        # 设置窗口图标和标题
        self.setWindowTitle("AI 智能助手")
    
    def center_on_screen(self):
        """将窗口居中"""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
            self.move(x, y)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_container = QFrame(self)
        self.main_container.setObjectName("mainContainer")
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.main_container)
        
        # 容器布局
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = self.create_title_bar()
        container_layout.addWidget(self.title_bar)
        
        # 聊天区域
        self.chat_area = self.create_chat_area()
        container_layout.addWidget(self.chat_area, 1)
        
        # 输入区域
        # self.input_area = ModernInputArea(self, self.theme_manager) # 原来的代码
        self.input_area = ModernInputArea(self, self.theme_manager, self.app_controller) # <--- 传入 self.app_controller
        container_layout.addWidget(self.input_area)
        
        # 添加阴影效果
        self.add_shadow_effect()
    
    def create_title_bar(self) -> QWidget:
        """创建标题栏"""
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(60)
        
        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(15)
        
        # 左侧：头像和标题
        left_layout = QHBoxLayout()
        left_layout.setSpacing(12)
        
        # AI头像
        self.avatar_label = QLabel("🤖")
        self.avatar_label.setFixedSize(40, 40)
        self.avatar_label.setAlignment(Qt.AlignCenter)
        self.avatar_label.setObjectName("avatarLabel")
        
        # 标题和状态
        title_status_layout = QVBoxLayout()
        title_status_layout.setSpacing(2)
        
        self.title_label = QLabel("AI 智能助手")
        self.title_label.setObjectName("titleLabel")
        
        self.status_label = QLabel("● 在线")
        self.status_label.setObjectName("statusLabel")
        
        title_status_layout.addWidget(self.title_label)
        title_status_layout.addWidget(self.status_label)
        
        left_layout.addWidget(self.avatar_label)
        left_layout.addLayout(title_status_layout)
        
        # 右侧：操作按钮
        right_layout = QHBoxLayout()
        right_layout.setSpacing(8)
        
        # 主题切换按钮
        self.theme_button = ModernButton(self.theme_manager.get_color("theme_icon"))
        self.theme_button.setFixedSize(32, 32)
        self.theme_button.setToolTip("切换主题")
        self.theme_button.clicked.connect(self.toggle_theme)
        
        # 菜单按钮
        self.menu_button = ModernButton("☰")
        self.menu_button.setFixedSize(32, 32)
        self.menu_button.setToolTip("菜单")
        self.menu_button.clicked.connect(self.show_menu)
        
        # 最小化按钮
        self.minimize_button = ModernButton("—")
        self.minimize_button.setFixedSize(32, 32)
        self.minimize_button.setToolTip("最小化")
        self.minimize_button.clicked.connect(self.animated_hide)
        
        right_layout.addWidget(self.theme_button)
        right_layout.addWidget(self.menu_button)
        right_layout.addWidget(self.minimize_button)
        
        layout.addLayout(left_layout)
        layout.addStretch()
        layout.addLayout(right_layout)
        
        return title_bar
    
    def create_chat_area(self) -> QWidget:
        """创建聊天区域"""
        # 创建聊天区域容器
        chat_container = QWidget()
        chat_layout = QVBoxLayout(chat_container)
        chat_layout.setContentsMargins(0, 0, 0, 0)
        chat_layout.setSpacing(0)
        
        # 创建推送消息折叠组件
        self.push_message_widget = CollapsiblePushMessageWidget(self, max_messages=50)
        self.push_message_widget.message_cleared.connect(self._on_push_messages_cleared)
        self.push_message_widget.expand_state_changed.connect(self._on_push_expand_state_changed)
        
        # 将推送消息组件添加到聊天区域顶部
        chat_layout.addWidget(self.push_message_widget)
        
        # 智能滚动区域（正常聊天消息）
        self.scroll_area = SmartScrollArea(self)
        self.scroll_area.enable_scroll_animation()
        
        # 消息容器
        self.message_container = QWidget()
        self.message_layout = QVBoxLayout(self.message_container)
        self.message_layout.setContentsMargins(10, 10, 10, 10)
        self.message_layout.setSpacing(12)
        
        # 添加弹性空间，使消息从底部开始
        self.message_layout.addStretch()
        
        self.scroll_area.setWidget(self.message_container)
        
        # 将正常聊天区域添加到容器
        chat_layout.addWidget(self.scroll_area, 1)  # 占用剩余空间
        
        return chat_container
    
    def add_shadow_effect(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(8)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.main_container.setGraphicsEffect(shadow)
    
    def setup_animations(self):
        """设置动画"""
        # 窗口显示动画
        self.show_animation = QPropertyAnimation(self, b"windowOpacity")
        self.show_animation.setDuration(300)
        self.show_animation.setStartValue(0.0)
        self.show_animation.setEndValue(1.0)
        self.show_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 窗口隐藏动画
        self.hide_animation = QPropertyAnimation(self, b"windowOpacity")
        self.hide_animation.setDuration(250)
        self.hide_animation.setStartValue(1.0)
        self.hide_animation.setEndValue(0.0)
        self.hide_animation.setEasingCurve(QEasingCurve.InCubic)
        self.hide_animation.finished.connect(self.hide)
        
        # 主题切换动画
        self.theme_transition = QPropertyAnimation(self.main_container, b"geometry")
        self.theme_transition.setDuration(200)
        self.theme_transition.setEasingCurve(QEasingCurve.OutCubic)
    
    def connect_signals(self):
        """连接信号槽"""
        # 输入区域信号
        if self.input_area:
            self.input_area.sendMessage.connect(self.handle_send_message)
            self.input_area.voiceRecordingToggled.connect(self.handle_voice_recording)
            
            # 连接游戏伴侣模式信号
            if hasattr(self.input_area, 'gameCompanionModeToggled'):
                self.input_area.gameCompanionModeToggled.connect(self.handle_game_companion_toggle)
            
            # 模型选择信号
            if hasattr(self.input_area, 'model_selector'):
                self.input_area.model_selector.currentTextChanged.connect(self.on_model_changed)
        
        # SSE客户端信号
        # self.sse_client.dataReceived.connect(self._handle_sse_data)
        # self.sse_client.statusChanged.connect(self._handle_sse_connection_status)
        # self.sse_client.errorOccurred.connect(self._handle_sse_error)
        
        # 自动启动VCPLog连接
        if hasattr(self, 'vcplog_client'):
            # 延迟启动，确保UI完全初始化
            QTimer.singleShot(1000, self.start_vcplog_connection)
    
    def _on_push_messages_cleared(self):
        """推送消息清空时的处理"""
        logger.info("推送消息已清空")
    
    def _on_push_expand_state_changed(self, is_expanded: bool):
        """推送消息展开状态变化处理"""
        logger.debug(f"推送消息区域{'展开' if is_expanded else '折叠'}")
        
        # 可以在这里添加其他UI调整逻辑
        # 比如调整聊天区域的高度等
    
    def apply_theme(self):
        """应用主题"""
        # 应用窗口样式
        window_style = self.theme_manager.apply_window_style(self)
        
        # 获取主题颜色
        theme = self.theme_manager.get_current_theme()
        
        # 主容器样式
        main_container_style = f"""
        QFrame#mainContainer {{
            background-color: {theme['window_bg']};
            border-radius: 20px;
            border: 1px solid {theme['border_color']};
        }}
        """
        
        # 标题栏样式
        title_bar_style = f"""
        QFrame#titleBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {theme['window_bg']}, 
                stop:1 rgba(0,0,0,0.05));
            border-radius: 20px 20px 0 0;
            border-bottom: 1px solid {theme['separator_color']};
        }}
        QLabel#avatarLabel {{
            background-color: {theme['primary']};
            border-radius: 20px;
            font-size: 20px;
            color: {theme['text_inverse']};
        }}
        QLabel#titleLabel {{
            color: {theme['text_primary']};
            font-size: 16px;
            font-weight: 600;
        }}
        QLabel#statusLabel {{
            color: {theme['online_color']};
            font-size: 12px;
        }}
        """
        
        # 聊天区域样式
        chat_area_style = self.theme_manager.apply_chat_area_style()
        
        # 合并所有样式
        full_style = window_style + main_container_style + title_bar_style + chat_area_style
        self.setStyleSheet(full_style)
        
        # 应用按钮样式
        if hasattr(self, 'theme_button'):
            self.theme_button.set_style_type("tool", self.theme_manager)
        if hasattr(self, 'menu_button'):
            self.menu_button.set_style_type("tool", self.theme_manager)
        if hasattr(self, 'minimize_button'):
            self.minimize_button.set_style_type("tool", self.theme_manager)
        
        # 更新主题图标
        if hasattr(self, 'theme_button'):
            self.theme_button.setText(theme.get('theme_icon', '🌗'))
        
        # 更新推送消息组件主题
        if hasattr(self, 'push_message_widget') and self.push_message_widget:
            is_dark_theme = self.theme_manager.current_theme == "dark"
            self.push_message_widget.update_theme(is_dark_theme)
        
    
    def toggle_theme(self):
        """切换主题"""
        old_theme = self.theme_manager.current_theme
        new_theme = self.theme_manager.toggle_theme()
        
        logger.info(f"Theme changed from {old_theme} to {new_theme}")
        
        # 重新应用主题
        self.apply_theme()
        
        # 如果有输入区域，也需要重新应用样式
        if self.input_area:
            self.input_area.apply_styling()
        
        # 刷新所有现有的消息气泡
        self.refresh_message_bubbles()
        
        # 显示主题切换提示
        self.show_theme_changed_notification(new_theme)
    
    def refresh_message_bubbles(self):
        """刷新所有消息气泡的样式"""
        for i in range(self.message_layout.count() - 1):  # -1 因为最后一个是stretch
            item = self.message_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, MessageBubble):
                    widget.theme_manager = self.theme_manager
                    widget.apply_styling()
    
    def show_theme_changed_notification(self, theme_name: str):
        """显示主题切换通知"""
        theme_names = {"dark": "深色主题", "light": "浅色主题", "auto": "自动主题"}
        display_name = theme_names.get(theme_name, theme_name)
        
        # 创建临时通知气泡
        notification = MessageBubble("系统", f"已切换到{display_name}")
        self.add_message_bubble(notification)
        
        # 3秒后自动移除
        QTimer.singleShot(3000, lambda: self.remove_message_bubble(notification))
    
    def show_menu(self):
        """显示菜单"""
        menu = QMenu(self)
        
        # 设置菜单样式
        theme = self.theme_manager.get_current_theme()
        menu.setStyleSheet(f"""
            QMenu {{
                background-color: {theme['window_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 12px;
                padding: 8px;
                color: {theme['text_primary']};
            }}
            QMenu::item {{
                padding: 8px 16px;
                border-radius: 6px;
                margin: 2px;
            }}
            QMenu::item:selected {{
                background-color: {theme['primary']};
                color: {theme['text_inverse']};
            }}
            QMenu::separator {{
                height: 1px;
                background-color: {theme['separator_color']};
                margin: 4px 8px;
            }}
        """)
        
        # 菜单项
        new_topic_action = menu.addAction("✨ 新对话")
        new_topic_action.triggered.connect(self.new_topic_requested_signal.emit)
        
        history_action = menu.addAction("📜 历史记录")
        history_action.triggered.connect(self.show_history)
        
        menu.addSeparator()
        
        # 模型选择子菜单
        model_menu = menu.addMenu("🤖 选择模型")
        if self.app_controller and hasattr(self.app_controller, 'available_models'):
            current_model = self.app_controller.config.get('api_model_name', '')
            model_group = QActionGroup(model_menu)
            
            for model_name in self.app_controller.available_models:
                action = model_menu.addAction(model_name)
                action.setCheckable(True)
                action.setChecked(model_name == current_model)
                action.triggered.connect(lambda checked, m=model_name: self.model_selected_signal.emit(m))
                model_group.addAction(action)
        
        menu.addSeparator()
        
        settings_action = menu.addAction("⚙️ 设置")
        settings_action.triggered.connect(self.show_settings)
        
        about_action = menu.addAction("ℹ️ 关于")
        about_action.triggered.connect(self.show_about)
        
        # 显示菜单
        button_pos = self.menu_button.mapToGlobal(QPoint(0, self.menu_button.height() + 5))
        menu.exec_(button_pos)
    
    def show_settings(self):
        """显示设置对话框"""
        current_prompt = ""
        if self.app_controller and hasattr(self.app_controller, 'system_prompt'):
            current_prompt = self.app_controller.system_prompt or ""
        
        # 创建设置对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("设置")
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # 系统提示词设置
        prompt_label = QLabel("系统提示词:")
        prompt_edit = QTextEdit()
        prompt_edit.setPlainText(current_prompt)
        prompt_edit.setMaximumHeight(150)
        
        layout.addWidget(prompt_label)
        layout.addWidget(prompt_edit)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        # 应用对话框样式
        theme = self.theme_manager.get_current_theme()
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
            QTextEdit {{
                background-color: {theme['input_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                padding: 8px;
                color: {theme['text_primary']};
            }}
        """)
        
        if dialog.exec_() == QDialog.Accepted:
            new_prompt = prompt_edit.toPlainText().strip()
            self.system_prompt_changed_signal.emit(new_prompt)
    
    def show_about(self):
        """显示关于对话框"""
        from PyQt5.QtWidgets import QMessageBox
        
        msg = QMessageBox(self)
        msg.setWindowTitle("关于")
        msg.setIconPixmap(QPixmap())  # 可以设置图标
        msg.setText("AI 智能助手")
        msg.setInformativeText(
            "一个现代化的AI聊天助手\n"
            "支持多种AI模型和丰富的交互功能\n\n"
            "版本: 2.0\n"
            "基于 PyQt5 开发"
        )
        
        # 应用主题样式
        theme = self.theme_manager.get_current_theme()
        msg.setStyleSheet(f"""
            QMessageBox {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
        """)
        
        msg.exec_()
    
    def show_history(self):
        """显示历史记录"""
        if not self.app_controller or not hasattr(self.app_controller, 'get_all_topics_for_display'):
            self.add_system_message("无法加载历史记录")
            return
        
        topics = self.app_controller.get_all_topics_for_display()
        if not topics:
            self.add_system_message("没有历史对话记录")
            return
        
        # 创建历史记录对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("历史对话")
        dialog.setModal(False)
        dialog.resize(500, 600)
        
        layout = QVBoxLayout(dialog)
        
        # 搜索框
        search_edit = QLineEdit()
        search_edit.setPlaceholderText("搜索对话...")
        layout.addWidget(search_edit)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        for topic_data in topics:
            title = topic_data.get("title", "无标题")
            last_updated = topic_data.get("last_updated", "")
            topic_id = topic_data.get("id")
            
            # 格式化时间
            if last_updated:
                try:
                    dt = datetime.fromisoformat(last_updated.replace("Z", "+00:00"))
                    time_str = dt.strftime("%m-%d %H:%M")
                except:
                    time_str = last_updated
            else:
                time_str = "未知时间"
            
            # 创建话题项容器
            topic_frame = QFrame()
            topic_frame.setFrameStyle(QFrame.Box)
            topic_layout = QHBoxLayout(topic_frame)
            
            # 话题信息
            info_layout = QVBoxLayout()
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
            time_label = QLabel(time_str)
            time_label.setStyleSheet("color: gray; font-size: 12px;")
            
            info_layout.addWidget(title_label)
            info_layout.addWidget(time_label)
            topic_layout.addLayout(info_layout)
            
            # 按钮容器
            button_layout = QVBoxLayout()
            
            # 选择按钮
            select_btn = QPushButton("选择")
            select_btn.clicked.connect(lambda checked, tid=topic_id: self._select_topic_by_id(tid, dialog))
            select_btn.setFixedSize(60, 30)
            
            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, tid=topic_id, ttitle=title: self._confirm_delete_topic(tid, ttitle, dialog))
            delete_btn.setFixedSize(60, 30)
            delete_btn.setStyleSheet("QPushButton { background-color: #FF3B30; color: white; }")
            
            button_layout.addWidget(select_btn)
            button_layout.addWidget(delete_btn)
            topic_layout.addLayout(button_layout)
            
            scroll_layout.addWidget(topic_frame)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 搜索功能
        def filter_topics(text):
            for i in range(scroll_layout.count()):
                frame = scroll_layout.itemAt(i).widget()
                if isinstance(frame, QFrame):
                    title_label = frame.findChild(QLabel)
                    if title_label:
                        visible = text.lower() in title_label.text().lower()
                        frame.setVisible(visible)
        
        search_edit.textChanged.connect(filter_topics)
        
        # 应用样式
        self._apply_dialog_style(dialog)
        
        dialog.show()
    
    def _select_topic(self, item: QListWidgetItem, dialog: QDialog):
        """选择话题"""
        topic_id = item.data(Qt.UserRole)
        if topic_id:
            self.topic_selected_signal.emit(topic_id)
            dialog.accept()
    
    def _select_topic_by_id(self, topic_id: str, dialog: QDialog):
        """通过ID选择话题"""
        if topic_id:
            self.topic_selected_signal.emit(topic_id)
            dialog.accept()
    
    def _confirm_delete_topic(self, topic_id: str, title: str, dialog: QDialog):
        """确认删除话题"""
        from PyQt5.QtWidgets import QMessageBox
        
        # 创建确认对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("确认删除")
        msg_box.setText(f"确定要删除对话 '{title}' 吗？")
        msg_box.setInformativeText("此操作无法撤销。")
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.No)
        
        # 设置按钮文本
        yes_button = msg_box.button(QMessageBox.Yes)
        no_button = msg_box.button(QMessageBox.No)
        yes_button.setText("删除")
        no_button.setText("取消")
        
        # 应用样式
        theme = self.theme_manager.get_current_theme()
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
            QMessageBox QPushButton {{
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 60px;
            }}
            QMessageBox QPushButton[text="删除"] {{
                background-color: #FF3B30;
                color: white;
                border: none;
            }}
            QMessageBox QPushButton[text="取消"] {{
                background-color: {theme['input_bg']};
                color: {theme['text_primary']};
                border: 1px solid {theme['border_color']};
            }}
        """)
        
        # 显示确认对话框
        result = msg_box.exec_()
        
        if result == QMessageBox.Yes:
            # 发出删除信号
            self.topic_deleted_signal.emit(topic_id)
            # 关闭历史对话框并重新打开以刷新列表
            dialog.accept()
            # 延迟重新打开历史对话框
            QTimer.singleShot(100, self.show_history)
    
    def _apply_dialog_style(self, dialog: QDialog):
        """应用对话框样式"""
        theme = self.theme_manager.get_current_theme()
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {theme['window_bg']};
                color: {theme['text_primary']};
            }}
            QLineEdit {{
                background-color: {theme['input_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                padding: 8px;
                color: {theme['text_primary']};
            }}
            QListWidget {{
                background-color: {theme['chat_bg']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                color: {theme['text_primary']};
            }}
            QListWidget::item {{
                padding: 12px;
                border-radius: 6px;
                margin: 2px;
            }}
            QListWidget::item:selected {{
                background-color: {theme['primary']};
                color: {theme['text_inverse']};
            }}
            QListWidget::item:hover {{
                background-color: {theme['border_color']};
            }}
        """)
    def _display_command_result(self, result: CommandResult):
        """
        显示命令执行结果
        
        Args:
            result: 命令执行结果
        """
        # 使用新的 _add_system_message 方法来显示结果
        if result.success:
            self._add_system_message(result.message, "success")
        else:
            self._add_system_message(result.message, "error")

    def _add_system_message(self, message: str, message_type: str = "info"):
        """
        添加带特定类型的系统消息到聊天窗口
        
        Args:
            message: 消息内容
            message_type: 消息类型 ("info", "success", "error", "warning")
        """
        sender_prefix = ""
        actual_sender = "系统" # 默认 MessageBubble sender for system messages

        if message_type == "success":
            sender_prefix = "✅ "
        elif message_type == "error":
            sender_prefix = "❌ "
            actual_sender = "SystemError" # This is recognized by MessageBubble as is_system
        elif message_type == "warning":
            sender_prefix = "⚠️ "
        else: # info
            sender_prefix = "ℹ️ "

        formatted_message = sender_prefix + message
        
        system_bubble = MessageBubble(actual_sender, formatted_message, datetime.now(), self)
        self.add_message_bubble(system_bubble)
        
        logger.info(f"System message ({message_type}) added to chat: {formatted_message}")

    def _handle_normal_message(self, text: str, image_path: Optional[str], attachment_path: Optional[str]):
        """
        处理普通聊天消息的发送逻辑 (发送信号并显示打字指示器)
        用户消息气泡应在此方法调用前已添加。
        """
        if self.app_controller:
            self.send_composed_message_signal.emit(text,
                                                 image_path if image_path else None,
                                                 attachment_path if attachment_path else None)
            self.show_typing_indicator()
        else:
            logger.warning("AppController not set in ChatWindow, cannot process normal message via signal.")

    def handle_send_message(self, text: str, image_path: str, attachment_path: str):
        """处理用户输入（包括普通消息和命令）"""
        
        original_text = text
        original_image_path = image_path
        original_attachment_path = attachment_path

        # 检查是否有任何输入内容
        if not text.strip() and not original_image_path and not original_attachment_path:
            return

        # 用户输入的消息气泡应该总是先显示
        # (确保即使文本为空但有附件时也显示用户气泡)
        if text or original_image_path or original_attachment_path: # 只有当有内容时才创建气泡
            user_bubble = MessageBubble("User", text, datetime.now(), self)
            if original_image_path and os.path.exists(original_image_path):
                user_bubble.set_image(original_image_path)
            self.add_message_bubble(user_bubble)

        # 首先尝试处理命令 (仅当有文本输入时)
        if text.strip(): # 命令通常是纯文本
            if not hasattr(self, 'command_parser'): # 防御性检查
                logger.error("Command parser not initialized in ChatWindow.")
                # 如果 command_parser 未初始化，则作为普通消息处理
                self._handle_normal_message(original_text, original_image_path, original_attachment_path)
                return

            command_result = self.command_parser.execute_command(text, self.app_controller)
            if command_result: # Assumes execute_command returns a CommandResult object or None/False
                self._display_command_result(command_result)
                
                # 特殊处理成功的TTS切换命令
                if (command_result.success and
                    command_result.data and
                    isinstance(command_result.data, dict) and
                    'service_id' in command_result.data):
                    logger.info(f"TTS service switched successfully via command: {command_result.data}")
                return # 命令已处理，结束

        # 如果不是命令 (或者 command_parser 未初始化，或者 execute_command 返回 None/False)
        self._handle_normal_message(original_text, original_image_path, original_attachment_path)
    
    def on_model_changed(self, model_name: str):
        """模型改变时的处理"""
        if model_name:
            self.model_selected_signal.emit(model_name)
            self.add_system_message(f"已切换到模型: {model_name}")
    
    def handle_voice_recording(self, is_recording: bool):
        """处理语音录制"""
        if is_recording:
            self.add_system_message("开始录音...")
            self.status_label.setText("🎤 录音中")
            self.status_label.setStyleSheet("color: #FF3B30;")
        else:
            self.add_system_message("录音结束")
            self.status_label.setText("● 在线")
            theme = self.theme_manager.get_current_theme()
            self.status_label.setStyleSheet(f"color: {theme['online_color']};")
    
    def handle_game_companion_toggle(self, activate: bool):
        """处理游戏伴侣模式切换"""
        self.is_game_companion_active = activate
        
        if activate:
            # 启动定时器
            self.game_companion_timer.start(self.companion_interval_seconds * 1000)  # 转为毫秒
            
            # 显示启动消息
            self.add_system_message(
                f"🎮 游戏伴侣模式已开启 (每 {self.companion_interval_seconds} 秒分析一次屏幕)"
            )
            logger.info(f"游戏伴侣模式已开启，截图间隔: {self.companion_interval_seconds}s")
        else:
            # 停止定时器
            self.game_companion_timer.stop()
            
            # 显示停止消息
            self.add_system_message("🛑 游戏伴侣模式已关闭")
            logger.info("游戏伴侣模式已关闭")
    
    def _trigger_timed_companion_action(self):
        """游戏伴侣定时分析核心方法"""
        if not self.is_game_companion_active or not self.app_controller:
            return
        
        logger.info("游戏伴侣：定时器触发，执行全屏截图并发送")
        
        # 调用AppController的全屏截图方法
        if hasattr(self.app_controller, 'capture_full_screen_and_get_path'):
            screenshot_path = self.app_controller.capture_full_screen_and_get_path()
            
            if screenshot_path and os.path.exists(screenshot_path):
                logger.info(f"游戏伴侣：获取到全屏截图 {screenshot_path}，准备发送")
                
                # 从配置文件读取提示词
                prompt_text = self.companion_config['prompt']
                
                # 通过现有消息处理流程发送
                self.app_controller._handle_composed_message(
                    message_text=prompt_text,
                    image_path=screenshot_path,
                    audio_data_url=None
                )
            else:
                logger.warning("游戏伴侣：自动全屏截图失败或未返回有效路径")
                self.add_system_message("⚠️ 游戏伴侣：截屏失败")
        else:
            logger.error("游戏伴侣：AppController 缺少 capture_full_screen_and_get_path 方法")
            self.add_system_message("❌ 游戏伴侣：截图功能配置错误")
            # 发生错误时自动关闭模式
            self.handle_game_companion_toggle(False)
            if self.input_area and self.input_area.game_companion_button:
                self.input_area.game_companion_button.setChecked(False)
                self.input_area.toggle_game_companion_mode()
    
    # === 消息管理方法 ===
    
    def add_message_bubble(self, bubble: MessageBubble):
        """添加消息气泡"""
        # 插入到stretch之前
        index = self.message_layout.count() - 1
        self.message_layout.insertWidget(index, bubble)
        
        # 自动滚动到底部
        QTimer.singleShot(100, self.scroll_area.scroll_to_bottom)
    
    def remove_message_bubble(self, bubble: MessageBubble): # 或者 bubble: QWidget
        """移除消息气泡"""
        # 检查 bubble 是否有效并且确实在布局中
        if bubble is None: # 如果 bubble 是 None，直接返回
            return

        found_and_removed = False
        for i in range(self.message_layout.count()):
            item = self.message_layout.itemAt(i)
            if item and item.widget() == bubble:
                # 从布局中移除项目
                self.message_layout.takeAt(i) # 或者 item.widget().setParent(None)
                # 彻底删除控件
                bubble.deleteLater()
                found_and_removed = True
                break # 找到并移除后就可以退出了

        if not found_and_removed:
            # 如果代码走到这里，说明传入的 bubble 不在布局中
            # 这可能是正常情况（比如它已经被移除了），也可能是逻辑问题
            # 可以选择性地打印一个警告，但对于 typing_indicator 来说，可能多次调用 hide 是正常的
            # logger.warning(f"Attempted to remove a bubble that was not found in the layout: {bubble}")
            pass
    
    def add_system_message(self, text: str):
        """添加系统消息"""
        system_bubble = MessageBubble("系统", text, datetime.now(), self)
        self.add_message_bubble(system_bubble)
    
    def show_typing_indicator(self):
        """显示打字指示器"""
        if self.typing_indicator:
            return
        
        self.typing_indicator = TypingIndicator(self)
        self.add_message_bubble(self.typing_indicator)
        self.typing_indicator.start_animation()
    
    def hide_typing_indicator(self):
        """隐藏打字指示器"""
        if self.typing_indicator:
            self.remove_message_bubble(self.typing_indicator)
            self.typing_indicator = None
    
    # === VCPLog消息处理方法 ===
    
    def handle_vcp_log_message(self, message_data: Dict[str, Any]):
        """处理VCPLog接收到的VCP工具调用日志"""
        try:
            if VCPLogMessageHandler.is_vcp_log_message(message_data):
                # 将VCP消息路由到推送消息组件
                if hasattr(self, 'push_message_widget') and self.push_message_widget:
                    self.push_message_widget.add_vcp_message(message_data)
                    
                    # 记录日志
                    data = message_data.get('data', {})
                    tool_name = data.get('tool_name', 'Unknown')
                    status = data.get('status', 'unknown')
                    logger.info(f"VCP日志已添加到推送消息区域 (工具: {tool_name}, 状态: {status})")
                else:
                    # 降级处理：如果推送消息组件不可用，使用原来的方式
                    formatted_message = VCPLogMessageHandler.format_vcp_log_message(message_data)
                    vcp_bubble = MessageBubble(
                        formatted_message['sender'],
                        formatted_message['content'],
                        datetime.now(),
                        self
                    )
                    self.add_message_bubble(vcp_bubble)
                    logger.warning("推送消息组件不可用，VCP消息显示在正常聊天区域")
                
        except Exception as e:
            logger.error(f"处理VCP日志消息时出错: {e}")
    
    def handle_vcplog_status_change(self, is_connected: bool):
        """处理VCPLog连接状态变化"""
        status_text = "已连接" if is_connected else "未连接"
        logger.info(f"VCPLog状态: {status_text}")
        
        # 在界面上显示连接状态
        if hasattr(self, 'status_label') and self.status_label:
            if is_connected:
                self.status_label.setText("● VCPLog已连接")
                self.status_label.setStyleSheet("color: #4CAF50;")
            else:
                self.status_label.setText("● 在线")
                theme = self.theme_manager.get_current_theme()
                self.status_label.setStyleSheet(f"color: {theme['online_color']};")
      def handle_vcplog_error(self, error_message: str):
        """处理VCPLog错误"""
        logger.error(f"VCPLog错误: {error_message}")
        # 在界面上显示错误消息
        self.add_system_message(f"VCPLog连接错误: {error_message}")
    
    def handle_agent_message(self, message_data: Dict[str, Any]):
        """处理AgentMessage插件推送的AI通知消息 🆕"""
        try:
            from websocket_client import AgentMessageHandler
            
            if AgentMessageHandler.is_agent_message(message_data):
                # 将Agent消息路由到推送消息组件
                if hasattr(self, 'push_message_widget') and self.push_message_widget:
                    self.push_message_widget.add_agent_message(message_data)
                    
                    # 记录日志
                    message_content = message_data.get('message', '')
                    recipient = message_data.get('recipient', 'AI助手')
                    logger.info(f"Agent消息已添加到推送消息区域 (发送者: {recipient}, 内容: {message_content[:50]}...)")
                    
                    # 可选：显示桌面通知
                    if hasattr(self.app_controller, 'show_desktop_notification'):
                        self.app_controller.show_desktop_notification(
                            title=f"来自 {recipient} 的消息",
                            message=message_content[:100]
                        )
                else:
                    # 降级处理：如果推送消息组件不可用，使用原来的方式
                    formatted_message = AgentMessageHandler.format_agent_message(message_data)
                    agent_bubble = MessageBubble(
                        formatted_message['sender'],
                        formatted_message['content'],
                        datetime.now(),
                        self
                    )
                    self.add_message_bubble(agent_bubble)
                    logger.warning("推送消息组件不可用，Agent消息显示在正常聊天区域")
                
        except Exception as e:
            logger.error(f"处理Agent消息时出错: {e}")
    
    def start_vcplog_connection(self):
        """启动VCPLog连接"""
        if hasattr(self, 'vcplog_client'):
            # 尝试从配置或环境变量获取VCP_Key
            vcp_key = self._get_vcp_key_from_config()
            if vcp_key:
                self.vcplog_client.update_vcp_key(vcp_key)
                self.vcplog_client.connect()
                logger.info("正在启动VCPLog连接...")
            else:
                logger.warning("VCP_Key未配置，无法连接VCPLog服务")
                self.add_system_message("⚠️ VCP_Key未配置，无法连接VCPLog服务")
    
    def stop_vcplog_connection(self):
        """停止VCPLog连接"""
        if hasattr(self, 'vcplog_client'):
            self.vcplog_client.disconnect()
            logger.info("已停止VCPLog连接")
    
    def _get_vcp_key_from_config(self) -> str:
        """从配置中获取VCP_Key"""
        try:
            print("DEBUG: 开始获取VCP_Key")
            # 优先从app_controller的配置中获取
            if (self.app_controller and
                hasattr(self.app_controller, 'config') and
                hasattr(self.app_controller.config, 'get')):
                vcp_key = self.app_controller.config.get('VCP_Key', '')
                print(f"DEBUG: 从app_controller获取VCP_Key: '{vcp_key}'")
                if vcp_key:
                    return vcp_key
            
            # 尝试从环境变量获取
            import os
            vcp_key = os.getenv('VCP_Key', '')
            print(f"DEBUG: 从环境变量获取VCP_Key: '{vcp_key}'")
            if vcp_key:
                return vcp_key
                
            # 尝试从配置文件获取
            config_paths = [
                'config.env',
                'Plugin/VCPLog/config.env',
                os.path.join(os.path.dirname(__file__), '..', 'config.env'),
                os.path.join(os.path.dirname(__file__), 'config.env')
            ]
            
            print(f"DEBUG: 配置文件搜索路径: {config_paths}")
            
            for config_path in config_paths:
                print(f"DEBUG: 检查配置文件: {config_path}")
                if os.path.exists(config_path):
                    print(f"DEBUG: 配置文件存在: {config_path}")
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"DEBUG: 配置文件内容: '{content}'")
                        import re
                        match = re.search(r'VCP_Key\s*=\s*(.+)', content)
                        if match:
                            result = match.group(1).strip().strip('"').strip("'")
                            print(f"DEBUG: 正则匹配到VCP_Key: '{result}'")
                            return result
                        else:
                            print("DEBUG: 正则匹配失败")
                else:
                    print(f"DEBUG: 配置文件不存在: {config_path}")
                            
        except Exception as e:
            print(f"DEBUG: 获取VCP_Key时出错: {e}")
            logger.warning(f"获取VCP_Key时出错: {e}")
        
        print("DEBUG: 所有方法都未获取到VCP_Key，返回空字符串")
        return ""
    
    def cleanup_on_app_exit(self):
        """应用程序真正退出时的清理方法"""
        logger.info("应用程序退出，清理VCPLog连接...")
        if hasattr(self, 'vcplog_client'):
            self.vcplog_client.disconnect()
        logger.info("VCPLog连接已清理")
    
    # === 流式响应处理方法 ===
    
    def handle_app_ai_stream_start(self, message_id: str, sender_override: Optional[str] = None):
        """处理AI流式响应开始"""
        logger.info(f"🚨🚨🚨 ChatWindow.handle_app_ai_stream_start 被调用: {message_id}, sender: {sender_override}")
        print(f"🚨🚨🚨 ChatWindow.handle_app_ai_stream_start 被调用: {message_id}, sender: {sender_override}")
        
        # 隐藏打字指示器
        self.hide_typing_indicator()
        
        sender = sender_override if sender_override else "AI"
        
        # 创建新的消息气泡
        ai_bubble = MessageBubble(sender, "", datetime.now(), self)
        self.add_message_bubble(ai_bubble)
        
        # 存储到活动消息中
        self.active_message_elements[message_id] = {
            'bubble_instance': ai_bubble,
            'sender_role': sender,
            'full_content': "",
            'start_time': datetime.now()
        }
        
        self.last_history_sender_was_ai = (sender.lower() == "ai")
    
    def handle_app_ai_stream_chunk(self, message_id: str, text_chunk: str):
        """处理AI流式响应片段"""
        logger.info(f"🚨🚨🚨 ChatWindow.handle_app_ai_stream_chunk 被调用: {message_id}, chunk长度: {len(text_chunk)}")
        print(f"🚨🚨🚨 ChatWindow.handle_app_ai_stream_chunk 被调用: {message_id}, chunk: {text_chunk}")
        
        if message_id not in self.active_message_elements:
            logger.warning(f"Message ID {message_id} not found in active elements")
            return
        
        msg_info = self.active_message_elements[message_id]
        bubble_instance = msg_info.get('bubble_instance')
        
        if bubble_instance:
            # 累积内容
            msg_info['full_content'] += text_chunk
            
            # 在流式更新过程中，检查是否有完整的img标签
            full_content = msg_info['full_content']
            print(f"🔥🔥 累积内容: {full_content}")
            
            # 检查是否有完整的img标签，如果有就实时显示
            import re
            complete_img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
            complete_images = re.findall(complete_img_pattern, full_content)
            
            if complete_images:
                print(f"🔥🔥 发现完整图片标签: {complete_images}")
                # 如果有完整的图片，使用混合内容更新
                bubble_instance.update_content(full_content)
            else:
                # 如果没有完整图片，只更新文本内容
                display_text = self._remove_img_tags(full_content)
                bubble_instance._update_streaming_text(display_text)
            
            # 自动滚动
            self.scroll_area.scroll_to_bottom()
    
    def handle_app_ai_image_display(self, message_id: str, local_path: str, original_url: str, width: Optional[int], height: Optional[int]):
        """处理AI图片显示"""
        logger.info(f"🔥 handle_app_ai_image_display 被调用: {message_id}, path: {local_path}")
        
        # 暂时禁用旧的图片显示逻辑，因为我们现在使用混合内容显示
        logger.info(f"🔥 跳过旧的图片显示逻辑，使用新的混合内容模式")
        return
        
        # 以下是旧的逻辑，已禁用
        if message_id not in self.active_message_elements:
            logger.warning(f"Message ID {message_id} not found for image display")
            return
        
        msg_info = self.active_message_elements[message_id]
        bubble_instance = msg_info.get('bubble_instance')
        
        if bubble_instance and os.path.exists(local_path):
            # 设置图片，使用指定的尺寸或默认最大宽度
            max_width = width if width and width > 0 else 250
            bubble_instance.set_image(local_path, max_width)
            
            # 自动滚动
            self.scroll_area.scroll_to_bottom()
    
    def handle_app_ai_raw_block(self, message_id: str, block_type: str, raw_block_content: str):
        """处理AI原始块内容"""
        logger.info(f"Raw block: {message_id}, type: {block_type}")
        
        if message_id not in self.active_message_elements:
            logger.warning(f"Message ID {message_id} not found for raw block")
            return
        
        msg_info = self.active_message_elements[message_id]
        bubble_instance = msg_info.get('bubble_instance')
        
        if bubble_instance:
            # 添加原始块到内容
            block_text = f"\n\n[{block_type}]\n{raw_block_content}\n[/{block_type}]"
            msg_info['full_content'] += block_text
            
            # 在流式过程中使用流式文本更新，避免重建导致图片消失
            display_text = self._remove_img_tags(msg_info['full_content'])
            bubble_instance._update_streaming_text(display_text)
            
            # 自动滚动
            self.scroll_area.scroll_to_bottom()
    
    def handle_ai_stream_finished(self, message_id: str, full_text_for_tts: str, original_message_obj: Optional[Any] = None, last_event_data: Optional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None):
        """处理AI流式响应结束"""
        print(f"🚨🚨🚨 ChatWindow.handle_ai_stream_finished 被调用: {message_id}")
        logger.info(f"🚨🚨🚨 ChatWindow.handle_ai_stream_finished 被调用: {message_id}")
        
        # 确保隐藏打字指示器（多重保障）
        self.hide_typing_indicator()
        
        logger.info(f"🔥🔥🔥🔥🔥 Stream finished: {message_id}")
        
        metadata = metadata or {}
        is_final = metadata.get("is_final_stream_end", False)
        is_error = metadata.get("is_error", False)
        
        logger.info(f"🔥🔥🔥🔥🔥 is_final: {is_final}, is_error: {is_error}")
        
        if message_id in self.active_message_elements:
            msg_info = self.active_message_elements[message_id]
            bubble_instance = msg_info.get('bubble_instance')
            
            logger.info(f"🔥🔥🔥🔥🔥 找到消息元素，bubble_instance存在: {bubble_instance is not None}")
            
            if bubble_instance:
                if is_final:
                    logger.info(f"🔥🔥🔥🔥🔥 处理最终流结束")
                    # 流式结束时，检查是否需要完整重建内容
                    full_content_with_images = msg_info['full_content']
                    
                    logger.info(f"🔥🔥🔥🔥🔥 完整内容长度: {len(full_content_with_images)}")
                    logger.info(f"🔥🔥🔥🔥🔥 完整内容前100字符: {full_content_with_images[:100]}")
                    
                    # 检查内容中是否有图片标签
                    import re
                    img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
                    has_images = bool(re.search(img_pattern, full_content_with_images))
                    
                    logger.info(f"🔥🔥🔥🔥🔥 检测到图片: {has_images}")
                    
                    if has_images:
                        logger.info(f"🔥🔥🔥🔥🔥 调用 _finalize_mixed_content")
                        # 如果有图片，使用智能更新避免清空已加载的图片
                        bubble_instance._finalize_mixed_content(full_content_with_images)
                    else:
                        logger.info(f"🔥🔥🔥🔥🔥 没有图片，只更新文本")
                        # 如果没有图片，直接更新文本即可
                        final_content = self._remove_img_tags(full_content_with_images)
                        bubble_instance._update_streaming_text(final_content)
                else:
                    # 非最终结束时，只更新文本内容
                    final_content = self._remove_img_tags(msg_info['full_content'])
                    bubble_instance._update_streaming_text(final_content)
                
                # 如果是错误，高亮显示
                if is_error:
                    bubble_instance.highlight_briefly(2000)
            
            # 如果是最终结束，清理活动消息
            if is_final:
                del self.active_message_elements[message_id]
        
        # 更新状态
        if is_final:
            sender_role = ""
            if message_id in self.active_message_elements:
                sender_role = self.active_message_elements[message_id].get('sender_role', "").lower()
            
            if sender_role == "ai":
                self.last_history_sender_was_ai = True
            elif sender_role == "user":
                self.last_history_sender_was_ai = False
        
        # 确保滚动到底部
        self.scroll_area.scroll_to_bottom()
    
    # === 图片处理方法 ===
    
    def _process_images_in_content(self, message_id: str, content: str):
        """处理内容中的图片"""
        # 查找所有img标签
        img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
        matches = re.findall(img_pattern, content, re.IGNORECASE)
        
        logger.debug(f"Found {len(matches)} images in content for {message_id}")
        
        for img_url in matches:
            process_key = f"{message_id}_{img_url}"
            
            if process_key in self.processed_images:
                continue
            
            self.processed_images[process_key] = True
            logger.debug(f"Processing new image: {img_url}")
            
            # 异步加载图片
            self._load_and_display_image(message_id, img_url)
    
    def _load_and_display_image(self, message_id: str, img_url: str):
        """异步加载并显示图片 - 添加请求缓存 (优化建议 4)"""
        # 检查缓存
        try:
            cache_key = hashlib.md5(img_url.encode()).hexdigest()
            # 使用应用特定的缓存目录可能更好，但暂时用系统临时目录
            cache_dir = os.path.join(tempfile.gettempdir(), "ai_pet_img_cache")
            os.makedirs(cache_dir, exist_ok=True) # 确保目录存在
            # 使用原始 URL 的一部分作为文件名可能有助于调试，但哈希值是唯一标识
            # 文件扩展名可能未知，可以尝试从 URL 推断或默认 .img
            file_ext = os.path.splitext(img_url)[1] or ".img"
            cache_path = os.path.join(cache_dir, f"{cache_key}{file_ext}")

            if os.path.exists(cache_path):
                logger.info(f"Image cache hit for {img_url} at {cache_path}")
                # 检查文件是否有效（例如，大小 > 0）
                if os.path.getsize(cache_path) > 0:
                    self.update_image_signal.emit(message_id, cache_path)
                    return # 缓存命中，直接返回
                else:
                    logger.warning(f"Cached image file is empty, removing: {cache_path}")
                    os.remove(cache_path) # 无效缓存文件，删除

        except Exception as e:
            logger.error(f"Error accessing image cache for {img_url}: {e}")
            # 缓存出错，继续尝试下载

        # --- 缓存未命中或无效，执行下载 ---
        def load_image_thread():
            temp_path = None # 初始化变量
            try:
                logger.debug(f"Loading image (cache miss): {img_url}")
                
                # 下载图片
                response = requests.get(img_url, timeout=10, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                response.raise_for_status()
                
                # 保存到临时文件
                # 使用 NamedTemporaryFile 来确保唯一性，但下载后需要复制到缓存路径
                with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_file:
                    tmp_file.write(response.content)
                    temp_path = tmp_file.name # 获取临时文件路径
                
                logger.debug(f"Image downloaded to temporary file: {temp_path}")

                # 下载后保存到缓存 (优化建议 4)
                try:
                    shutil.copy2(temp_path, cache_path) # 使用 copy2 保留元数据
                    logger.info(f"Image saved to cache: {cache_path}")
                except Exception as copy_err:
                    logger.error(f"Failed to save image to cache {cache_path}: {copy_err}")
                    # 即使缓存失败，仍然尝试使用临时文件显示
                
                # 在主线程中更新UI (使用缓存路径或临时路径)
                display_path = cache_path if os.path.exists(cache_path) else temp_path
                if display_path: # 确保路径有效
                    self.update_image_signal.emit(message_id, display_path)
                
            except Exception as e:
                logger.error(f"Failed to load image {img_url}: {e}")
            finally:
                # 清理临时文件（如果它不是最终显示的路径）
                if temp_path and os.path.exists(temp_path) and temp_path != display_path:
                    try:
                        os.remove(temp_path)
                        logger.debug(f"Temporary download file removed: {temp_path}")
                    except Exception as rm_err:
                        logger.warning(f"Failed to remove temporary download file {temp_path}: {rm_err}")

        # 在单独线程中加载
        threading.Thread(target=load_image_thread, daemon=True).start()
    
    def _update_image_in_bubble_safe(self, message_id: str, image_path: str):
        """线程安全的图片更新方法"""
        logger.debug(f"Updating image in bubble for {message_id}: {image_path}")
        
        msg_info = self.active_message_elements.get(message_id)
        if not msg_info:
            logger.warning(f"Message {message_id} not found for image update")
            return
        
        bubble_instance = msg_info.get('bubble_instance')
        if bubble_instance:
            try:
                # 设置图片
                bubble_instance.set_image(image_path, 200)
                
                # 滚动到底部
                self.scroll_area.scroll_to_bottom()
                
                logger.debug(f"Image successfully set for {message_id}")
                
            except Exception as e:
                logger.error(f"Failed to set image: {e}")
            finally:
                # 清理临时文件
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                        logger.debug(f"Temp file removed: {image_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temp file: {e}")
    
    def _remove_img_tags(self, content: str) -> str:
        """移除HTML img标签（包括不完整的标签）"""
        import re
        # 移除完整的img标签
        content = re.sub(r'<img[^>]*>', '', content)
        # 移除不完整的img标签开始部分
        content = re.sub(r'<img[^>]*$', '', content)
        return content
    
    # === 历史记录处理 ===
    
    def clear_chat_display(self):
        """清空聊天显示 - 添加内存优化"""
        # 移除所有消息气泡（除了最后的stretch）
        logger.debug("Clearing chat display...")
        widgets_to_delete = []
        while self.message_layout.count() > 1:
            item = self.message_layout.takeAt(0)
            if item and item.widget():
                widgets_to_delete.append(item.widget())
        
        for widget in widgets_to_delete:
             widget.deleteLater() # 标记删除
        logger.debug(f"Marked {len(widgets_to_delete)} widgets for deletion.")

        # 清空相关数据
        self.active_message_elements.clear()
        self.last_history_sender_was_ai = False
        self.processed_images.clear()
        
        # 隐藏打字指示器
        self.hide_typing_indicator()
        
        # 显式调用垃圾回收 (优化建议 6)
        gc.collect()
        logger.info("Chat display cleared and GC collected.")
    
    def populate_with_history(self, history_list: List[Dict[str, Any]]):
        """用历史记录填充聊天显示 - 分批加载 (优化建议 6)"""
        self.clear_chat_display()
        
        BATCH_SIZE = 20  # 每批加载的消息数量
        logger.info(f"Populating chat with {len(history_list)} history entries in batches of {BATCH_SIZE}...")

        total_entries = len(history_list)
        for i in range(0, total_entries, BATCH_SIZE):
            batch = history_list[i:min(i + BATCH_SIZE, total_entries)]
            logger.debug(f"Processing batch {i // BATCH_SIZE + 1}/{(total_entries + BATCH_SIZE - 1) // BATCH_SIZE} (entries {i+1}-{min(i + BATCH_SIZE, total_entries)})")

            for entry_index_in_batch, entry in enumerate(batch):
                # 计算全局索引，用于生成唯一消息ID
                global_index = i + entry_index_in_batch
                
                # --- 原有的单个条目处理逻辑 ---
                role = entry.get("role", "unknown").lower()
                content = entry.get("content", "")
                parts = entry.get("parts", [])
                
                # 确定发送者
                sender = "User" if role == "user" else "AI"
                if role == "model_error" or role == "ai_error": sender = "AI_Error"
                elif role == "system_tool": sender = "系统工具"
                elif role == "system" or role == "systemerror": sender = "系统"


                # 提取文本内容
                full_content_text_with_html = ""
                if isinstance(content, str) and content:
                    full_content_text_with_html = content
                elif isinstance(parts, list) and parts:
                    for part_idx, part_entry in enumerate(parts):
                        if isinstance(part_entry, dict):
                            if part_entry.get("type") == "text":
                                full_content_text_with_html += part_entry.get("text", "")
                            elif part_entry.get("type") == "image_url":
                                # 对于历史记录中的 image_url，我们假设它已经是 base64 或外部 URL
                                # 这里我们主要处理AI回复中可能内嵌的 <img> 标签
                                # 如果 parts 中直接有 image_url，MessageBubble 的 set_image 还不能直接处理 URL
                                # 但如果 AI 回复是 <img src="...">，则下面的逻辑会处理
                                img_url_from_part = part_entry.get("url", "")
                                if img_url_from_part:
                                    # 为了演示，我们暂时也把它包装成 <img> 标签，让后续逻辑统一处理
                                    # 注意：这可能不是最佳实践，取决于 image_url 的具体格式
                                    # full_content_text_with_html += f'<img src="{img_url_from_part}">'
                                    # 更好的方式是，如果 parts 里有 image_url，直接尝试下载并设置
                                    pass # 暂时跳过直接的 image_url part 的处理，聚焦于修复 <img> 标签

                            # 添加换行，除非是最后一个 part
                            if part_idx < len(parts) - 1:
                                 full_content_text_with_html += "\n"

                full_content_text_with_html = full_content_text_with_html.strip()
                
                # 生成一个临时的唯一消息 ID 用于历史消息的图片加载
                history_message_id = f"history_{global_index}_{uuid.uuid4().hex}"

                # 移除 <img> 标签以用于文本显示
                display_text = self._remove_img_tags(full_content_text_with_html)

                if display_text or (full_content_text_with_html and not display_text and "<img" in full_content_text_with_html): # 如果只有图片也创建气泡
                    # 创建消息气泡
                    # 注意：这里的 timestamp 应该是历史消息的真实时间，如果 entry 中有的话
                    timestamp_from_history = entry.get("timestamp") # 假设历史条目中可能有时间戳
                    bubble_timestamp = datetime.fromisoformat(timestamp_from_history) if timestamp_from_history else datetime.now()

                    # 创建消息气泡，直接使用带图片的完整内容
                    bubble = MessageBubble(sender, "", bubble_timestamp, self)
                    self.add_message_bubble(bubble)
                    
                    logger.info(f"🔥 历史记录加载 - 创建气泡: {sender}, 内容: {full_content_text_with_html[:100]}...")
                    
                    # 直接使用新的混合内容更新机制
                    bubble.update_content(full_content_text_with_html)
                # --- 单个条目处理结束 ---

            # 每批处理完后短暂暂停，避免界面卡顿 (优化建议 6)
            QApplication.processEvents() # 处理挂起的事件，让UI响应
            time.sleep(0.05) # 短暂休眠 50ms        # 滚动到底部
        QTimer.singleShot(200, self.scroll_area.scroll_to_bottom) # 稍微增加延时，确保气泡渲染和图片开始加载
        
        logger.info(f"Finished populating chat with {len(history_list)} history entries.")
    
    def display_error_message(self, error_text: str, is_critical: bool = False):
        """显示错误消息"""
        # 出现错误时清理打字指示器
        self.hide_typing_indicator()
        
        logger.error(f"Error message: {error_text} (critical: {is_critical})")
        
        sender = "系统错误" if is_critical else "系统"
        error_bubble = MessageBubble(sender, error_text, datetime.now(), self)
        
        # 如果是严重错误，高亮显示
        if is_critical:
            error_bubble.highlight_briefly(3000)
        
        self.add_message_bubble(error_bubble)
    
    # === 窗口控制方法 ===
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查是否点击在标题栏区域
            if hasattr(self, 'title_bar') and self.title_bar.geometry().contains(event.pos()):
                self.is_dragging = True
                self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
                event.accept()
                return
        
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.is_dragging and event.buttons() == Qt.LeftButton:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)
            event.accept()
        else:
            super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            event.accept()
        else:
            super().mouseReleaseEvent(event)
    
    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        if hasattr(self, 'show_animation'):
            self.show_animation.start()
    
    def closeEvent(self, event):
        """窗口关闭事件 - 添加资源清理 (优化建议 8)"""
        logger.info("Close event triggered. Cleaning up resources...")
        
        # 注意：这里不清理WebSocket连接，因为closeEvent只是隐藏窗口，不是退出程序
        # WebSocket连接应该保持，以便在后台继续接收陪玩消息
        
        # 清理所有子组件 (尤其是那些可能持有外部资源或定时器的)
        # 注意：这可能比较激进，确保不会意外删除不应删除的子对象
        # 更好的做法是让每个子组件自己管理自己的资源清理（例如通过 __del__ 或特定的 cleanup 方法）
        # 但作为通用清理，可以尝试 deleteLater 所有 QObject 子项
        children_to_delete = self.findChildren(QObject)
        logger.debug(f"Found {len(children_to_delete)} child QObjects to deleteLater.")
        for child in children_to_delete:
            # 避免删除自身或父对象（理论上 findChildren 不会返回自身）
            if child != self and child.parent() == self: # 只删除直接子对象？或者所有后代？
                 # 检查是否有 deleteLater 方法，以防万一
                 if hasattr(child, 'deleteLater'):
                     try:
                         child.deleteLater()
                         # logger.debug(f"Called deleteLater on child: {type(child)}")
                     except Exception as e:
                         logger.warning(f"Error calling deleteLater on child {type(child)}: {e}")

        # 确保主容器也被正确处理（如果它不是 QWidget 的直接子类且需要特殊清理）
        # if hasattr(self, 'main_container') and self.main_container:
        #     self.main_container.deleteLater()

        # 调用父类的 closeEvent 之前执行隐藏动画
        event.ignore() # 忽略原始关闭事件，使用动画隐藏
        self.animated_hide()
        logger.info("Cleanup initiated, starting animated hide.")
        # super().closeEvent(event) # 不在这里调用 super，因为我们用动画隐藏替代了

    def hideEvent(self, event):
        """当窗口被隐藏时调用 (无论是通过 self.hide() 还是其他方式)"""
        super().hideEvent(event)
        self.window_hidden_signal.emit() # <--- 发出信号
        logger.info("ChatWindow hidden, emitted window_hidden_signal.")
    
    def animated_hide(self):
        """动画隐藏窗口"""
        if hasattr(self, 'hide_animation'):
            # 确保 hide_animation.finished 连接到 self.hide 或一个能发出信号的方法
            # 如果它直接连接到 self.hide，那么 hideEvent 会被调用
            self.hide_animation.start()
        else:
            self.hide() # 直接隐藏也会触发 hideEvent
    
    def show_window(self):
        """显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 确保主容器大小匹配窗口
        if hasattr(self, 'main_container'):
            self.main_container.resize(self.size())

    def eventFilter(self, obj, event):
        """事件过滤器 - 添加性能监控 (优化建议 7)"""
        # 注意：这个 eventFilter 是 ChatWindow 级别的，可以捕获所有子控件的事件
        # 如果只想监控特定子控件，需要在该子控件上 installEventFilter
        start_time = time.time()
        
        # 调用父类的 eventFilter 来处理事件
        # 如果 ChatWindow 本身没有重写 eventFilter，这将调用 QWidget 的默认实现
        result = super().eventFilter(obj, event)
        
        processing_time = time.time() - start_time
        
        # 记录处理时间过长的操作
        if processing_time > 0.05:  # 超过 50ms
            try:
                # 尝试获取更详细的事件信息
                event_type = event.type()
                obj_info = f"Object: {type(obj).__name__}" if obj else "Object: None"
                logger.warning(f"Event processing took {processing_time:.4f}s: Type={event_type}, {obj_info}") # 使用 .4f 提高精度
            except Exception as log_e:
                # 防止日志记录本身出错
                logger.warning(f"Event processing took {processing_time:.4f}s (logging event details failed: {log_e})")

        return result
    
    # === 兼容性方法 ===
    
    def enable_capture_button(self):
        """启用截图按钮（兼容性方法）"""
        if self.input_area and hasattr(self.input_area, 'tool_buttons'):
            screenshot_btn = self.input_area.tool_buttons.get('screenshot')
            if screenshot_btn:
                screenshot_btn.setEnabled(True)
    
    def disable_capture_button(self):
        """禁用截图按钮（兼容性方法）"""
        if self.input_area and hasattr(self.input_area, 'tool_buttons'):
            screenshot_btn = self.input_area.tool_buttons.get('screenshot')
            if screenshot_btn:
                screenshot_btn.setEnabled(False)

    def set_selected_screenshot(self, image_path: str):
        """由 AppController 调用，设置截图结果到输入区域"""
        if self.input_area:
            self.input_area.selected_image_path = image_path
            self.input_area.selected_attachment_path = None # 清除可能的其他附件
            self.input_area.update_preview() # 调用 ModernInputArea 的方法来更新UI
            logger.info(f"ChatWindow: Screenshot set to input area: {image_path}")
        else:
            logger.warning("ChatWindow: input_area not found, cannot set screenshot.")

    def set_selected_screenshot(self, image_path: str):
        """由 AppController 调用，设置截图结果到输入区域"""
        if self.input_area:
            self.input_area.selected_image_path = image_path
            self.input_area.selected_attachment_path = None # 清除可能的其他附件
            self.input_area.update_preview() # 调用 ModernInputArea 的方法来更新UI
            logger.info(f"ChatWindow: Screenshot set to input area: {image_path}")
        else:
            logger.warning("ChatWindow: input_area not found, cannot set screenshot.")

    # === SSE相关方法 ===
    
    def _handle_sse_data(self, sse_data: str):
        """处理从SSE客户端接收到的数据"""
        try:
            data = json.loads(sse_data)
            message_id = data.get("id", f"sse_stream_{uuid.uuid4().hex[:8]}")
            
            # 处理连接类型消息
            if data.get("type") == "connection":
                self._add_system_message(f"📡 {data.get('message', 'SSE连接事件')}")
                return
                
            # 处理心跳消息
            elif data.get("type") == "ping":
                logger.debug("SSE心跳消息")
                return
                
            # 处理AI流式消息（OpenAI格式）- 简化版本，参考companion_client.html
            elif "choices" in data and data["choices"] and "delta" in data["choices"][0]:
                delta = data["choices"][0].get("delta", {})
                ai_content = delta.get("content", "")
                
                if ai_content and ai_content.strip():
                    # 直接创建新的AI消息，不进行流式拼接
                    ai_bubble = MessageBubble("AI小爱", ai_content, datetime.now())
                    self.add_message_bubble(ai_bubble)
                    logger.info(f"接收到AI消息块: {ai_content[:50]}...")
                    
            # 处理流结束事件
            elif data.get("type") == "stream_end":
                stream_id = data.get("stream_id", message_id)
                if stream_id in self.active_message_elements:
                    full_text = self.active_message_elements.get(stream_id, {}).get('full_content', "")
                    self.handle_ai_stream_finished(
                        stream_id,
                        full_text_for_tts=full_text,
                        metadata={"is_final_stream_end": True}
                    )
                    
            # 处理其他类型的消息
            else:
                message_text = data.get("message", json.dumps(data, ensure_ascii=False, indent=2))
                self._add_system_message(f"📨 SSE: {message_text}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode SSE JSON data: {sse_data[:200]}...")
            # 检查是否是数据被截断的问题
            if "Unterminated string" in str(e) or len(sse_data) > 1000:
                logger.warning("可能收到不完整的SSE数据，忽略此消息")
                return
            
            # 尝试多种编码修复方法
            for encoding_method in ['latin1', 'iso-8859-1', 'cp1252']:
                try:
                    if isinstance(sse_data, str):
                        # 尝试不同的编码修复方法
                        fixed_data = sse_data.encode(encoding_method).decode('utf-8', errors='replace')
                        # 清理可能的JSON格式问题
                        fixed_data = fixed_data.replace('\ufffd', '?')  # 替换无法解码的字符
                        data = json.loads(fixed_data)
                        
                        # 如果成功解析，记录并处理
                        content_preview = data.get('choices', [{}])[0].get('delta', {}).get('content', '')[:30]
                        logger.info(f"使用 {encoding_method} 编码修复成功: {content_preview}...")
                        self._handle_sse_data(fixed_data)
                        return
                except Exception:
                    continue
            
            # 所有修复方法都失败了
            logger.error("所有编码修复方法都失败")
            self._add_system_message(f"⚠️ 收到无法解析的SSE消息: {sse_data[:50]}...", "error")
        except Exception as e:
            logger.error(f"Error processing SSE data: {e}")
            self._add_system_message(f"❌ 处理SSE消息时出错: {str(e)}", "error")
    
    def _handle_sse_connection_status(self, connected: bool, message: str):
        """处理SSE连接状态变化"""
        self.sse_connected = connected
        
        if connected:
            self._add_system_message(f"🎉 成功连接到SSE服务器！({message})", "success")
        else:
            self._add_system_message(f"🔌 与SSE服务器断开连接。({message})", "error")
    
    def _handle_sse_error(self, error_message: str):
        """处理SSE错误"""
        logger.error(f"SSE Error: {error_message}")
        self._add_system_message(f"❌ SSE错误: {error_message}", "error")
    
    def toggle_sse_connection(self):
        """切换SSE连接状态"""
        if self.sse_connected:
            # self.sse_client.disconnect_from_server()
            pass
        else:
            if self.sse_url:
                # self.sse_client.set_connection_params(self.sse_url)
                # self.sse_client.connect_to_server()
                pass
            else:
                self._add_system_message("❌ 请先设置SSE服务器地址", "error")
    
    def set_sse_url(self, url: str):
        """设置SSE服务器地址"""
        self.sse_url = url.strip()
        logger.info(f"SSE URL set to: {self.sse_url}")
    
    def _auto_connect_sse(self):
        """自动连接SSE服务器"""
        if self.sse_url:
            logger.info(f"Auto-connecting to SSE: {self.sse_url}")
            # self.sse_client.set_connection_params(self.sse_url)
            # self.sse_client.connect_to_server()


# === 测试代码 ===

if __name__ == "__main__":
    import sys
    
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ChatWindow()
    
    # 添加测试消息
    def add_test_messages():
        # 用户消息
        user_bubble = MessageBubble("User", "你好！请介绍一下自己", datetime.now())
        window.add_message_bubble(user_bubble)
        
        # AI回复
        ai_bubble = MessageBubble("AI", 
            "你好！我是AI智能助手，很高兴为您服务！😊\n\n"
            "我可以帮助您：\n"
            "• 回答各种问题\n"
            "• 编写和调试代码\n" 
            "• 翻译文本\n"
            "• 创意写作\n"
            "• 分析图片内容\n\n"
            "有什么我可以帮助您的吗？", 
            datetime.now()
        )
        window.add_message_bubble(ai_bubble)
        
        # 系统消息
        system_bubble = MessageBubble("系统", "连接已建立，开始对话", datetime.now())
        window.add_message_bubble(system_bubble)
    
    # 延迟添加测试消息，让窗口动画完成
    QTimer.singleShot(500, add_test_messages)
    
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())