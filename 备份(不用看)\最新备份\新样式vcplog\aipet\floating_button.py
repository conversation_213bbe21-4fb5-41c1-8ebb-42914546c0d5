import sys
from typing import Optional

# 尝试导入PyQt5，如果失败则尝试PyQt6
try:
    from PyQt5.QtWidgets import QWidget, QPushButton, QApplication
    from PyQt5.QtCore import Qt, pyqtSignal, QPoint, QSize, QObject, QEvent
    from PyQt5.QtGui import QMouseEvent
    IS_PYQT6 = False
    # 检查 PyQt5 版本，以确定 QMouseEvent.globalPos() 或 globalPosition()
    from PyQt5.QtCore import PYQT_VERSION_STR
    if PYQT_VERSION_STR and PYQT_VERSION_STR.startswith("6."): # 这实际上不会发生，因为这是PyQt5块
        IS_PYQT6 = True # 理论上这里不应该为True
except ImportError:
    try:
        from PyQt6.QtWidgets import QWidget, QPushButton, QApplication
        from PyQt6.QtCore import Qt, pyqtSignal, QPoint, QSize
        from PyQt6.QtGui import QMouseEvent
        IS_PYQT6 = True
    except ImportError:
        print("错误：未找到 PyQt5 或 PyQt6。请确保已安装其中一个。")
        sys.exit(1)

class FloatingButton(QWidget):
    """
    一个小的、无边框、总在最前的可点击/可拖动窗口，用作聊天窗口切换按钮。
    """
    clicked = pyqtSignal()  # 自定义点击信号

    def __init__(self, parent: Optional[QWidget] = None, icon_text: str = "💬"):
        super().__init__(parent)

        self.is_dragging: bool = False
        self.drag_start_position: QPoint = QPoint() # 初始化
        self.drag_has_moved_window: bool = False # 新增标志位，判断是否确实发生了窗口移动

        self.icon_text = icon_text
        self._setup_window_properties()
        self._setup_ui()

    def _setup_window_properties(self):
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | 
                            Qt.WindowType.WindowStaysOnTopHint | 
                            Qt.WindowType.Tool)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAutoFillBackground(False)
        self.setFixedSize(40, 40) # 稍微大一点以便点击
        self.setObjectName("FloatingChatButton")

    def _setup_ui(self):
        self.button = QPushButton(self.icon_text, self)
        self.button.setFixedSize(self.width(), self.height())
        self.button.setObjectName("InternalButton")
        self.button.setStyleSheet("""
            QPushButton#InternalButton {
                background-color: rgba(80, 80, 100, 0.75); /* 半透明蓝紫色 */
                border: none;
                border-radius: 20px; /* 圆形按钮 (size/2) */
                color: white;
                font-size: 18pt; /* 调整 Emoji 或文本大小 */
                text-align: center;
                padding-bottom: 2px; /* 微调 Emoji 垂直居中 */
            }
            QPushButton#InternalButton:hover {
                background-color: rgba(100, 100, 120, 0.85);
            }
            QPushButton#InternalButton:pressed {
                background-color: rgba(70, 70, 90, 0.9);
            }
        """)
        # 将内部按钮的点击事件连接到 FloatingButton 自身的 clicked 信号发射
        self.button.clicked.connect(self.clicked.emit)
        self.button.installEventFilter(self) # 安装事件过滤器

        # 初始定位到屏幕右侧中间偏上
        self.move_to_screen_edge()

    def move_to_screen_edge(self, edge: str = "right_mid_upper", margin: int = 20):
        """将按钮定位到屏幕指定边缘的预设位置。"""
        try:
            screen = QApplication.primaryScreen()
            if not screen:
                print("错误: FloatingButton - 无法获取主屏幕信息。")
                return
            
            screen_geometry = screen.availableGeometry() # 获取可用区域，避开任务栏
            
            target_x = 0
            target_y = 0

            if edge == "right_mid_upper":
                target_x = screen_geometry.right() - self.width() - margin
                top_y = screen_geometry.top()
                center_y = screen_geometry.center().y()
                midpoint_y = top_y + (center_y - top_y) // 3 # 更偏上一些 (1/3处)
                target_y = midpoint_y - (self.height() // 2)
                target_y = max(screen_geometry.top() + margin, target_y)
            elif edge == "right_bottom":
                target_x = screen_geometry.right() - self.width() - margin
                target_y = screen_geometry.bottom() - self.height() - margin
            # 可以添加更多预设位置如 "left_mid_upper", "left_bottom" 等
            else: # 默认到右侧中间偏上
                target_x = screen_geometry.right() - self.width() - margin
                top_y = screen_geometry.top()
                center_y = screen_geometry.center().y()
                midpoint_y = top_y + (center_y - top_y) // 3
                target_y = midpoint_y - (self.height() // 2)
                target_y = max(screen_geometry.top() + margin, target_y)

            self.move(target_x, target_y)
            print(f"FloatingButton 定位到 {edge}: ({self.pos().x()}, {self.pos().y()})")

        except Exception as e:
            print(f"FloatingButton 定位时出错: {e}")

    def _snap_to_screen_edge(self):
        """将按钮吸附到最近的屏幕边缘（如果在阈值内）。"""
        SNAP_THRESHOLD = self.width()  # 吸附阈值，例如按钮自身的宽度
        EDGE_MARGIN = 20               # 吸附后距离边缘的间距

        screen = QApplication.primaryScreen()
        if not screen:
            print("错误: FloatingButton - _snap_to_screen_edge 无法获取主屏幕信息。")
            return
        
        screen_geom = screen.availableGeometry()
        current_pos = self.pos()
        btn_width = self.width()
        btn_height = self.height()

        target_x = current_pos.x()
        target_y = current_pos.y()

        # 计算到各边缘的距离 (按钮外边缘到屏幕可用区内边缘)
        dist_to_left_edge = current_pos.x() - screen_geom.left()
        dist_to_right_edge = screen_geom.right() - (current_pos.x() + btn_width)
        dist_to_top_edge = current_pos.y() - screen_geom.top()
        dist_to_bottom_edge = screen_geom.bottom() - (current_pos.y() + btn_height)

        # 水平吸附判断
        snap_horizontally = False
        if 0 <= dist_to_left_edge < SNAP_THRESHOLD:
            # 如果左边缘在阈值内，且（右边缘不在阈值内 或 左边缘更近）
            if not (0 <= dist_to_right_edge < SNAP_THRESHOLD) or dist_to_left_edge <= dist_to_right_edge:
                target_x = screen_geom.left() + EDGE_MARGIN
                snap_horizontally = True
        elif 0 <= dist_to_right_edge < SNAP_THRESHOLD: # 否则，如果只有右边缘在阈值内
            target_x = screen_geom.right() - btn_width - EDGE_MARGIN
            snap_horizontally = True
        
        # 垂直吸附判断
        snap_vertically = False
        if 0 <= dist_to_top_edge < SNAP_THRESHOLD:
            # 如果上边缘在阈值内，且（下边缘不在阈值内 或 上边缘更近）
            if not (0 <= dist_to_bottom_edge < SNAP_THRESHOLD) or dist_to_top_edge <= dist_to_bottom_edge:
                target_y = screen_geom.top() + EDGE_MARGIN
                snap_vertically = True
        elif 0 <= dist_to_bottom_edge < SNAP_THRESHOLD: # 否则，如果只有下边缘在阈值内
            target_y = screen_geom.bottom() - btn_height - EDGE_MARGIN
            snap_vertically = True

        if snap_horizontally or snap_vertically:
            # 如果只在一个方向上吸附，另一个方向保持不变（除非它也被独立判断为需要吸附）
            # 当前逻辑是独立判断X和Y，所以如果X吸附了，Y仍可能是原始Y或吸附后的Y
            if target_x != current_pos.x() or target_y != current_pos.y():
                self.move(target_x, target_y)

    # mousePressEvent is removed as its logic is now in eventFilter

    def eventFilter(self, watched: QObject, event: QEvent) -> bool:
        if watched == self.button:
            if event.type() == QEvent.Type.MouseButtonPress:
                # Ensure we are dealing with a QMouseEvent
                # In PyQt5, the event passed to eventFilter for mouse events is already a QMouseEvent
                mouse_event = event
                if mouse_event.button() == Qt.MouseButton.LeftButton:
                    self.is_dragging = True
                    self.drag_has_moved_window = False # Reset flag on new press
                    if IS_PYQT6:
                        self.drag_start_position = mouse_event.globalPosition().toPoint() - self.frameGeometry().topLeft()
                    else:
                        self.drag_start_position = mouse_event.globalPos() - self.frameGeometry().topLeft()
                    # Event is not consumed here, allow button to process it for potential click
            
            elif event.type() == QEvent.Type.MouseButtonRelease:
                mouse_event = event
                if mouse_event.button() == Qt.MouseButton.LeftButton:
                    if self.drag_has_moved_window:
                        # If window was dragged, consume this release event to prevent button's click signal
                        self.is_dragging = False # Ensure state is reset
                        self.drag_start_position = QPoint()
                        self.drag_has_moved_window = False # Reset flag
                        # Snapping is handled by FloatingButton's mouseReleaseEvent
                        return True # Consume event
                    # If not dragged, let button process release for click
                    self.is_dragging = False # Ensure state is reset even for a click
                    self.drag_start_position = QPoint()
                    self.drag_has_moved_window = False


        return super().eventFilter(watched, event)

    def mouseMoveEvent(self, event: QMouseEvent):
        if self.is_dragging and event.buttons() == Qt.MouseButton.LeftButton:
            if not self.drag_start_position.isNull():
                current_global_pos = event.globalPos() if not IS_PYQT6 else event.globalPosition().toPoint()
                new_pos = current_global_pos - self.drag_start_position
                
                if self.pos() != new_pos: # Check if the window actually moved
                    self.drag_has_moved_window = True
                
                self.move(new_pos)
                event.accept()

    def mouseReleaseEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            # This event is for the FloatingButton QWidget itself.
            # The eventFilter handles release events on self.button to suppress clicks after drag.
            
            was_dragging_when_released_on_widget = self.is_dragging # Check current drag state

            if was_dragging_when_released_on_widget: # If drag was in progress (e.g., mouse released outside button but still on widget)
                self._snap_to_screen_edge()

            # Reset all drag-related states here, as this is the definitive end of any mouse interaction
            self.is_dragging = False
            self.drag_start_position = QPoint()
            self.drag_has_moved_window = False # Reset this flag too
            
            event.accept() # Accept the release event on the widget

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 创建一个背景板，方便看清浮动按钮
    bg_widget = QWidget()
    bg_widget.setFixedSize(800, 600)
    bg_palette = bg_widget.palette()
    bg_palette.setColor(QPalette.ColorRole.Window, QColor(50,50,80)) # 深色背景
    bg_widget.setPalette(bg_palette)
    bg_widget.setAutoFillBackground(True)
    bg_widget.show()
    
    fb = FloatingButton(parent=None) # parent=None 使其成为独立窗口
    fb.show()

    def on_button_clicked():
        print("悬浮按钮被点击了！")
        # 测试切换按钮文本或做其他事
        current_text = fb.button.text()
        fb.button.setText("Clicked!" if current_text == "💬" else "💬")


    fb.clicked.connect(on_button_clicked)
    
    sys.exit(app.exec())