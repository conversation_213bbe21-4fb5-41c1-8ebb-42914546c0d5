import sys
from PyQt5.QtWidgets import QWidget, QApplication
from PyQt5.QtCore import Qt, pyqtSignal, QPoint, QRect, QTimer # 新增 QTimer
from PyQt5.QtGui import QPixmap, QScreen, QPainter, QRegion, QColor, QPen, QMouseEvent, QKeyEvent # 新增 QKeyEvent

class ScreenAreaSelector(QWidget):
    """
    一个全屏窗口，允许用户通过拖动鼠标选择屏幕上的一个矩形区域进行截图。
    选择完成后，会发出带有截取区域QPixmap的信号。
    """
    area_selected = pyqtSignal(QPixmap) # 信号，传递选定区域的QPixmap
    capture_cancelled = pyqtSignal()    # 信号，当截图被取消时发出

    def __init__(self, parent: QWidget = None):
        super().__init__(parent)

        # 设置窗口属性：无边框、总在最前 (移除了 Qt.WindowType.Tool)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint |
                            Qt.WindowType.WindowStaysOnTopHint)
        
        # 设置窗口状态为全屏
        self.setWindowState(Qt.WindowState.WindowFullScreen)
        
        # 恢复正常的半透明背景设置
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setStyleSheet("background-color: rgba(0, 0, 0, 70);") # 70/255 ~ 27% opacity

        self.start_pos: QPoint = None
        self.end_pos: QPoint = None
        self.current_rect: QRect = QRect()
        self.is_selecting: bool = False # 显式初始化
        self.accepting_mouse_input: bool = False # 新增标志位
        
        self.original_desktop_pixmap: QPixmap = None
        # self._capture_desktop() # 将在 showEvent 中调用

        self.setMouseTracking(True) # 需要持续追踪鼠标以更新光标等（如果需要）

    def _enable_mouse_input(self):
        print("ScreenAreaSelector: Mouse input enabled.")
        self.accepting_mouse_input = True

    def showEvent(self, event):
        """在窗口即将显示时调用。"""
        super().showEvent(event)
        print("ScreenAreaSelector: showEvent triggered.")
        # 重置选择状态，以防残留事件影响
        self.is_selecting = False
        self.start_pos = None
        self.current_rect = QRect()
        self.accepting_mouse_input = False # 确保每次显示时都重置
        print("ScreenAreaSelector: showEvent - Selection states and accepting_mouse_input reset.")

        if not self.original_desktop_pixmap: # 确保只捕获一次
            print("ScreenAreaSelector: showEvent - Capturing desktop...")
            self._capture_desktop()
            if self.original_desktop_pixmap.isNull():
                print("ScreenAreaSelector: showEvent - Desktop capture FAILED in showEvent.")
            else:
                print(f"ScreenAreaSelector: showEvent - Desktop captured: {self.original_desktop_pixmap.size()}")
        
        # 延迟启用鼠标输入
        QTimer.singleShot(50, self._enable_mouse_input) # 50ms 延迟
        self.update() # 确保在捕获后重绘

    def _capture_desktop(self):
        """捕获整个桌面（所有屏幕）的图像。"""
        desktop_widget = QApplication.desktop()
        if desktop_widget:
            # desktop_widget.winId() is not always reliable or available on all platforms for grabWindow.
            # A more robust way for full desktop (all screens) is often to iterate screens or use primaryScreen for single.
            # For simplicity and common use, primaryScreen is often sufficient if multi-monitor selection isn't complex.
            # However, to allow selection across monitors, grabbing the virtual desktop is better.
            # Let's try grabbing the primary screen first, can be expanded later if needed.
            primary_screen = QApplication.primaryScreen()
            if primary_screen:
                # winId=0 means the entire screen
                self.original_desktop_pixmap = primary_screen.grabWindow(0) 
            else:
                print("ScreenAreaSelector: Error - Cannot get primary screen.")
                self.original_desktop_pixmap = QPixmap(self.size()) # Fallback to empty pixmap
                self.original_desktop_pixmap.fill(Qt.transparent)
        else:
            print("ScreenAreaSelector: Error - Cannot get desktop widget.")
            self.original_desktop_pixmap = QPixmap(self.size())
            self.original_desktop_pixmap.fill(Qt.transparent)

        if self.original_desktop_pixmap.isNull():
            print("ScreenAreaSelector: Error - Failed to grab desktop pixmap.")
            # Fallback if grabWindow failed
            self.original_desktop_pixmap = QPixmap(self.size())
            self.original_desktop_pixmap.fill(Qt.transparent)


    def mousePressEvent(self, event: QMouseEvent):
        print(f"ScreenAreaSelector: mousePressEvent triggered. Button: {event.button()}, accepting_input: {self.accepting_mouse_input}")
        if event.button() == Qt.MouseButton.LeftButton and self.accepting_mouse_input:
            self.start_pos = event.pos()
            self.current_rect = QRect(self.start_pos, QPoint(self.start_pos)) # Initialize rect
            self.is_selecting = True
            print(f"ScreenAreaSelector: mousePressEvent - Selection started at {self.start_pos}")
            self.update() # Trigger repaint
        elif event.button() == Qt.MouseButton.LeftButton and not self.accepting_mouse_input:
            print("ScreenAreaSelector: mousePressEvent - Left click ignored (input not yet enabled).")


    def mouseMoveEvent(self, event: QMouseEvent):
        if self.is_selecting:
            self.end_pos = event.pos()
            self.current_rect = QRect(self.start_pos, self.end_pos).normalized()
            self.update() # Trigger repaint to show selection rectangle

    def mouseReleaseEvent(self, event: QMouseEvent):
        print(f"ScreenAreaSelector: mouseReleaseEvent triggered. Button: {event.button()}, is_selecting: {self.is_selecting}, current_rect: {self.current_rect}")
        if event.button() == Qt.MouseButton.LeftButton and self.is_selecting:
            self.is_selecting = False # 重置选择状态
            final_rect = self.current_rect.normalized()
            print(f"ScreenAreaSelector: Left button released. final_rect: {final_rect}")

            if final_rect.width() > 5 and final_rect.height() > 5: # Ensure a minimal selection
                # Crop the selected area from the original desktop pixmap
                # Ensure the rect is within the bounds of the pixmap
                valid_selection_rect = final_rect.intersected(self.original_desktop_pixmap.rect())
                if not valid_selection_rect.isEmpty():
                    selected_pixmap = self.original_desktop_pixmap.copy(valid_selection_rect)
                    self.area_selected.emit(selected_pixmap)
                    print("ScreenAreaSelector: area_selected emitted.")
                else:
                    print("ScreenAreaSelector: Valid selection rect is empty. Emitting capture_cancelled.")
                    self.capture_cancelled.emit() # Selection was outside pixmap bounds
            else:
                print(f"ScreenAreaSelector: Selection too small (width: {final_rect.width()}, height: {final_rect.height()}). Emitting capture_cancelled.")
                self.capture_cancelled.emit() # Selection too small
            
            self.close() # Close the selector window
            self.deleteLater()
        elif event.button() == Qt.MouseButton.LeftButton and not self.is_selecting:
            # 如果是左键释放，但并未处于选择状态（例如，窗口一显示就收到释放事件）
            print("ScreenAreaSelector: Left button released, but not in selecting mode. Closing.")
            self.capture_cancelled.emit() # 视为取消
            self.close()
            self.deleteLater()


    def paintEvent(self, event):
        # super().paintEvent(event) # WA_TranslucentBackground + stylesheet should handle the main background
        
        painter = QPainter(self)
        
        if not self.original_desktop_pixmap.isNull():
            # 强制绘制半透明黑色遮罩，覆盖整个窗口
            # 这将确保即使样式表不工作，遮罩也会被绘制
            painter.fillRect(self.rect(), QColor(0, 0, 0, 70))

            if self.is_selecting and not self.current_rect.isEmpty():
                # 在选区绘制原始屏幕内容，使其清晰可见 (这会覆盖上面绘制的遮罩部分)
                painter.drawPixmap(self.current_rect, self.original_desktop_pixmap, self.current_rect)

                # 绘制选区边框
                pen = QPen(QColor(50, 150, 255, 220), 1, Qt.PenStyle.SolidLine)
                painter.setPen(pen)
                painter.drawRect(self.current_rect.adjusted(0,0,-1,-1))
        else:
            # 如果 original_desktop_pixmap 为空，绘制一个明显的错误提示背景
            painter.fillRect(self.rect(), QColor(255, 0, 0, 150)) # 半透明红色
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "Error: Desktop capture failed!")
        
        painter.end() # 确保 QPainter 正确结束

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key.Key_Escape:
            self.capture_cancelled.emit()
            self.close()
            self.deleteLater()
        else:
            super().keyPressEvent(event)

if __name__ == '__main__':
    # Example Usage
    app = QApplication(sys.argv)

    # Dummy main window to show behind selector for testing
    # main_win = QWidget()
    # main_win.setFixedSize(800, 600)
    # main_win.setStyleSheet("background-color: lightblue;")
    # main_win.show()
    # QTimer.singleShot(100, main_win.hide) # Hide after a moment so selector can grab screen

    selector = ScreenAreaSelector()
    
    def on_area_selected(pixmap: QPixmap):
        print("Area selected!")
        # Save or display the pixmap
        save_path = "selected_area.png"
        if pixmap.save(save_path):
            print(f"Screenshot saved to {save_path}")
            
            # Display it in a QLabel for testing
            label = QLabel()
            label.setPixmap(pixmap.scaled(400,300, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
            label.setWindowTitle("Captured Area")
            label.show()
            # Keep app running until label is closed
            label.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose) 
            # app.aboutToQuit.connect(label.close) # This might not be needed if WA_DeleteOnClose works
        else:
            print(f"Failed to save screenshot to {save_path}")
        # selector.deleteLater() # Already called in mouseReleaseEvent
        # QApplication.quit() # Quit after selection for testing

    def on_capture_cancelled():
        print("Capture cancelled.")
        # selector.deleteLater() # Already called in keyPressEvent
        QApplication.quit()

    selector.area_selected.connect(on_area_selected)
    selector.capture_cancelled.connect(on_capture_cancelled)
    
    # Need a slight delay for any main window to draw itself before grabbing screen
    # This is more relevant if there's a visible main window.
    # For a tool window like this, it might grab immediately.
    # If issues with grabbing correct screen, a QTimer.singleShot might be needed here.
    selector.show()
    
    sys.exit(app.exec_())