// ecosystem.config.js - AI桌宠 PM2 配置文件
module.exports = {
  apps: [{
    name: "ai_桌宠",
    script: "../.venv/Scripts/python.exe",  // 使用虚拟环境中的Python
    args: "main.py",
    cwd: __dirname,
    
    // Python 应用程序特定配置
    interpreter: "none", // 禁用 Node.js 解释器，因为我们直接运行 python 命令
    
    // 监控和重启配置
    watch: true,
    ignore_watch: [
      "ai_桌宠_image_cache/",
      "*.pyc",
      "__pycache__/",
      "*.log",
      "configs/*.json"  // 避免配置文件变化导态重启
    ],
    
    // 环境变量
    env: {
      "PYTHONPATH": ".",
      "PYTHONUNBUFFERED": "1",  // 确保 Python 输出实时显示
      "QT_QPA_PLATFORM": "windows", // 明确指定 Qt 平台
      "VIRTUAL_ENV": "../.venv",  // 明确指定虚拟环境路径
    },
    
    // 重启策略 - GUI应用需要特殊处理
    autorestart: true,
    max_restarts: 5,
    min_uptime: "10s",
    restart_delay: 3000,
    
    // 日志配置
    out_file: "./logs/ai_桌宠_out.log",
    error_file: "./logs/ai_桌宠_error.log",
    merge_logs: true,
    log_date_format: "YYYY-MM-DD HH:mm:ss",
    
    // 实例管理
    instances: 1,  // GUI应用通常只运行一个实例
    exec_mode: "fork",
    
    // 高级配置
    kill_timeout: 5000,
    wait_ready: false,
    listen_timeout: 3000,
  }]
};