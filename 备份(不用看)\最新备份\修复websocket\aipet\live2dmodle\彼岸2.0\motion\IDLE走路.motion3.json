{"Version": 3, "Meta": {"Duration": 33.933, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 1304, "TotalPointCount": 3879, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param346", "Segments": [0, 0, 0, 0.033, 0, 1, 0.1, 0, 0.167, 1, 0.233, 1, 0, 33.933, 1]}, {"Target": "Parameter", "Id": "Param341", "Segments": [0, 1, 1, 0.256, 1, 0.511, 0.802, 0.767, 0, 1, 0.889, -0.383, 1.011, -1, 1.133, -1, 1, 1.256, -1, 1.378, 1, 1.5, 1, 1, 1.756, 1, 2.011, 0.802, 2.267, 0, 1, 2.389, -0.383, 2.511, -1, 2.633, -1, 1, 2.756, -1, 2.878, 1, 3, 1, 1, 3.289, 1, 3.578, 0.804, 3.867, 0, 1, 3.989, -0.34, 4.111, -1, 4.233, -1, 1, 4.356, -1, 4.478, 1, 4.6, 1, 1, 4.856, 1, 5.111, 0.802, 5.367, 0, 1, 5.489, -0.383, 5.611, -1, 5.733, -1, 1, 5.856, -1, 5.978, 1, 6.1, 1, 1, 6.389, 1, 6.678, 0.804, 6.967, 0, 1, 7.089, -0.34, 7.211, -1, 7.333, -1, 1, 7.456, -1, 7.578, 1, 7.7, 1, 1, 7.956, 1, 8.211, 0.802, 8.467, 0, 1, 8.589, -0.383, 8.711, -1, 8.833, -1, 1, 8.956, -1, 9.078, 1, 9.2, 1, 1, 9.456, 1, 9.711, 0.782, 9.967, 0, 1, 10.122, -0.476, 10.278, -1, 10.433, -1, 1, 10.556, -1, 10.678, 1, 10.8, 1, 1, 11.056, 1, 11.311, 0.802, 11.567, 0, 1, 11.689, -0.383, 11.811, -1, 11.933, -1, 1, 12.056, -1, 12.178, 1, 12.3, 1, 1, 12.556, 1, 12.811, 0.797, 13.067, 0, 1, 13.2, -0.416, 13.333, -1, 13.467, -1, 1, 13.611, -1, 13.756, 1, 13.9, 1, 1, 14.156, 1, 14.411, 0.802, 14.667, 0, 1, 14.789, -0.383, 14.911, -1, 15.033, -1, 1, 15.156, -1, 15.278, 1, 15.4, 1, 1, 15.656, 1, 15.911, 0.802, 16.167, 0, 1, 16.289, -0.383, 16.411, -1, 16.533, -1, 1, 16.667, -1, 16.8, 1, 16.933, 1, 1, 17.211, 1, 17.489, 0.804, 17.767, 0, 1, 17.889, -0.354, 18.011, -1, 18.133, -1, 1, 18.256, -1, 18.378, 1, 18.5, 1, 1, 18.756, 1, 19.011, 0.802, 19.267, 0, 1, 19.389, -0.383, 19.511, -1, 19.633, -1, 1, 19.756, -1, 19.878, 1, 20, 1, 1, 20.289, 1, 20.578, 0.804, 20.867, 0, 1, 20.989, -0.34, 21.111, -1, 21.233, -1, 1, 21.356, -1, 21.478, 1, 21.6, 1, 1, 21.856, 1, 22.111, 0.802, 22.367, 0, 1, 22.489, -0.383, 22.611, -1, 22.733, -1, 1, 22.856, -1, 22.978, 1, 23.1, 1, 1, 23.378, 1, 23.656, 0.802, 23.933, 0, 1, 24.067, -0.385, 24.2, -1, 24.333, -1, 1, 24.456, -1, 24.578, 1, 24.7, 1, 1, 24.956, 1, 25.211, 0.802, 25.467, 0, 1, 25.589, -0.383, 25.711, -1, 25.833, -1, 1, 25.956, -1, 26.078, 1, 26.2, 1, 1, 26.467, 1, 26.733, 0.8, 27, 0, 1, 27.133, -0.4, 27.267, -1, 27.4, -1, 1, 27.533, -1, 27.667, 1, 27.8, 1, 1, 28.056, 1, 28.311, 0.802, 28.567, 0, 1, 28.689, -0.383, 28.811, -1, 28.933, -1, 1, 29.056, -1, 29.178, 1, 29.3, 1, 1, 29.556, 1, 29.811, 0.797, 30.067, 0, 1, 30.2, -0.416, 30.333, -1, 30.467, -1, 1, 30.611, -1, 30.756, 1, 30.9, 1, 1, 31.156, 1, 31.411, 0.802, 31.667, 0, 1, 31.789, -0.383, 31.911, -1, 32.033, -1, 1, 32.156, -1, 32.278, 1, 32.4, 1, 1, 32.656, 1, 32.911, 0.802, 33.167, 0, 1, 33.289, -0.383, 33.411, -1, 33.533, -1, 1, 33.667, -1, 33.8, 1, 33.933, 1]}, {"Target": "Parameter", "Id": "Param342", "Segments": [0, 0, 1, 0.133, -0.433, 0.267, -1, 0.4, -1, 1, 0.522, -1, 0.644, 1, 0.767, 1, 1, 1.011, 1, 1.256, 0.794, 1.5, 0, 1, 1.633, -0.433, 1.767, -1, 1.9, -1, 1, 2.022, -1, 2.144, 1, 2.267, 1, 1, 2.511, 1, 2.756, 0.765, 3, 0, 1, 3.167, -0.522, 3.333, -1, 3.5, -1, 1, 3.622, -1, 3.744, 1, 3.867, 1, 1, 4.111, 1, 4.356, 0.794, 4.6, 0, 1, 4.733, -0.433, 4.867, -1, 5, -1, 1, 5.122, -1, 5.244, 1, 5.367, 1, 1, 5.611, 1, 5.856, 0.794, 6.1, 0, 1, 6.233, -0.433, 6.367, -1, 6.5, -1, 1, 6.656, -1, 6.811, 1, 6.967, 1, 1, 7.211, 1, 7.456, 0.794, 7.7, 0, 1, 7.833, -0.433, 7.967, -1, 8.1, -1, 1, 8.222, -1, 8.344, 1, 8.467, 1, 1, 8.711, 1, 8.956, 0.794, 9.2, 0, 1, 9.333, -0.433, 9.467, -1, 9.6, -1, 1, 9.722, -1, 9.844, 1, 9.967, 1, 1, 10.244, 1, 10.522, 0.802, 10.8, 0, 1, 10.933, -0.385, 11.067, -1, 11.2, -1, 1, 11.322, -1, 11.444, 1, 11.567, 1, 1, 11.811, 1, 12.056, 0.794, 12.3, 0, 1, 12.433, -0.433, 12.567, -1, 12.7, -1, 1, 12.822, -1, 12.944, 1, 13.067, 1, 1, 13.344, 1, 13.622, 0.802, 13.9, 0, 1, 14.033, -0.385, 14.167, -1, 14.3, -1, 1, 14.422, -1, 14.544, 1, 14.667, 1, 1, 14.911, 1, 15.156, 0.794, 15.4, 0, 1, 15.533, -0.433, 15.667, -1, 15.8, -1, 1, 15.922, -1, 16.044, 1, 16.167, 1, 1, 16.422, 1, 16.678, 0.782, 16.933, 0, 1, 17.089, -0.476, 17.244, -1, 17.4, -1, 1, 17.522, -1, 17.644, 1, 17.767, 1, 1, 18.011, 1, 18.256, 0.794, 18.5, 0, 1, 18.633, -0.433, 18.767, -1, 18.9, -1, 1, 19.022, -1, 19.144, 1, 19.267, 1, 1, 19.511, 1, 19.756, 0.776, 20, 0, 1, 20.156, -0.494, 20.311, -1, 20.467, -1, 1, 20.6, -1, 20.733, 1, 20.867, 1, 1, 21.111, 1, 21.356, 0.794, 21.6, 0, 1, 21.733, -0.433, 21.867, -1, 22, -1, 1, 22.122, -1, 22.244, 1, 22.367, 1, 1, 22.611, 1, 22.856, 0.794, 23.1, 0, 1, 23.233, -0.433, 23.367, -1, 23.5, -1, 1, 23.644, -1, 23.789, 1, 23.933, 1, 1, 24.189, 1, 24.444, 0.797, 24.7, 0, 1, 24.833, -0.416, 24.967, -1, 25.1, -1, 1, 25.222, -1, 25.344, 1, 25.467, 1, 1, 25.711, 1, 25.956, 0.794, 26.2, 0, 1, 26.333, -0.433, 26.467, -1, 26.6, -1, 1, 26.733, -1, 26.867, 1, 27, 1, 1, 27.267, 1, 27.533, 0.8, 27.8, 0, 1, 27.933, -0.4, 28.067, -1, 28.2, -1, 1, 28.322, -1, 28.444, 1, 28.567, 1, 1, 28.811, 1, 29.056, 0.794, 29.3, 0, 1, 29.433, -0.433, 29.567, -1, 29.7, -1, 1, 29.822, -1, 29.944, 1, 30.067, 1, 1, 30.344, 1, 30.622, 0.802, 30.9, 0, 1, 31.033, -0.385, 31.167, -1, 31.3, -1, 1, 31.422, -1, 31.544, 1, 31.667, 1, 1, 31.911, 1, 32.156, 0.794, 32.4, 0, 1, 32.533, -0.433, 32.667, -1, 32.8, -1, 1, 32.922, -1, 33.044, 1, 33.167, 1, 1, 33.422, 1, 33.678, 0, 33.933, 0]}, {"Target": "Parameter", "Id": "Param343", "Segments": [0, 0, 1, 0.122, -0.345, 0.244, -0.432, 0.367, -0.7, 1, 0.433, -0.846, 0.5, -1, 0.567, -1, 1, 0.633, -1, 0.7, -0.6, 0.767, -0.6, 1, 0.8, -0.6, 0.833, -1, 0.867, -1, 1, 0.922, -1, 0.978, -1, 1.033, -0.7, 1, 1.111, -0.204, 1.189, 1, 1.267, 1, 1, 1.344, 1, 1.422, 0.212, 1.5, 0, 1, 1.622, -0.334, 1.744, -0.432, 1.867, -0.7, 1, 1.933, -0.846, 2, -1, 2.067, -1, 1, 2.133, -1, 2.2, -0.6, 2.267, -0.6, 1, 2.3, -0.6, 2.333, -1, 2.367, -1, 1, 2.456, -1, 2.544, 1, 2.633, 1, 1, 2.756, 1, 2.878, 0.269, 3, 0, 1, 3.156, -0.342, 3.311, -0.426, 3.467, -0.7, 1, 3.533, -0.818, 3.6, -1, 3.667, -1, 1, 3.733, -1, 3.8, -0.6, 3.867, -0.6, 1, 3.9, -0.6, 3.933, -1, 3.967, -1, 1, 4.056, -1, 4.144, 1, 4.233, 1, 1, 4.356, 1, 4.478, 0.345, 4.6, 0, 1, 4.722, -0.345, 4.844, -0.432, 4.967, -0.7, 1, 5.033, -0.846, 5.1, -1, 5.167, -1, 1, 5.233, -1, 5.3, -0.6, 5.367, -0.6, 1, 5.4, -0.6, 5.433, -1, 5.467, -1, 1, 5.556, -1, 5.644, 1, 5.733, 1, 1, 5.856, 1, 5.978, 0.345, 6.1, 0, 1, 6.222, -0.345, 6.344, -0.432, 6.467, -0.7, 1, 6.533, -0.846, 6.6, -1, 6.667, -1, 1, 6.767, -1, 6.867, -0.6, 6.967, -0.6, 1, 7, -0.6, 7.033, -1, 7.067, -1, 1, 7.156, -1, 7.244, 1, 7.333, 1, 1, 7.456, 1, 7.578, 0.345, 7.7, 0, 1, 7.822, -0.345, 7.944, -0.432, 8.067, -0.7, 1, 8.133, -0.846, 8.2, -1, 8.267, -1, 1, 8.333, -1, 8.4, -0.6, 8.467, -0.6, 1, 8.5, -0.6, 8.533, -1, 8.567, -1, 1, 8.656, -1, 8.744, 1, 8.833, 1, 1, 8.956, 1, 9.078, 0.345, 9.2, 0, 1, 9.322, -0.345, 9.444, -0.432, 9.567, -0.7, 1, 9.633, -0.846, 9.7, -1, 9.767, -1, 1, 9.833, -1, 9.9, -0.6, 9.967, -0.6, 1, 10.011, -0.6, 10.056, -1, 10.1, -1, 1, 10.211, -1, 10.322, 1, 10.433, 1, 1, 10.556, 1, 10.678, 0.345, 10.8, 0, 1, 10.922, -0.345, 11.044, -0.432, 11.167, -0.7, 1, 11.233, -0.846, 11.3, -1, 11.367, -1, 1, 11.433, -1, 11.5, -0.6, 11.567, -0.6, 1, 11.6, -0.6, 11.633, -1, 11.667, -1, 1, 11.756, -1, 11.844, 1, 11.933, 1, 1, 12.056, 1, 12.178, 0.345, 12.3, 0, 1, 12.422, -0.345, 12.544, -0.432, 12.667, -0.7, 1, 12.733, -0.846, 12.8, -1, 12.867, -1, 1, 12.933, -1, 13, -0.6, 13.067, -0.6, 1, 13.1, -0.6, 13.133, -1, 13.167, -1, 1, 13.267, -1, 13.367, 1, 13.467, 1, 1, 13.611, 1, 13.756, 0.404, 13.9, 0, 1, 14.022, -0.342, 14.144, -0.432, 14.267, -0.7, 1, 14.333, -0.846, 14.4, -1, 14.467, -1, 1, 14.533, -1, 14.6, -0.6, 14.667, -0.6, 1, 14.7, -0.6, 14.733, -1, 14.767, -1, 1, 14.856, -1, 14.944, 1, 15.033, 1, 1, 15.156, 1, 15.278, 0.345, 15.4, 0, 1, 15.522, -0.345, 15.644, -0.432, 15.767, -0.7, 1, 15.833, -0.846, 15.9, -1, 15.967, -1, 1, 16.033, -1, 16.1, -0.6, 16.167, -0.6, 1, 16.2, -0.6, 16.233, -1, 16.267, -1, 1, 16.356, -1, 16.444, 1, 16.533, 1, 1, 16.667, 1, 16.8, 0.319, 16.933, 0, 1, 17.078, -0.345, 17.222, -0.427, 17.367, -0.7, 1, 17.433, -0.826, 17.5, -1, 17.567, -1, 1, 17.633, -1, 17.7, -0.6, 17.767, -0.6, 1, 17.8, -0.6, 17.833, -1, 17.867, -1, 1, 17.956, -1, 18.044, 1, 18.133, 1, 1, 18.256, 1, 18.378, 0.345, 18.5, 0, 1, 18.622, -0.345, 18.744, -0.432, 18.867, -0.7, 1, 18.933, -0.846, 19, -1, 19.067, -1, 1, 19.133, -1, 19.2, -0.6, 19.267, -0.6, 1, 19.3, -0.6, 19.333, -1, 19.367, -1, 1, 19.456, -1, 19.544, 1, 19.633, 1, 1, 19.756, 1, 19.878, 0.291, 20, 0, 1, 20.144, -0.344, 20.289, -0.432, 20.433, -0.7, 1, 20.511, -0.844, 20.589, -1, 20.667, -1, 1, 20.733, -1, 20.8, -0.6, 20.867, -0.6, 1, 20.9, -0.6, 20.933, -1, 20.967, -1, 1, 21.056, -1, 21.144, 1, 21.233, 1, 1, 21.356, 1, 21.478, 0.345, 21.6, 0, 1, 21.722, -0.345, 21.844, -0.432, 21.967, -0.7, 1, 22.033, -0.846, 22.1, -1, 22.167, -1, 1, 22.233, -1, 22.3, -0.6, 22.367, -0.6, 1, 22.4, -0.6, 22.433, -1, 22.467, -1, 1, 22.556, -1, 22.644, 1, 22.733, 1, 1, 22.856, 1, 22.978, 0.345, 23.1, 0, 1, 23.222, -0.345, 23.344, -0.441, 23.467, -0.7, 1, 23.544, -0.865, 23.622, -1, 23.7, -1, 1, 23.778, -1, 23.856, -0.6, 23.933, -0.6, 1, 23.978, -0.6, 24.022, -1, 24.067, -1, 1, 24.156, -1, 24.244, 1, 24.333, 1, 1, 24.456, 1, 24.578, 0.345, 24.7, 0, 1, 24.822, -0.345, 24.944, -0.432, 25.067, -0.7, 1, 25.133, -0.846, 25.2, -1, 25.267, -1, 1, 25.333, -1, 25.4, -0.6, 25.467, -0.6, 1, 25.5, -0.6, 25.533, -1, 25.567, -1, 1, 25.656, -1, 25.744, 1, 25.833, 1, 1, 25.956, 1, 26.078, 0.345, 26.2, 0, 1, 26.322, -0.345, 26.444, -0.432, 26.567, -0.7, 1, 26.633, -0.846, 26.7, -1, 26.767, -1, 1, 26.844, -1, 26.922, -0.6, 27, -0.6, 1, 27.033, -0.6, 27.067, -1, 27.1, -1, 1, 27.2, -1, 27.3, 1, 27.4, 1, 1, 27.533, 1, 27.667, 0.375, 27.8, 0, 1, 27.922, -0.344, 28.044, -0.432, 28.167, -0.7, 1, 28.233, -0.846, 28.3, -1, 28.367, -1, 1, 28.433, -1, 28.5, -0.6, 28.567, -0.6, 1, 28.6, -0.6, 28.633, -1, 28.667, -1, 1, 28.756, -1, 28.844, 1, 28.933, 1, 1, 29.056, 1, 29.178, 0.345, 29.3, 0, 1, 29.422, -0.345, 29.544, -0.432, 29.667, -0.7, 1, 29.733, -0.846, 29.8, -1, 29.867, -1, 1, 29.933, -1, 30, -0.6, 30.067, -0.6, 1, 30.1, -0.6, 30.133, -1, 30.167, -1, 1, 30.267, -1, 30.367, 1, 30.467, 1, 1, 30.611, 1, 30.756, 0.404, 30.9, 0, 1, 31.022, -0.342, 31.144, -0.432, 31.267, -0.7, 1, 31.333, -0.846, 31.4, -1, 31.467, -1, 1, 31.533, -1, 31.6, -0.6, 31.667, -0.6, 1, 31.7, -0.6, 31.733, -1, 31.767, -1, 1, 31.856, -1, 31.944, 1, 32.033, 1, 1, 32.156, 1, 32.278, 0.345, 32.4, 0, 1, 32.522, -0.345, 32.644, -0.432, 32.767, -0.7, 1, 32.833, -0.846, 32.9, -1, 32.967, -1, 1, 33.033, -1, 33.1, -0.6, 33.167, -0.6, 1, 33.2, -0.6, 33.233, -1, 33.267, -1, 1, 33.356, -1, 33.444, 1, 33.533, 1, 1, 33.667, 1, 33.8, 0, 33.933, 0]}, {"Target": "Parameter", "Id": "Param344", "Segments": [0, -0.7, 1, 0.033, -0.7, 0.067, -1, 0.1, -1, 1, 0.2, -1, 0.3, -1, 0.4, -1, 1, 0.433, -1, 0.467, 1, 0.5, 1, 1, 0.556, 1, 0.611, -1, 0.667, -1, 1, 0.7, -1, 0.733, -1, 0.767, -1, 1, 0.878, -1, 0.989, -0.7, 1.1, -0.7, 1, 1.178, -0.7, 1.256, -1, 1.333, -1, 1, 1.389, -1, 1.444, -0.7, 1.5, -0.7, 1, 1.533, -0.7, 1.567, -1, 1.6, -1, 1, 1.7, -1, 1.8, 1, 1.9, 1, 1, 2.022, 1, 2.144, 1, 2.267, 0.8, 1, 2.378, 0.611, 2.489, -0.318, 2.6, -0.7, 1, 2.678, -0.967, 2.756, -1, 2.833, -1, 1, 2.889, -1, 2.944, -0.7, 3, -0.7, 1, 3.033, -0.7, 3.067, -1, 3.1, -1, 1, 3.233, -1, 3.367, 1, 3.5, 1, 1, 3.622, 1, 3.744, 1, 3.867, 0.8, 1, 3.978, 0.611, 4.089, -0.318, 4.2, -0.7, 1, 4.278, -0.967, 4.356, -1, 4.433, -1, 1, 4.489, -1, 4.544, -0.7, 4.6, -0.7, 1, 4.633, -0.7, 4.667, -1, 4.7, -1, 1, 4.8, -1, 4.9, 1, 5, 1, 1, 5.122, 1, 5.244, 1, 5.367, 0.8, 1, 5.478, 0.611, 5.589, -0.318, 5.7, -0.7, 1, 5.778, -0.967, 5.856, -1, 5.933, -1, 1, 5.989, -1, 6.044, -0.7, 6.1, -0.7, 1, 6.133, -0.7, 6.167, -1, 6.2, -1, 1, 6.3, -1, 6.4, 1, 6.5, 1, 1, 6.656, 1, 6.811, 1, 6.967, 0.8, 1, 7.078, 0.656, 7.189, -0.318, 7.3, -0.7, 1, 7.378, -0.967, 7.456, -1, 7.533, -1, 1, 7.589, -1, 7.644, -0.7, 7.7, -0.7, 1, 7.733, -0.7, 7.767, -1, 7.8, -1, 1, 7.9, -1, 8, 1, 8.1, 1, 1, 8.222, 1, 8.344, 1, 8.467, 0.8, 1, 8.578, 0.611, 8.689, -0.318, 8.8, -0.7, 1, 8.878, -0.967, 8.956, -1, 9.033, -1, 1, 9.089, -1, 9.144, -0.7, 9.2, -0.7, 1, 9.233, -0.7, 9.267, -1, 9.3, -1, 1, 9.4, -1, 9.5, 1, 9.6, 1, 1, 9.722, 1, 9.844, 1, 9.967, 0.8, 1, 10.111, 0.55, 10.256, -0.254, 10.4, -0.7, 1, 10.478, -0.94, 10.556, -1, 10.633, -1, 1, 10.689, -1, 10.744, -0.7, 10.8, -0.7, 1, 10.833, -0.7, 10.867, -1, 10.9, -1, 1, 11, -1, 11.1, 1, 11.2, 1, 1, 11.322, 1, 11.444, 1, 11.567, 0.8, 1, 11.678, 0.611, 11.789, -0.318, 11.9, -0.7, 1, 11.978, -0.967, 12.056, -1, 12.133, -1, 1, 12.189, -1, 12.244, -0.7, 12.3, -0.7, 1, 12.333, -0.7, 12.367, -1, 12.4, -1, 1, 12.5, -1, 12.6, 1, 12.7, 1, 1, 12.822, 1, 12.944, 1, 13.067, 0.8, 1, 13.178, 0.611, 13.289, -0.414, 13.4, -0.7, 1, 13.511, -0.986, 13.622, -1, 13.733, -1, 1, 13.789, -1, 13.844, -0.7, 13.9, -0.7, 1, 13.933, -0.7, 13.967, -1, 14, -1, 1, 14.1, -1, 14.2, 1, 14.3, 1, 1, 14.422, 1, 14.544, 1, 14.667, 0.8, 1, 14.778, 0.611, 14.889, -0.318, 15, -0.7, 1, 15.078, -0.967, 15.156, -1, 15.233, -1, 1, 15.289, -1, 15.344, -0.7, 15.4, -0.7, 1, 15.433, -0.7, 15.467, -1, 15.5, -1, 1, 15.6, -1, 15.7, 1, 15.8, 1, 1, 15.922, 1, 16.044, 1, 16.167, 0.8, 1, 16.278, 0.611, 16.389, -0.318, 16.5, -0.7, 1, 16.578, -0.967, 16.656, -1, 16.733, -1, 1, 16.8, -1, 16.867, -0.7, 16.933, -0.7, 1, 16.978, -0.7, 17.022, -1, 17.067, -1, 1, 17.178, -1, 17.289, 1, 17.4, 1, 1, 17.522, 1, 17.644, 1, 17.767, 0.8, 1, 17.878, 0.611, 17.989, -0.318, 18.1, -0.7, 1, 18.178, -0.967, 18.256, -1, 18.333, -1, 1, 18.389, -1, 18.444, -0.7, 18.5, -0.7, 1, 18.533, -0.7, 18.567, -1, 18.6, -1, 1, 18.7, -1, 18.8, 1, 18.9, 1, 1, 19.022, 1, 19.144, 1, 19.267, 0.8, 1, 19.378, 0.611, 19.489, -0.318, 19.6, -0.7, 1, 19.678, -0.967, 19.756, -1, 19.833, -1, 1, 19.889, -1, 19.944, -0.7, 20, -0.7, 1, 20.033, -0.7, 20.067, -1, 20.1, -1, 1, 20.222, -1, 20.344, 1, 20.467, 1, 1, 20.6, 1, 20.733, 1, 20.867, 0.8, 1, 20.978, 0.628, 21.089, -0.318, 21.2, -0.7, 1, 21.278, -0.967, 21.356, -1, 21.433, -1, 1, 21.489, -1, 21.544, -0.7, 21.6, -0.7, 1, 21.633, -0.7, 21.667, -1, 21.7, -1, 1, 21.8, -1, 21.9, 1, 22, 1, 1, 22.122, 1, 22.244, 1, 22.367, 0.8, 1, 22.478, 0.611, 22.589, -0.318, 22.7, -0.7, 1, 22.778, -0.967, 22.856, -1, 22.933, -1, 1, 22.989, -1, 23.044, -0.7, 23.1, -0.7, 1, 23.133, -0.7, 23.167, -1, 23.2, -1, 1, 23.3, -1, 23.4, 1, 23.5, 1, 1, 23.644, 1, 23.789, 1, 23.933, 0.8, 1, 24.056, 0.625, 24.178, -0.294, 24.3, -0.7, 1, 24.378, -0.959, 24.456, -1, 24.533, -1, 1, 24.589, -1, 24.644, -0.7, 24.7, -0.7, 1, 24.733, -0.7, 24.767, -1, 24.8, -1, 1, 24.9, -1, 25, 1, 25.1, 1, 1, 25.222, 1, 25.344, 1, 25.467, 0.8, 1, 25.578, 0.611, 25.689, -0.318, 25.8, -0.7, 1, 25.878, -0.967, 25.956, -1, 26.033, -1, 1, 26.089, -1, 26.144, -0.7, 26.2, -0.7, 1, 26.233, -0.7, 26.267, -1, 26.3, -1, 1, 26.4, -1, 26.5, 1, 26.6, 1, 1, 26.733, 1, 26.867, 1, 27, 0.8, 1, 27.122, 0.609, 27.244, -0.328, 27.367, -0.7, 1, 27.456, -0.97, 27.544, -1, 27.633, -1, 1, 27.689, -1, 27.744, -0.7, 27.8, -0.7, 1, 27.833, -0.7, 27.867, -1, 27.9, -1, 1, 28, -1, 28.1, 1, 28.2, 1, 1, 28.322, 1, 28.444, 1, 28.567, 0.8, 1, 28.678, 0.611, 28.789, -0.318, 28.9, -0.7, 1, 28.978, -0.967, 29.056, -1, 29.133, -1, 1, 29.189, -1, 29.244, -0.7, 29.3, -0.7, 1, 29.333, -0.7, 29.367, -1, 29.4, -1, 1, 29.5, -1, 29.6, 1, 29.7, 1, 1, 29.822, 1, 29.944, 1, 30.067, 0.8, 1, 30.189, 0.59, 30.311, -0.328, 30.433, -0.7, 1, 30.522, -0.97, 30.611, -1, 30.7, -1, 1, 30.767, -1, 30.833, -0.7, 30.9, -0.7, 1, 30.933, -0.7, 30.967, -1, 31, -1, 1, 31.1, -1, 31.2, 1, 31.3, 1, 1, 31.422, 1, 31.544, 1, 31.667, 0.8, 1, 31.778, 0.611, 31.889, -0.318, 32, -0.7, 1, 32.078, -0.967, 32.156, -1, 32.233, -1, 1, 32.289, -1, 32.344, -0.7, 32.4, -0.7, 1, 32.433, -0.7, 32.467, -1, 32.5, -1, 1, 32.6, -1, 32.7, 1, 32.8, 1, 1, 32.922, 1, 33.044, 1, 33.167, 0.8, 1, 33.278, 0.611, 33.389, -0.354, 33.5, -0.7, 1, 33.589, -0.977, 33.678, -1, 33.767, -1, 1, 33.822, -1, 33.878, -0.7, 33.933, -0.7]}, {"Target": "Parameter", "Id": "Param345", "Segments": [0, 0.8, 1, 0.256, 0.8, 0.511, -0.7, 0.767, -0.7, 1, 1.011, -0.7, 1.256, 0.8, 1.5, 0.8, 1, 1.756, 0.8, 2.011, -0.7, 2.267, -0.7, 1, 2.511, -0.7, 2.756, 0.8, 3, 0.8, 1, 3.289, 0.8, 3.578, -0.7, 3.867, -0.7, 1, 4.111, -0.7, 4.356, 0.8, 4.6, 0.8, 1, 4.856, 0.8, 5.111, -0.7, 5.367, -0.7, 1, 5.611, -0.7, 5.856, 0.8, 6.1, 0.8, 1, 6.389, 0.8, 6.678, -0.7, 6.967, -0.7, 1, 7.211, -0.7, 7.456, 0.8, 7.7, 0.8, 1, 7.956, 0.8, 8.211, -0.7, 8.467, -0.7, 1, 8.711, -0.7, 8.956, 0.8, 9.2, 0.8, 1, 9.456, 0.8, 9.711, -0.7, 9.967, -0.7, 1, 10.244, -0.7, 10.522, 0.8, 10.8, 0.8, 1, 11.056, 0.8, 11.311, -0.7, 11.567, -0.7, 1, 11.811, -0.7, 12.056, 0.8, 12.3, 0.8, 1, 12.556, 0.8, 12.811, -0.7, 13.067, -0.7, 1, 13.344, -0.7, 13.622, 0.8, 13.9, 0.8, 1, 14.156, 0.8, 14.411, -0.7, 14.667, -0.7, 1, 14.911, -0.7, 15.156, 0.8, 15.4, 0.8, 1, 15.656, 0.8, 15.911, -0.7, 16.167, -0.7, 1, 16.422, -0.7, 16.678, 0.8, 16.933, 0.8, 1, 17.211, 0.8, 17.489, -0.7, 17.767, -0.7, 1, 18.011, -0.7, 18.256, 0.8, 18.5, 0.8, 1, 18.756, 0.8, 19.011, -0.7, 19.267, -0.7, 1, 19.511, -0.7, 19.756, 0.8, 20, 0.8, 1, 20.289, 0.8, 20.578, -0.7, 20.867, -0.7, 1, 21.111, -0.7, 21.356, 0.8, 21.6, 0.8, 1, 21.856, 0.8, 22.111, -0.7, 22.367, -0.7, 1, 22.611, -0.7, 22.856, 0.8, 23.1, 0.8, 1, 23.378, 0.8, 23.656, -0.7, 23.933, -0.7, 1, 24.189, -0.7, 24.444, 0.8, 24.7, 0.8, 1, 24.956, 0.8, 25.211, -0.7, 25.467, -0.7, 1, 25.711, -0.7, 25.956, 0.8, 26.2, 0.8, 1, 26.467, 0.8, 26.733, -0.7, 27, -0.7, 1, 27.267, -0.7, 27.533, 0.8, 27.8, 0.8, 1, 28.056, 0.8, 28.311, -0.7, 28.567, -0.7, 1, 28.811, -0.7, 29.056, 0.8, 29.3, 0.8, 1, 29.556, 0.8, 29.811, -0.7, 30.067, -0.7, 1, 30.344, -0.7, 30.622, 0.8, 30.9, 0.8, 1, 31.156, 0.8, 31.411, -0.7, 31.667, -0.7, 1, 31.911, -0.7, 32.156, 0.8, 32.4, 0.8, 1, 32.656, 0.8, 32.911, -0.7, 33.167, -0.7, 1, 33.422, -0.7, 33.678, 0.8, 33.933, 0.8]}, {"Target": "Parameter", "Id": "Param348", "Segments": [0, 0.046, 1, 0.067, 0.262, 0.133, 0.5, 0.2, 0.5, 1, 0.333, 0.5, 0.467, -0.3, 0.6, -0.3, 1, 0.722, -0.3, 0.844, 0.5, 0.967, 0.5, 1, 1.089, 0.5, 1.211, -0.3, 1.333, -0.3, 1, 1.456, -0.3, 1.578, 0.5, 1.7, 0.5, 1, 1.833, 0.5, 1.967, -0.3, 2.1, -0.3, 1, 2.222, -0.3, 2.344, 0.5, 2.467, 0.5, 1, 2.589, 0.5, 2.711, -0.3, 2.833, -0.3, 1, 2.956, -0.3, 3.078, 0.5, 3.2, 0.5, 1, 3.367, 0.5, 3.533, -0.3, 3.7, -0.3, 1, 3.822, -0.3, 3.944, 0.5, 4.067, 0.5, 1, 4.189, 0.5, 4.311, -0.3, 4.433, -0.3, 1, 4.556, -0.3, 4.678, 0.5, 4.8, 0.5, 1, 4.933, 0.5, 5.067, -0.3, 5.2, -0.3, 1, 5.322, -0.3, 5.444, 0.5, 5.567, 0.5, 1, 5.689, 0.5, 5.811, -0.3, 5.933, -0.3, 1, 6.056, -0.3, 6.178, 0.5, 6.3, 0.5, 1, 6.444, 0.5, 6.589, -0.3, 6.733, -0.3, 1, 6.878, -0.3, 7.022, 0.5, 7.167, 0.5, 1, 7.289, 0.5, 7.411, -0.3, 7.533, -0.3, 1, 7.656, -0.3, 7.778, 0.5, 7.9, 0.5, 1, 8.033, 0.5, 8.167, -0.3, 8.3, -0.3, 1, 8.422, -0.3, 8.544, 0.5, 8.667, 0.5, 1, 8.8, 0.5, 8.933, -0.3, 9.067, -0.3, 1, 9.189, -0.3, 9.311, 0.5, 9.433, 0.5, 1, 9.556, 0.5, 9.678, -0.3, 9.8, -0.3, 1, 9.944, -0.3, 10.089, 0.5, 10.233, 0.5, 1, 10.378, 0.5, 10.522, -0.3, 10.667, -0.3, 1, 10.789, -0.3, 10.911, 0.5, 11.033, 0.5, 1, 11.156, 0.5, 11.278, -0.3, 11.4, -0.3, 1, 11.522, -0.3, 11.644, 0.5, 11.767, 0.5, 1, 11.9, 0.5, 12.033, -0.3, 12.167, -0.3, 1, 12.289, -0.3, 12.411, 0.5, 12.533, 0.5, 1, 12.656, 0.5, 12.778, -0.3, 12.9, -0.3, 1, 13.022, -0.3, 13.144, 0.5, 13.267, 0.5, 1, 13.433, 0.5, 13.6, -0.3, 13.767, -0.3, 1, 13.889, -0.3, 14.011, 0.5, 14.133, 0.5, 1, 14.256, 0.5, 14.378, -0.3, 14.5, -0.3, 1, 14.622, -0.3, 14.744, 0.5, 14.867, 0.5, 1, 15, 0.5, 15.133, -0.3, 15.267, -0.3, 1, 15.389, -0.3, 15.511, 0.5, 15.633, 0.5, 1, 15.756, 0.5, 15.878, -0.3, 16, -0.3, 1, 16.122, -0.3, 16.244, 0.5, 16.367, 0.5, 1, 16.5, 0.5, 16.633, -0.3, 16.767, -0.3, 1, 16.911, -0.3, 17.056, 0.5, 17.2, 0.5, 1, 17.333, 0.5, 17.467, -0.3, 17.6, -0.3, 1, 17.722, -0.3, 17.844, 0.5, 17.967, 0.5, 1, 18.089, 0.5, 18.211, -0.3, 18.333, -0.3, 1, 18.456, -0.3, 18.578, 0.5, 18.7, 0.5, 1, 18.833, 0.5, 18.967, -0.3, 19.1, -0.3, 1, 19.222, -0.3, 19.344, 0.5, 19.467, 0.5, 1, 19.589, 0.5, 19.711, -0.3, 19.833, -0.3, 1, 19.967, -0.3, 20.1, 0.5, 20.233, 0.5, 1, 20.389, 0.5, 20.544, -0.3, 20.7, -0.3, 1, 20.822, -0.3, 20.944, 0.5, 21.067, 0.5, 1, 21.189, 0.5, 21.311, -0.3, 21.433, -0.3, 1, 21.556, -0.3, 21.678, 0.5, 21.8, 0.5, 1, 21.933, 0.5, 22.067, -0.3, 22.2, -0.3, 1, 22.322, -0.3, 22.444, 0.5, 22.567, 0.5, 1, 22.689, 0.5, 22.811, -0.3, 22.933, -0.3, 1, 23.056, -0.3, 23.178, 0.5, 23.3, 0.5, 1, 23.444, 0.5, 23.589, -0.3, 23.733, -0.3, 1, 23.878, -0.3, 24.022, 0.5, 24.167, 0.5, 1, 24.289, 0.5, 24.411, -0.3, 24.533, -0.3, 1, 24.656, -0.3, 24.778, 0.5, 24.9, 0.5, 1, 25.033, 0.5, 25.167, -0.3, 25.3, -0.3, 1, 25.422, -0.3, 25.544, 0.5, 25.667, 0.5, 1, 25.8, 0.5, 25.933, -0.3, 26.067, -0.3, 1, 26.189, -0.3, 26.311, 0.5, 26.433, 0.5, 1, 26.556, 0.5, 26.678, -0.3, 26.8, -0.3, 1, 26.944, -0.3, 27.089, 0.5, 27.233, 0.5, 1, 27.378, 0.5, 27.522, -0.3, 27.667, -0.3, 1, 27.789, -0.3, 27.911, 0.5, 28.033, 0.5, 1, 28.156, 0.5, 28.278, -0.3, 28.4, -0.3, 1, 28.522, -0.3, 28.644, 0.5, 28.767, 0.5, 1, 28.9, 0.5, 29.033, -0.3, 29.167, -0.3, 1, 29.289, -0.3, 29.411, 0.5, 29.533, 0.5, 1, 29.656, 0.5, 29.778, -0.3, 29.9, -0.3, 1, 30.033, -0.3, 30.167, 0.5, 30.3, 0.5, 1, 30.444, 0.5, 30.589, -0.3, 30.733, -0.3, 1, 30.867, -0.3, 31, 0.5, 31.133, 0.5, 1, 31.256, 0.5, 31.378, -0.3, 31.5, -0.3, 1, 31.622, -0.3, 31.744, 0.5, 31.867, 0.5, 1, 32, 0.5, 32.133, -0.3, 32.267, -0.3, 1, 32.389, -0.3, 32.511, 0.5, 32.633, 0.5, 1, 32.756, 0.5, 32.878, -0.3, 33, -0.3, 1, 33.122, -0.3, 33.244, 0.5, 33.367, 0.5, 1, 33.511, 0.5, 33.656, -0.3, 33.8, -0.3, 1, 33.844, -0.3, 33.889, 0.1, 33.933, 0.1]}, {"Target": "Parameter", "Id": "BodyAngleX2", "Segments": [0, -10, 1, 0.122, -10, 0.244, -6.363, 0.367, 0, 1, 0.5, 6.941, 0.633, 10, 0.767, 10, 1, 0.889, 10, 1.011, 6.667, 1.133, 0, 1, 1.256, -6.667, 1.378, -10, 1.5, -10, 1, 1.622, -10, 1.744, -6.363, 1.867, 0, 1, 2, 6.941, 2.133, 10, 2.267, 10, 1, 2.389, 10, 2.511, 6.667, 2.633, 0, 1, 2.756, -6.667, 2.878, -10, 3, -10, 1, 3.122, -10, 3.244, -5.491, 3.367, 0, 1, 3.533, 7.487, 3.7, 10, 3.867, 10, 1, 3.989, 10, 4.111, 6.667, 4.233, 0, 1, 4.356, -6.667, 4.478, -10, 4.6, -10, 1, 4.722, -10, 4.844, -6.363, 4.967, 0, 1, 5.1, 6.941, 5.233, 10, 5.367, 10, 1, 5.489, 10, 5.611, 6.667, 5.733, 0, 1, 5.856, -6.667, 5.978, -10, 6.1, -10, 1, 6.222, -10, 6.344, -6.363, 6.467, 0, 1, 6.6, 6.941, 6.733, 10, 6.867, 10, 1, 7.022, 10, 7.178, 7.344, 7.333, 0, 1, 7.456, -5.77, 7.578, -10, 7.7, -10, 1, 7.822, -10, 7.944, -6.363, 8.067, 0, 1, 8.2, 6.941, 8.333, 10, 8.467, 10, 1, 8.589, 10, 8.711, 6.667, 8.833, 0, 1, 8.956, -6.667, 9.078, -10, 9.2, -10, 1, 9.322, -10, 9.444, -6.363, 9.567, 0, 1, 9.7, 6.941, 9.833, 10, 9.967, 10, 1, 10.089, 10, 10.211, 5.77, 10.333, 0, 1, 10.489, -7.344, 10.644, -10, 10.8, -10, 1, 10.922, -10, 11.044, -6.363, 11.167, 0, 1, 11.3, 6.941, 11.433, 10, 11.567, 10, 1, 11.689, 10, 11.811, 6.667, 11.933, 0, 1, 12.056, -6.667, 12.178, -10, 12.3, -10, 1, 12.422, -10, 12.544, -6.363, 12.667, 0, 1, 12.8, 6.941, 12.933, 10, 13.067, 10, 1, 13.189, 10, 13.311, 6.363, 13.433, 0, 1, 13.567, -6.941, 13.7, -10, 13.833, -10, 1, 13.978, -10, 14.122, -6.92, 14.267, 0, 1, 14.4, 6.388, 14.533, 10, 14.667, 10, 1, 14.789, 10, 14.911, 6.667, 15.033, 0, 1, 15.156, -6.667, 15.278, -10, 15.4, -10, 1, 15.522, -10, 15.644, -6.363, 15.767, 0, 1, 15.9, 6.941, 16.033, 10, 16.167, 10, 1, 16.289, 10, 16.411, 6.667, 16.533, 0, 1, 16.656, -6.667, 16.778, -10, 16.9, -10, 1, 17.033, -10, 17.167, -6.112, 17.3, 0, 1, 17.456, 7.131, 17.611, 10, 17.767, 10, 1, 17.889, 10, 18.011, 6.667, 18.133, 0, 1, 18.256, -6.667, 18.378, -10, 18.5, -10, 1, 18.622, -10, 18.744, -6.363, 18.867, 0, 1, 19, 6.941, 19.133, 10, 19.267, 10, 1, 19.389, 10, 19.511, 6.667, 19.633, 0, 1, 19.756, -6.667, 19.878, -10, 20, -10, 1, 20.122, -10, 20.244, -5.77, 20.367, 0, 1, 20.522, 7.344, 20.678, 10, 20.833, 10, 1, 20.967, 10, 21.1, 6.941, 21.233, 0, 1, 21.356, -6.363, 21.478, -10, 21.6, -10, 1, 21.722, -10, 21.844, -6.363, 21.967, 0, 1, 22.1, 6.941, 22.233, 10, 22.367, 10, 1, 22.489, 10, 22.611, 6.667, 22.733, 0, 1, 22.856, -6.667, 22.978, -10, 23.1, -10, 1, 23.222, -10, 23.344, -6.363, 23.467, 0, 1, 23.6, 6.941, 23.733, 10, 23.867, 10, 1, 24.011, 10, 24.156, 6.92, 24.3, 0, 1, 24.433, -6.388, 24.567, -10, 24.7, -10, 1, 24.822, -10, 24.944, -6.363, 25.067, 0, 1, 25.2, 6.941, 25.333, 10, 25.467, 10, 1, 25.589, 10, 25.711, 6.667, 25.833, 0, 1, 25.956, -6.667, 26.078, -10, 26.2, -10, 1, 26.322, -10, 26.444, -6.363, 26.567, 0, 1, 26.7, 6.941, 26.833, 10, 26.967, 10, 1, 27.1, 10, 27.233, 6.667, 27.367, 0, 1, 27.5, -6.667, 27.633, -10, 27.767, -10, 1, 27.9, -10, 28.033, -6.667, 28.167, 0, 1, 28.3, 6.667, 28.433, 10, 28.567, 10, 1, 28.689, 10, 28.811, 6.667, 28.933, 0, 1, 29.056, -6.667, 29.178, -10, 29.3, -10, 1, 29.422, -10, 29.544, -6.363, 29.667, 0, 1, 29.8, 6.941, 29.933, 10, 30.067, 10, 1, 30.189, 10, 30.311, 6.363, 30.433, 0, 1, 30.567, -6.941, 30.7, -10, 30.833, -10, 1, 30.978, -10, 31.122, -6.92, 31.267, 0, 1, 31.4, 6.388, 31.533, 10, 31.667, 10, 1, 31.789, 10, 31.911, 6.667, 32.033, 0, 1, 32.156, -6.667, 32.278, -10, 32.4, -10, 1, 32.522, -10, 32.644, -6.363, 32.767, 0, 1, 32.9, 6.941, 33.033, 10, 33.167, 10, 1, 33.289, 10, 33.411, 6.667, 33.533, 0, 1, 33.656, -6.667, 33.778, -10, 33.9, -10, 1, 33.911, -10, 33.922, -9.977, 33.933, -9.931]}, {"Target": "Parameter", "Id": "BodyAngleZ2", "Segments": [0, 0, 1, 0.133, 0.694, 0.267, 1, 0.4, 1, 1, 0.522, 1, 0.644, 0.667, 0.767, 0, 1, 0.889, -0.667, 1.011, -1, 1.133, -1, 1, 1.256, -1, 1.378, -0.636, 1.5, 0, 1, 1.633, 0.694, 1.767, 1, 1.9, 1, 1, 2.022, 1, 2.144, 0.667, 2.267, 0, 1, 2.389, -0.667, 2.511, -1, 2.633, -1, 1, 2.756, -1, 2.878, -0.551, 3, 0, 1, 3.167, 0.751, 3.333, 1, 3.5, 1, 1, 3.622, 1, 3.744, 0.667, 3.867, 0, 1, 3.989, -0.667, 4.111, -1, 4.233, -1, 1, 4.356, -1, 4.478, -0.636, 4.6, 0, 1, 4.733, 0.694, 4.867, 1, 5, 1, 1, 5.122, 1, 5.244, 0.667, 5.367, 0, 1, 5.489, -0.667, 5.611, -1, 5.733, -1, 1, 5.856, -1, 5.978, -0.636, 6.1, 0, 1, 6.233, 0.694, 6.367, 1, 6.5, 1, 1, 6.656, 1, 6.811, 0.736, 6.967, 0, 1, 7.089, -0.578, 7.211, -1, 7.333, -1, 1, 7.456, -1, 7.578, -0.636, 7.7, 0, 1, 7.833, 0.694, 7.967, 1, 8.1, 1, 1, 8.222, 1, 8.344, 0.667, 8.467, 0, 1, 8.589, -0.667, 8.711, -1, 8.833, -1, 1, 8.956, -1, 9.078, -0.636, 9.2, 0, 1, 9.333, 0.694, 9.467, 1, 9.6, 1, 1, 9.722, 1, 9.844, 0.578, 9.967, 0, 1, 10.122, -0.736, 10.278, -1, 10.433, -1, 1, 10.556, -1, 10.678, -0.636, 10.8, 0, 1, 10.933, 0.694, 11.067, 1, 11.2, 1, 1, 11.322, 1, 11.444, 0.667, 11.567, 0, 1, 11.689, -0.667, 11.811, -1, 11.933, -1, 1, 12.056, -1, 12.178, -0.636, 12.3, 0, 1, 12.433, 0.694, 12.567, 1, 12.7, 1, 1, 12.822, 1, 12.944, 0.636, 13.067, 0, 1, 13.2, -0.694, 13.333, -1, 13.467, -1, 1, 13.611, -1, 13.756, -0.692, 13.9, 0, 1, 14.033, 0.639, 14.167, 1, 14.3, 1, 1, 14.422, 1, 14.544, 0.667, 14.667, 0, 1, 14.789, -0.667, 14.911, -1, 15.033, -1, 1, 15.156, -1, 15.278, -0.636, 15.4, 0, 1, 15.533, 0.694, 15.667, 1, 15.8, 1, 1, 15.922, 1, 16.044, 0.667, 16.167, 0, 1, 16.289, -0.667, 16.411, -1, 16.533, -1, 1, 16.667, -1, 16.8, -0.612, 16.933, 0, 1, 17.089, 0.714, 17.244, 1, 17.4, 1, 1, 17.522, 1, 17.644, 0.667, 17.767, 0, 1, 17.889, -0.667, 18.011, -1, 18.133, -1, 1, 18.256, -1, 18.378, -0.636, 18.5, 0, 1, 18.633, 0.694, 18.767, 1, 18.9, 1, 1, 19.022, 1, 19.144, 0.667, 19.267, 0, 1, 19.389, -0.667, 19.511, -1, 19.633, -1, 1, 19.756, -1, 19.878, -0.578, 20, 0, 1, 20.156, 0.736, 20.311, 1, 20.467, 1, 1, 20.6, 1, 20.733, 0.694, 20.867, 0, 1, 20.989, -0.636, 21.111, -1, 21.233, -1, 1, 21.356, -1, 21.478, -0.636, 21.6, 0, 1, 21.733, 0.694, 21.867, 1, 22, 1, 1, 22.122, 1, 22.244, 0.667, 22.367, 0, 1, 22.489, -0.667, 22.611, -1, 22.733, -1, 1, 22.856, -1, 22.978, -0.636, 23.1, 0, 1, 23.233, 0.694, 23.367, 1, 23.5, 1, 1, 23.644, 1, 23.789, 0.692, 23.933, 0, 1, 24.067, -0.639, 24.2, -1, 24.333, -1, 1, 24.456, -1, 24.578, -0.636, 24.7, 0, 1, 24.833, 0.694, 24.967, 1, 25.1, 1, 1, 25.222, 1, 25.344, 0.667, 25.467, 0, 1, 25.589, -0.667, 25.711, -1, 25.833, -1, 1, 25.956, -1, 26.078, -0.636, 26.2, 0, 1, 26.333, 0.694, 26.467, 1, 26.6, 1, 1, 26.733, 1, 26.867, 0.667, 27, 0, 1, 27.133, -0.667, 27.267, -1, 27.4, -1, 1, 27.533, -1, 27.667, -0.667, 27.8, 0, 1, 27.933, 0.667, 28.067, 1, 28.2, 1, 1, 28.322, 1, 28.444, 0.667, 28.567, 0, 1, 28.689, -0.667, 28.811, -1, 28.933, -1, 1, 29.056, -1, 29.178, -0.636, 29.3, 0, 1, 29.433, 0.694, 29.567, 1, 29.7, 1, 1, 29.822, 1, 29.944, 0.636, 30.067, 0, 1, 30.2, -0.694, 30.333, -1, 30.467, -1, 1, 30.611, -1, 30.756, -0.692, 30.9, 0, 1, 31.033, 0.639, 31.167, 1, 31.3, 1, 1, 31.422, 1, 31.544, 0.667, 31.667, 0, 1, 31.789, -0.667, 31.911, -1, 32.033, -1, 1, 32.156, -1, 32.278, -0.636, 32.4, 0, 1, 32.533, 0.694, 32.667, 1, 32.8, 1, 1, 32.922, 1, 33.044, 0.667, 33.167, 0, 1, 33.289, -0.667, 33.411, -1, 33.533, -1, 1, 33.667, -1, 33.8, -0.667, 33.933, 0]}, {"Target": "Parameter", "Id": "HipAngleXBS2", "Segments": [0, 0, 1, 0.133, 0, 0.267, -30, 0.4, -30, 1, 0.522, -30, 0.644, -20, 0.767, 0, 1, 0.889, 20, 1.011, 30, 1.133, 30, 1, 1.256, 30, 1.378, 19.091, 1.5, 0, 1, 1.633, -20.826, 1.767, -30, 1.9, -30, 1, 2.022, -30, 2.144, -20, 2.267, 0, 1, 2.389, 20, 2.511, 30, 2.633, 30, 1, 2.756, 30, 2.878, 16.492, 3, 0, 1, 3.167, -22.489, 3.333, -30, 3.5, -30, 1, 3.622, -30, 3.744, -20, 3.867, 0, 1, 3.989, 20, 4.111, 30, 4.233, 30, 1, 4.356, 30, 4.478, 19.091, 4.6, 0, 1, 4.733, -20.826, 4.867, -30, 5, -30, 1, 5.122, -30, 5.244, -20, 5.367, 0, 1, 5.489, 20, 5.611, 30, 5.733, 30, 1, 5.856, 30, 5.978, 19.091, 6.1, 0, 1, 6.233, -20.826, 6.367, -30, 6.5, -30, 1, 6.656, -30, 6.811, -22.05, 6.967, 0, 1, 7.089, 17.325, 7.211, 30, 7.333, 30, 1, 7.456, 30, 7.578, 19.091, 7.7, 0, 1, 7.833, -20.826, 7.967, -30, 8.1, -30, 1, 8.222, -30, 8.344, -20, 8.467, 0, 1, 8.589, 20, 8.711, 30, 8.833, 30, 1, 8.956, 30, 9.078, 19.091, 9.2, 0, 1, 9.333, -20.826, 9.467, -30, 9.6, -30, 1, 9.722, -30, 9.844, -17.325, 9.967, 0, 1, 10.122, 22.05, 10.278, 30, 10.433, 30, 1, 10.556, 30, 10.678, 19.091, 10.8, 0, 1, 10.933, -20.826, 11.067, -30, 11.2, -30, 1, 11.322, -30, 11.444, -20, 11.567, 0, 1, 11.689, 20, 11.811, 30, 11.933, 30, 1, 12.056, 30, 12.178, 19.091, 12.3, 0, 1, 12.433, -20.826, 12.567, -30, 12.7, -30, 1, 12.822, -30, 12.944, -19.091, 13.067, 0, 1, 13.2, 20.826, 13.333, 30, 13.467, 30, 1, 13.611, 30, 13.756, 20.763, 13.9, 0, 1, 14.033, -19.166, 14.167, -30, 14.3, -30, 1, 14.422, -30, 14.544, -20, 14.667, 0, 1, 14.789, 20, 14.911, 30, 15.033, 30, 1, 15.156, 30, 15.278, 19.091, 15.4, 0, 1, 15.533, -20.826, 15.667, -30, 15.8, -30, 1, 15.922, -30, 16.044, -20, 16.167, 0, 1, 16.289, 20, 16.411, 30, 16.533, 30, 1, 16.656, 30, 16.778, 16.492, 16.9, 0, 1, 17.067, -22.489, 17.233, -30, 17.4, -30, 1, 17.522, -30, 17.644, -20, 17.767, 0, 1, 17.889, 20, 18.011, 30, 18.133, 30, 1, 18.256, 30, 18.378, 19.091, 18.5, 0, 1, 18.633, -20.826, 18.767, -30, 18.9, -30, 1, 19.022, -30, 19.144, -20, 19.267, 0, 1, 19.389, 20, 19.511, 30, 19.633, 30, 1, 19.756, 30, 19.878, 17.325, 20, 0, 1, 20.156, -22.05, 20.311, -30, 20.467, -30, 1, 20.622, -30, 20.778, -22.05, 20.933, 0, 1, 21.056, 17.325, 21.178, 30, 21.3, 30, 1, 21.422, 30, 21.544, 19.091, 21.667, 0, 1, 21.8, -20.826, 21.933, -30, 22.067, -30, 1, 22.189, -30, 22.311, -20, 22.433, 0, 1, 22.556, 20, 22.678, 30, 22.8, 30, 1, 22.922, 30, 23.044, 19.091, 23.167, 0, 1, 23.3, -20.826, 23.433, -30, 23.567, -30, 1, 23.689, -30, 23.811, -19.091, 23.933, 0, 1, 24.067, 20.826, 24.2, 30, 24.333, 30, 1, 24.456, 30, 24.578, 19.091, 24.7, 0, 1, 24.833, -20.826, 24.967, -30, 25.1, -30, 1, 25.222, -30, 25.344, -20, 25.467, 0, 1, 25.589, 20, 25.711, 30, 25.833, 30, 1, 25.956, 30, 26.078, 19.091, 26.2, 0, 1, 26.333, -20.826, 26.467, -30, 26.6, -30, 1, 26.722, -30, 26.844, -19.091, 26.967, 0, 1, 27.1, 20.826, 27.233, 30, 27.367, 30, 1, 27.511, 30, 27.656, 20.763, 27.8, 0, 1, 27.933, -19.166, 28.067, -30, 28.2, -30, 1, 28.322, -30, 28.444, -20, 28.567, 0, 1, 28.689, 20, 28.811, 30, 28.933, 30, 1, 29.056, 30, 29.178, 19.091, 29.3, 0, 1, 29.433, -20.826, 29.567, -30, 29.7, -30, 1, 29.822, -30, 29.944, -19.091, 30.067, 0, 1, 30.2, 20.826, 30.333, 30, 30.467, 30, 1, 30.611, 30, 30.756, 20.763, 30.9, 0, 1, 31.033, -19.166, 31.167, -30, 31.3, -30, 1, 31.422, -30, 31.544, -20, 31.667, 0, 1, 31.789, 20, 31.911, 30, 32.033, 30, 1, 32.156, 30, 32.278, 19.091, 32.4, 0, 1, 32.533, -20.826, 32.667, -30, 32.8, -30, 1, 32.922, -30, 33.044, -20, 33.167, 0, 1, 33.289, 20, 33.411, 30, 33.533, 30, 1, 33.656, 30, 33.778, 19.091, 33.9, 0, 1, 33.911, -1.736, 33.922, -3.391, 33.933, -4.966]}, {"Target": "Parameter", "Id": "HipAngleX2", "Segments": [0, -17.9, 1, 0.044, -19.32, 0.089, -20, 0.133, -20, 1, 0.256, -20, 0.378, -13.333, 0.5, 0, 1, 0.622, 13.333, 0.744, 20, 0.867, 20, 1, 0.989, 20, 1.111, 12.726, 1.233, 0, 1, 1.367, -13.883, 1.5, -20, 1.633, -20, 1, 1.756, -20, 1.878, -13.333, 2, 0, 1, 2.122, 13.333, 2.244, 20, 2.367, 20, 1, 2.489, 20, 2.611, 10.987, 2.733, 0, 1, 2.9, -14.983, 3.067, -20, 3.233, -20, 1, 3.356, -20, 3.478, -13.333, 3.6, 0, 1, 3.722, 13.333, 3.844, 20, 3.967, 20, 1, 4.089, 20, 4.211, 12.726, 4.333, 0, 1, 4.467, -13.883, 4.6, -20, 4.733, -20, 1, 4.856, -20, 4.978, -13.333, 5.1, 0, 1, 5.222, 13.333, 5.344, 20, 5.467, 20, 1, 5.589, 20, 5.711, 12.726, 5.833, 0, 1, 5.967, -13.883, 6.1, -20, 6.233, -20, 1, 6.389, -20, 6.544, -14.694, 6.7, 0, 1, 6.822, 11.545, 6.944, 20, 7.067, 20, 1, 7.189, 20, 7.311, 12.726, 7.433, 0, 1, 7.567, -13.883, 7.7, -20, 7.833, -20, 1, 7.956, -20, 8.078, -13.333, 8.2, 0, 1, 8.322, 13.333, 8.444, 20, 8.567, 20, 1, 8.689, 20, 8.811, 12.726, 8.933, 0, 1, 9.067, -13.883, 9.2, -20, 9.333, -20, 1, 9.456, -20, 9.578, -11.545, 9.7, 0, 1, 9.856, 14.694, 10.011, 20, 10.167, 20, 1, 10.289, 20, 10.411, 12.726, 10.533, 0, 1, 10.667, -13.883, 10.8, -20, 10.933, -20, 1, 11.056, -20, 11.178, -13.333, 11.3, 0, 1, 11.422, 13.333, 11.544, 20, 11.667, 20, 1, 11.789, 20, 11.911, 12.726, 12.033, 0, 1, 12.167, -13.883, 12.3, -20, 12.433, -20, 1, 12.556, -20, 12.678, -12.726, 12.8, 0, 1, 12.933, 13.883, 13.067, 20, 13.2, 20, 1, 13.344, 20, 13.489, 13.842, 13.633, 0, 1, 13.767, -12.777, 13.9, -20, 14.033, -20, 1, 14.156, -20, 14.278, -13.333, 14.4, 0, 1, 14.522, 13.333, 14.644, 20, 14.767, 20, 1, 14.889, 20, 15.011, 12.726, 15.133, 0, 1, 15.267, -13.883, 15.4, -20, 15.533, -20, 1, 15.656, -20, 15.778, -13.333, 15.9, 0, 1, 16.022, 13.333, 16.144, 20, 16.267, 20, 1, 16.4, 20, 16.533, 12.225, 16.667, 0, 1, 16.822, -14.263, 16.978, -20, 17.133, -20, 1, 17.256, -20, 17.378, -13.333, 17.5, 0, 1, 17.622, 13.333, 17.744, 20, 17.867, 20, 1, 17.989, 20, 18.111, 12.726, 18.233, 0, 1, 18.367, -13.883, 18.5, -20, 18.633, -20, 1, 18.756, -20, 18.878, -13.333, 19, 0, 1, 19.122, 13.333, 19.244, 20, 19.367, 20, 1, 19.489, 20, 19.611, 11.545, 19.733, 0, 1, 19.889, -14.694, 20.044, -20, 20.2, -20, 1, 20.333, -20, 20.467, -13.883, 20.6, 0, 1, 20.722, 12.726, 20.844, 20, 20.967, 20, 1, 21.089, 20, 21.211, 12.726, 21.333, 0, 1, 21.467, -13.883, 21.6, -20, 21.733, -20, 1, 21.856, -20, 21.978, -13.333, 22.1, 0, 1, 22.222, 13.333, 22.344, 20, 22.467, 20, 1, 22.589, 20, 22.711, 12.726, 22.833, 0, 1, 22.967, -13.883, 23.1, -20, 23.233, -20, 1, 23.378, -20, 23.522, -13.842, 23.667, 0, 1, 23.8, 12.777, 23.933, 20, 24.067, 20, 1, 24.189, 20, 24.311, 12.726, 24.433, 0, 1, 24.567, -13.883, 24.7, -20, 24.833, -20, 1, 24.956, -20, 25.078, -13.333, 25.2, 0, 1, 25.322, 13.333, 25.444, 20, 25.567, 20, 1, 25.689, 20, 25.811, 12.726, 25.933, 0, 1, 26.067, -13.883, 26.2, -20, 26.333, -20, 1, 26.467, -20, 26.6, -13.333, 26.733, 0, 1, 26.867, 13.333, 27, 20, 27.133, 20, 1, 27.267, 20, 27.4, 13.333, 27.533, 0, 1, 27.667, -13.333, 27.8, -20, 27.933, -20, 1, 28.056, -20, 28.178, -13.333, 28.3, 0, 1, 28.422, 13.333, 28.544, 20, 28.667, 20, 1, 28.789, 20, 28.911, 12.726, 29.033, 0, 1, 29.167, -13.883, 29.3, -20, 29.433, -20, 1, 29.556, -20, 29.678, -12.726, 29.8, 0, 1, 29.933, 13.883, 30.067, 20, 30.2, 20, 1, 30.344, 20, 30.489, 13.842, 30.633, 0, 1, 30.767, -12.777, 30.9, -20, 31.033, -20, 1, 31.156, -20, 31.278, -13.333, 31.4, 0, 1, 31.522, 13.333, 31.644, 20, 31.767, 20, 1, 31.889, 20, 32.011, 12.726, 32.133, 0, 1, 32.267, -13.883, 32.4, -20, 32.533, -20, 1, 32.656, -20, 32.778, -13.333, 32.9, 0, 1, 33.022, 13.333, 33.144, 20, 33.267, 20, 1, 33.4, 20, 33.533, 13.333, 33.667, 0, 1, 33.756, -8.889, 33.844, -14.815, 33.933, -17.778]}, {"Target": "Parameter", "Id": "HipAngleZ2", "Segments": [0, 0, 1, 0.133, -17.355, 0.267, -25, 0.4, -25, 1, 0.522, -25, 0.644, -16.667, 0.767, 0, 1, 0.889, 16.667, 1.011, 25, 1.133, 25, 1, 1.256, 25, 1.378, 15.909, 1.5, 0, 1, 1.633, -17.355, 1.767, -25, 1.9, -25, 1, 2.022, -25, 2.144, -16.667, 2.267, 0, 1, 2.389, 16.667, 2.511, 25, 2.633, 25, 1, 2.756, 25, 2.878, 13.739, 3, 0, 1, 3.167, -18.735, 3.333, -25, 3.5, -25, 1, 3.622, -25, 3.744, -16.667, 3.867, 0, 1, 3.989, 16.667, 4.111, 25, 4.233, 25, 1, 4.356, 25, 4.478, 15.909, 4.6, 0, 1, 4.733, -17.355, 4.867, -25, 5, -25, 1, 5.122, -25, 5.244, -16.667, 5.367, 0, 1, 5.489, 16.667, 5.611, 25, 5.733, 25, 1, 5.856, 25, 5.978, 15.909, 6.1, 0, 1, 6.233, -17.355, 6.367, -25, 6.5, -25, 1, 6.656, -25, 6.811, -18.371, 6.967, 0, 1, 7.089, 14.435, 7.211, 25, 7.333, 25, 1, 7.456, 25, 7.578, 15.909, 7.7, 0, 1, 7.833, -17.355, 7.967, -25, 8.1, -25, 1, 8.222, -25, 8.344, -16.667, 8.467, 0, 1, 8.589, 16.667, 8.711, 25, 8.833, 25, 1, 8.956, 25, 9.078, 15.909, 9.2, 0, 1, 9.333, -17.355, 9.467, -25, 9.6, -25, 1, 9.722, -25, 9.844, -14.435, 9.967, 0, 1, 10.122, 18.371, 10.278, 25, 10.433, 25, 1, 10.556, 25, 10.678, 15.909, 10.8, 0, 1, 10.933, -17.355, 11.067, -25, 11.2, -25, 1, 11.322, -25, 11.444, -16.667, 11.567, 0, 1, 11.689, 16.667, 11.811, 25, 11.933, 25, 1, 12.056, 25, 12.178, 15.909, 12.3, 0, 1, 12.433, -17.355, 12.567, -25, 12.7, -25, 1, 12.822, -25, 12.944, -15.909, 13.067, 0, 1, 13.2, 17.355, 13.333, 25, 13.467, 25, 1, 13.611, 25, 13.756, 17.302, 13.9, 0, 1, 14.033, -15.971, 14.167, -25, 14.3, -25, 1, 14.422, -25, 14.544, -16.667, 14.667, 0, 1, 14.789, 16.667, 14.911, 25, 15.033, 25, 1, 15.156, 25, 15.278, 15.909, 15.4, 0, 1, 15.533, -17.355, 15.667, -25, 15.8, -25, 1, 15.922, -25, 16.044, -16.667, 16.167, 0, 1, 16.289, 16.667, 16.411, 25, 16.533, 25, 1, 16.667, 25, 16.8, 15.283, 16.933, 0, 1, 17.089, -17.83, 17.244, -25, 17.4, -25, 1, 17.522, -25, 17.644, -16.667, 17.767, 0, 1, 17.889, 16.667, 18.011, 25, 18.133, 25, 1, 18.256, 25, 18.378, 15.909, 18.5, 0, 1, 18.633, -17.355, 18.767, -25, 18.9, -25, 1, 19.022, -25, 19.144, -16.667, 19.267, 0, 1, 19.389, 16.667, 19.511, 25, 19.633, 25, 1, 19.756, 25, 19.878, 14.435, 20, 0, 1, 20.156, -18.371, 20.311, -25, 20.467, -25, 1, 20.6, -25, 20.733, -17.355, 20.867, 0, 1, 20.989, 15.909, 21.111, 25, 21.233, 25, 1, 21.356, 25, 21.478, 15.909, 21.6, 0, 1, 21.733, -17.355, 21.867, -25, 22, -25, 1, 22.122, -25, 22.244, -16.667, 22.367, 0, 1, 22.489, 16.667, 22.611, 25, 22.733, 25, 1, 22.856, 25, 22.978, 15.909, 23.1, 0, 1, 23.233, -17.355, 23.367, -25, 23.5, -25, 1, 23.644, -25, 23.789, -17.302, 23.933, 0, 1, 24.067, 15.971, 24.2, 25, 24.333, 25, 1, 24.456, 25, 24.578, 15.909, 24.7, 0, 1, 24.833, -17.355, 24.967, -25, 25.1, -25, 1, 25.222, -25, 25.344, -16.667, 25.467, 0, 1, 25.589, 16.667, 25.711, 25, 25.833, 25, 1, 25.956, 25, 26.078, 15.909, 26.2, 0, 1, 26.333, -17.355, 26.467, -25, 26.6, -25, 1, 26.733, -25, 26.867, -16.667, 27, 0, 1, 27.133, 16.667, 27.267, 25, 27.4, 25, 1, 27.533, 25, 27.667, 16.667, 27.8, 0, 1, 27.933, -16.667, 28.067, -25, 28.2, -25, 1, 28.322, -25, 28.444, -16.667, 28.567, 0, 1, 28.689, 16.667, 28.811, 25, 28.933, 25, 1, 29.056, 25, 29.178, 15.909, 29.3, 0, 1, 29.433, -17.355, 29.567, -25, 29.7, -25, 1, 29.822, -25, 29.944, -15.909, 30.067, 0, 1, 30.2, 17.355, 30.333, 25, 30.467, 25, 1, 30.611, 25, 30.756, 17.302, 30.9, 0, 1, 31.033, -15.971, 31.167, -25, 31.3, -25, 1, 31.422, -25, 31.544, -16.667, 31.667, 0, 1, 31.789, 16.667, 31.911, 25, 32.033, 25, 1, 32.156, 25, 32.278, 15.909, 32.4, 0, 1, 32.533, -17.355, 32.667, -25, 32.8, -25, 1, 32.922, -25, 33.044, -16.667, 33.167, 0, 1, 33.289, 16.667, 33.411, 25, 33.533, 25, 1, 33.667, 25, 33.8, 16.667, 33.933, 0]}, {"Target": "Parameter", "Id": "Key14", "Segments": [0, 0, 0, 33.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 33.933, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 33.933, 1]}, {"Target": "Parameter", "Id": "Ani4", "Segments": [0, 0, 1, 1.244, 0, 2.489, 0.329, 3.733, 0.329, 1, 5, 0.329, 6.267, 1, 7.533, 1, 1, 8.522, 1, 9.511, 0.5, 10.5, 0.5, 1, 11.667, 0.5, 12.833, 0, 14, 0, 1, 15.256, 0, 16.511, 0.329, 17.767, 0.329, 1, 19.044, 0.329, 20.322, 1, 21.6, 1, 1, 22.589, 1, 23.578, 0.5, 24.567, 0.5, 1, 25.722, 0.5, 26.878, 0, 28.033, 0, 0, 33.933, 0]}, {"Target": "Parameter", "Id": "Ani6", "Segments": [0, 0, 0, 30.267, 1, 0, 33.933, 1]}, {"Target": "Parameter", "Id": "Ani1", "Segments": [0, 0, 1, 0.078, 0, 0.156, 1.845, 0.233, 5.537, 1, 0.311, 9.229, 0.389, 11.075, 0.467, 11.075, 1, 0.544, 11.075, 0.622, 5.876, 0.7, 5.596, 1, 0.822, 5.156, 0.944, 9.56, 1.067, 9.56, 1, 1.289, 9.56, 1.511, 8.452, 1.733, 8.452, 1, 1.922, 8.452, 2.111, 7.053, 2.3, 7.053, 1, 2.911, 7.053, 3.522, 40, 4.133, 40, 1, 4.278, 40, 4.422, 45, 4.567, 45, 1, 4.956, 45, 5.344, 45, 5.733, 45, 1, 5.767, 45, 5.8, 45, 5.833, 45, 1, 5.867, 45, 5.9, 45, 5.933, 45, 1, 5.944, 45, 5.956, 35.382, 5.967, 27.22, 1, 5.989, 10.896, 6.011, 6, 6.033, 6, 1, 6.189, 6, 6.344, 6.228, 6.5, 9, 1, 6.778, 13.95, 7.056, 18.595, 7.333, 18.595, 1, 7.889, 18.595, 8.444, 13.86, 9, 11.075, 1, 9.344, 11.075, 9.689, 9.56, 10.033, 9.56, 1, 10.311, 9.56, 10.589, 15.563, 10.867, 15.563, 1, 11.044, 15.563, 11.222, 8.452, 11.4, 8.452, 1, 11.722, 8.452, 12.044, 7.053, 12.367, 7.053, 1, 12.878, 7.053, 13.389, 22.966, 13.9, 22.966, 1, 14.689, 22.966, 15.478, 40, 16.267, 40, 1, 16.733, 40, 17.2, 45, 17.667, 45, 1, 17.911, 45, 18.156, 45, 18.4, 45, 1, 18.411, 45, 18.422, 0, 18.433, 0, 1, 18.511, 0, 18.589, 1.589, 18.667, 5.537, 1, 18.733, 8.921, 18.8, 11.075, 18.867, 11.075, 1, 18.922, 11.075, 18.978, 5.596, 19.033, 5.596, 1, 19.344, 5.596, 19.656, 9.56, 19.967, 9.56, 1, 20.233, 9.56, 20.5, 8.452, 20.767, 8.452, 1, 20.922, 8.452, 21.078, 7.053, 21.233, 7.053, 1, 21.756, 7.053, 22.278, 40, 22.8, 40, 1, 22.911, 40, 23.022, 45, 23.133, 45, 1, 23.467, 45, 23.8, 45, 24.133, 45, 1, 24.156, 45, 24.178, 45, 24.2, 45, 1, 24.233, 45, 24.267, 45, 24.3, 45, 1, 24.311, 45, 24.322, 35.382, 24.333, 27.22, 1, 24.356, 10.896, 24.378, 6, 24.4, 6, 1, 24.522, 6, 24.644, 6.298, 24.767, 9, 1, 25, 14.158, 25.233, 18.595, 25.467, 18.595, 1, 25.867, 18.595, 26.267, 16.117, 26.667, 11.075, 1, 26.744, 11.075, 26.822, 9.56, 26.9, 9.56, 1, 27.144, 9.56, 27.389, 15.563, 27.633, 15.563, 1, 27.778, 15.563, 27.922, 8.452, 28.067, 8.452, 1, 28.356, 8.452, 28.644, 7.053, 28.933, 7.053, 1, 29.344, 7.053, 29.756, 22.966, 30.167, 22.966, 1, 30.844, 22.966, 31.522, 40, 32.2, 40, 1, 32.589, 40, 32.978, 45, 33.367, 45, 1, 33.544, 45, 33.722, 45, 33.9, 45, 1, 33.911, 45, 33.922, 45, 33.933, 45]}, {"Target": "Parameter", "Id": "Ani2", "Segments": [0, 0, 1, 1.311, 0, 2.622, 0, 3.933, 0, 1, 4.478, 0, 5.022, 0.145, 5.567, 0.6, 1, 5.689, 0.702, 5.811, 1, 5.933, 1, 1, 5.956, 1, 5.978, 0, 6, 0, 1, 6.1, 0, 6.2, 0, 6.3, 0, 1, 9.822, 0, 13.344, 0, 16.867, 0, 1, 17.378, 0, 17.889, 1, 18.4, 1, 1, 18.411, 1, 18.422, 0, 18.433, 0, 1, 19.822, 0, 21.211, 0, 22.6, 0, 1, 23.067, 0, 23.533, 0.146, 24, 0.6, 1, 24.1, 0.697, 24.2, 1, 24.3, 1, 1, 24.322, 1, 24.344, 0, 24.367, 0, 1, 24.444, 0, 24.522, 0, 24.6, 0, 1, 27.311, 0, 30.022, 0, 32.733, 0, 1, 33.122, 0, 33.511, 1, 33.9, 1, 1, 33.911, 1, 33.922, 1, 33.933, 1]}, {"Target": "Parameter", "Id": "Ani3", "Segments": [0, 0, 0, 2.533, 0, 1, 3.356, 0, 4.178, 16.5, 5, 30, 1, 6.833, 30, 8.667, 30, 10.5, 30, 1, 11.478, 30, 12.456, 46.5, 13.433, 60, 0, 18.867, 60, 0, 22, 90, 0, 29.867, 90, 0, 33.633, 120, 0, 33.933, 120]}, {"Target": "Parameter", "Id": "Ani7", "Segments": [0, 10.07, 1, 0.133, 9.791, 0.267, 9.56, 0.4, 9.56, 1, 0.678, 9.56, 0.956, 15.563, 1.233, 15.563, 1, 1.411, 15.563, 1.589, 8.452, 1.767, 8.452, 1, 2.089, 8.452, 2.411, 7.053, 2.733, 7.053, 1, 3.233, 7.053, 3.733, 22.966, 4.233, 22.966, 1, 5.144, 22.966, 6.056, 40, 6.967, 40, 1, 7.433, 40, 7.9, 45, 8.367, 45, 1, 8.611, 45, 8.856, 45, 9.1, 45, 1, 9.133, 45, 9.167, 0, 9.2, 0, 1, 9.256, 0, 9.311, 2.213, 9.367, 5.537, 1, 9.433, 9.526, 9.5, 11.075, 9.567, 11.075, 1, 9.622, 11.075, 9.678, 5.596, 9.733, 5.596, 1, 9.844, 5.596, 9.956, 9.56, 10.067, 9.56, 1, 10.256, 9.56, 10.444, 8.452, 10.633, 8.452, 1, 10.789, 8.452, 10.944, 7.053, 11.1, 7.053, 1, 11.622, 7.053, 12.144, 40, 12.667, 40, 1, 12.778, 40, 12.889, 45, 13, 45, 1, 13.333, 45, 13.667, 45, 14, 45, 1, 14.022, 45, 14.044, 45, 14.067, 45, 1, 14.1, 45, 14.133, 45, 14.167, 45, 1, 14.178, 45, 14.189, 35.382, 14.2, 27.22, 1, 14.222, 10.896, 14.244, 6, 14.267, 6, 1, 14.389, 6, 14.511, 6.298, 14.633, 9, 1, 14.867, 14.158, 15.1, 18.595, 15.333, 18.595, 1, 15.7, 18.595, 16.067, 13.35, 16.433, 11.075, 1, 16.711, 11.075, 16.989, 9.56, 17.267, 9.56, 1, 17.511, 9.56, 17.756, 15.563, 18, 15.563, 1, 18.144, 15.563, 18.289, 8.452, 18.433, 8.452, 1, 18.722, 8.452, 19.011, 7.053, 19.3, 7.053, 1, 19.511, 7.053, 19.722, 22.966, 19.933, 22.966, 1, 20.611, 22.966, 21.289, 40, 21.967, 40, 1, 22.356, 40, 22.744, 45, 23.133, 45, 1, 23.144, 45, 23.156, 30.208, 23.167, 30, 1, 23.211, 29.129, 23.256, 17.359, 23.3, 15, 1, 23.467, 6.176, 23.633, 0, 23.8, 0, 1, 23.878, 0, 23.956, 1.845, 24.033, 5.537, 1, 24.111, 9.229, 24.189, 11.075, 24.267, 11.075, 1, 24.344, 11.075, 24.422, 5.876, 24.5, 5.596, 1, 24.622, 5.156, 24.744, 9.56, 24.867, 9.56, 1, 25.289, 9.56, 25.711, 8.452, 26.133, 8.452, 1, 26.322, 8.452, 26.511, 7.053, 26.7, 7.053, 1, 27.533, 7.053, 28.367, 40, 29.2, 40, 1, 29.378, 40, 29.556, 45, 29.733, 45, 1, 30.2, 45, 30.667, 45, 31.133, 45, 1, 31.167, 45, 31.2, 45, 31.233, 45, 1, 31.244, 45, 31.256, 45, 31.267, 45, 1, 31.278, 45, 31.289, 0, 31.3, 0, 1, 31.333, 0, 31.367, 27.22, 31.4, 27.22, 1, 31.422, 27.22, 31.444, 6, 31.467, 6, 1, 31.656, 6, 31.844, 6.213, 32.033, 9, 1, 32.367, 13.918, 32.7, 18.595, 33.033, 18.595, 1, 33.156, 18.595, 33.278, 11.792, 33.4, 11.075, 1, 33.578, 11.075, 33.756, 10.44, 33.933, 10.07]}, {"Target": "Parameter", "Id": "Ani8", "Segments": [0, 0, 1, 2.522, 0, 5.044, 0, 7.567, 0, 1, 8.078, 0, 8.589, 1, 9.1, 1, 1, 9.133, 1, 9.167, 1, 9.2, 1, 1, 9.211, 1, 9.222, 0, 9.233, 0, 1, 10.311, 0, 11.389, 0, 12.467, 0, 1, 12.944, 0, 13.422, 0.168, 13.9, 0.6, 1, 13.944, 0.64, 13.989, 1, 14.033, 1, 1, 14.078, 1, 14.122, 1, 14.167, 1, 1, 14.189, 1, 14.211, 0, 14.233, 0, 1, 14.311, 0, 14.389, 0, 14.467, 0, 1, 17.144, 0, 19.822, 0, 22.5, 0, 1, 22.711, 0, 22.922, 1, 23.133, 1, 1, 23.178, 1, 23.222, 1, 23.267, 1, 1, 23.278, 1, 23.289, 0, 23.3, 0, 1, 23.467, 0, 23.633, 0, 23.8, 0, 1, 25.511, 0, 27.222, 0, 28.933, 0, 1, 29.578, 0, 30.222, 0.145, 30.867, 0.6, 1, 31.011, 0.702, 31.156, 1, 31.3, 1, 1, 31.311, 1, 31.322, 0, 31.333, 0, 1, 31.467, 0, 31.6, 0, 31.733, 0, 1, 32.467, 0, 33.2, 0, 33.933, 0]}, {"Target": "Parameter", "Id": "Ani9", "Segments": [0, 0, 0, 1.233, 0, 0, 3.667, 30, 0, 7.333, 30, 0, 9.1, 60, 0, 15.833, 60, 0, 20.533, 90, 0, 29.867, 90, 0, 33.633, 120, 0, 33.933, 120]}, {"Target": "Parameter", "Id": "Ani10", "Segments": [0, 0, 1, 0.078, 0, 0.156, 1.845, 0.233, 5.537, 1, 0.311, 9.229, 0.389, 11.075, 0.467, 11.075, 1, 0.544, 11.075, 0.622, 5.876, 0.7, 5.596, 1, 0.822, 5.156, 0.944, 9.56, 1.067, 9.56, 1, 1.289, 9.56, 1.511, 8.452, 1.733, 8.452, 1, 1.922, 8.452, 2.111, 7.053, 2.3, 7.053, 1, 2.911, 7.053, 3.522, 40, 4.133, 40, 1, 4.278, 40, 4.422, 45, 4.567, 45, 1, 4.956, 45, 5.344, 45, 5.733, 45, 1, 5.767, 45, 5.8, 45, 5.833, 45, 1, 5.867, 45, 5.9, 45, 5.933, 45, 1, 5.944, 45, 5.956, 35.382, 5.967, 27.22, 1, 5.989, 10.896, 6.011, 6, 6.033, 6, 1, 6.189, 6, 6.344, 6.228, 6.5, 9, 1, 6.778, 13.95, 7.056, 18.595, 7.333, 18.595, 1, 7.889, 18.595, 8.444, 13.86, 9, 11.075, 1, 9.344, 11.075, 9.689, 9.56, 10.033, 9.56, 1, 10.311, 9.56, 10.589, 15.563, 10.867, 15.563, 1, 11.044, 15.563, 11.222, 8.452, 11.4, 8.452, 1, 11.667, 8.452, 11.933, 7.053, 12.2, 7.053, 1, 12.622, 7.053, 13.044, 22.966, 13.467, 22.966, 1, 14.267, 22.966, 15.067, 40, 15.867, 40, 1, 16.333, 40, 16.8, 45, 17.267, 45, 1, 17.511, 45, 17.756, 45, 18, 45, 1, 18.011, 45, 18.022, 0, 18.033, 0, 1, 18.111, 0, 18.189, 1.589, 18.267, 5.537, 1, 18.333, 8.921, 18.4, 11.075, 18.467, 11.075, 1, 18.522, 11.075, 18.578, 5.596, 18.633, 5.596, 1, 18.744, 5.596, 18.856, 9.56, 18.967, 9.56, 1, 19.211, 9.56, 19.456, 8.452, 19.7, 8.452, 1, 19.933, 8.452, 20.167, 7.053, 20.4, 7.053, 1, 20.922, 7.053, 21.444, 40, 21.967, 40, 1, 22.078, 40, 22.189, 45, 22.3, 45, 1, 22.633, 45, 22.967, 45, 23.3, 45, 1, 23.322, 45, 23.344, 45, 23.367, 45, 1, 23.4, 45, 23.433, 45, 23.467, 45, 1, 23.478, 45, 23.489, 35.382, 23.5, 27.22, 1, 23.522, 10.896, 23.544, 6, 23.567, 6, 1, 23.689, 6, 23.811, 6.298, 23.933, 9, 1, 24.167, 14.158, 24.4, 18.595, 24.633, 18.595, 1, 25.111, 18.595, 25.589, 14.024, 26.067, 11.075, 1, 26.344, 11.075, 26.622, 9.56, 26.9, 9.56, 1, 27.144, 9.56, 27.389, 15.563, 27.633, 15.563, 1, 27.778, 15.563, 27.922, 8.452, 28.067, 8.452, 1, 28.356, 8.452, 28.644, 7.053, 28.933, 7.053, 1, 29.344, 7.053, 29.756, 22.966, 30.167, 22.966, 1, 30.844, 22.966, 31.522, 40, 32.2, 40, 1, 32.589, 40, 32.978, 45, 33.367, 45, 1, 33.544, 45, 33.722, 45, 33.9, 45, 1, 33.911, 45, 33.922, 45, 33.933, 45]}, {"Target": "Parameter", "Id": "Ani11", "Segments": [0, 0, 1, 1.311, 0, 2.622, 0, 3.933, 0, 1, 4.322, 0, 4.711, 0.143, 5.1, 0.6, 1, 5.2, 0.718, 5.3, 1, 5.4, 1, 1, 5.6, 1, 5.8, 1, 6, 1, 1, 6.011, 1, 6.022, 0, 6.033, 0, 1, 6.144, 0, 6.256, 0, 6.367, 0, 1, 6.467, 0, 6.567, 0, 6.667, 0, 1, 9.933, 0, 13.2, 0, 16.467, 0, 1, 16.978, 0, 17.489, 1, 18, 1, 1, 18.011, 1, 18.022, 0, 18.033, 0, 1, 19.278, 0, 20.522, 0, 21.767, 0, 1, 22.233, 0, 22.7, 0.146, 23.167, 0.6, 1, 23.267, 0.697, 23.367, 1, 23.467, 1, 1, 23.489, 1, 23.511, 0, 23.533, 0, 1, 23.611, 0, 23.689, 0, 23.767, 0, 1, 25.911, 0, 28.056, 0, 30.2, 0, 1, 30.9, 0, 31.6, 1, 32.3, 1, 1, 32.322, 1, 32.344, 1, 32.367, 1, 0, 33.933, 1]}, {"Target": "Parameter", "Id": "Ani12", "Segments": [0, 0, 1, 0.6, 0, 1.2, 0, 1.8, 0, 1, 2.289, 0, 2.778, 30.1, 3.267, 30.1, 0, 5.467, 30.1, 0, 9.667, 60, 0, 20.8, 60, 0, 25.2, 90, 0, 33.933, 90]}, {"Target": "Parameter", "Id": "Ani13", "Segments": [0, 10.07, 1, 0.133, 9.791, 0.267, 9.56, 0.4, 9.56, 1, 0.678, 9.56, 0.956, 15.563, 1.233, 15.563, 1, 1.411, 15.563, 1.589, 8.452, 1.767, 8.452, 1, 2.089, 8.452, 2.411, 7.053, 2.733, 7.053, 1, 3.189, 7.053, 3.644, 22.966, 4.1, 22.966, 1, 4.489, 22.966, 4.878, 40, 5.267, 40, 1, 5.7, 40, 6.133, 45, 6.567, 45, 1, 6.811, 45, 7.056, 45, 7.3, 45, 1, 7.333, 45, 7.367, 0, 7.4, 0, 1, 7.456, 0, 7.511, 2.213, 7.567, 5.537, 1, 7.633, 9.526, 7.7, 11.075, 7.767, 11.075, 1, 7.822, 11.075, 7.878, 5.596, 7.933, 5.596, 1, 8.044, 5.596, 8.156, 9.56, 8.267, 9.56, 1, 8.456, 9.56, 8.644, 8.452, 8.833, 8.452, 1, 9.022, 8.452, 9.211, 7.053, 9.4, 7.053, 1, 10.378, 7.053, 11.356, 40, 12.333, 40, 1, 12.444, 40, 12.556, 45, 12.667, 45, 1, 13, 45, 13.333, 45, 13.667, 45, 1, 13.689, 45, 13.711, 45, 13.733, 45, 1, 13.767, 45, 13.8, 45, 13.833, 45, 1, 13.856, 45, 13.878, 41.874, 13.9, 27.22, 1, 13.911, 19.893, 13.922, 6, 13.933, 6, 1, 14.056, 6, 14.178, 6.298, 14.3, 9, 1, 14.533, 14.158, 14.767, 18.595, 15, 18.595, 1, 15.478, 18.595, 15.956, 14.024, 16.433, 11.075, 1, 16.711, 11.075, 16.989, 9.56, 17.267, 9.56, 1, 17.511, 9.56, 17.756, 15.563, 18, 15.563, 1, 18.144, 15.563, 18.289, 8.452, 18.433, 8.452, 1, 18.722, 8.452, 19.011, 7.053, 19.3, 7.053, 1, 19.711, 7.053, 20.122, 22.966, 20.533, 22.966, 1, 21.211, 22.966, 21.889, 40, 22.567, 40, 1, 22.956, 40, 23.344, 45, 23.733, 45, 1, 23.744, 45, 23.756, 30.208, 23.767, 30, 1, 23.811, 29.129, 23.856, 17.359, 23.9, 15, 1, 24.067, 6.176, 24.233, 0, 24.4, 0, 1, 24.478, 0, 24.556, 1.845, 24.633, 5.537, 1, 24.711, 9.229, 24.789, 11.075, 24.867, 11.075, 1, 24.944, 11.075, 25.022, 5.876, 25.1, 5.596, 1, 25.222, 5.156, 25.344, 9.56, 25.467, 9.56, 1, 25.689, 9.56, 25.911, 8.452, 26.133, 8.452, 1, 26.322, 8.452, 26.511, 7.053, 26.7, 7.053, 1, 27.311, 7.053, 27.922, 40, 28.533, 40, 1, 28.678, 40, 28.822, 45, 28.967, 45, 1, 29.356, 45, 29.744, 45, 30.133, 45, 1, 30.167, 45, 30.2, 45, 30.233, 45, 1, 30.267, 45, 30.3, 45, 30.333, 45, 1, 30.344, 45, 30.356, 35.382, 30.367, 27.22, 1, 30.389, 10.896, 30.411, 6, 30.433, 6, 1, 30.589, 6, 30.744, 6.228, 30.9, 9, 1, 31.178, 13.95, 31.456, 18.595, 31.733, 18.595, 1, 32.289, 18.595, 32.844, 14.813, 33.4, 11.075, 1, 33.578, 11.075, 33.756, 10.44, 33.933, 10.07]}, {"Target": "Parameter", "Id": "Ani14", "Segments": [0, 0, 1, 1.922, 0, 3.844, 0, 5.767, 0, 1, 6.278, 0, 6.789, 1, 7.3, 1, 1, 7.333, 1, 7.367, 1, 7.4, 1, 1, 7.411, 1, 7.422, 0, 7.433, 0, 1, 9, 0, 10.567, 0, 12.133, 0, 1, 12.644, 0, 13.156, 0.155, 13.667, 0.6, 1, 13.744, 0.668, 13.822, 1, 13.9, 1, 1, 13.978, 1, 14.056, 0, 14.133, 0, 1, 17.122, 0, 20.111, 0, 23.1, 0, 1, 23.311, 0, 23.522, 1, 23.733, 1, 1, 23.778, 1, 23.822, 1, 23.867, 1, 1, 23.878, 1, 23.889, 0, 23.9, 0, 1, 24.067, 0, 24.233, 0, 24.4, 0, 1, 25.711, 0, 27.022, 0, 28.333, 0, 1, 28.878, 0, 29.422, 0.252, 29.967, 0.6, 1, 30.444, 0.906, 30.922, 1, 31.4, 1, 1, 31.422, 1, 31.444, 0.001, 31.467, 0.001, 1, 31.567, 0.001, 31.667, 0.001, 31.767, 0.001, 1, 32.489, 0.001, 33.211, 0, 33.933, 0]}, {"Target": "Parameter", "Id": "Ani15", "Segments": [0, 0, 1, 2.511, 0, 5.022, 0, 7.533, 0, 1, 9.067, 0, 10.6, 30.1, 12.133, 30.1, 1, 13.133, 30.1, 14.133, 30.1, 15.133, 30.1, 1, 15.878, 30.1, 16.622, 60, 17.367, 60, 1, 20.311, 60, 23.256, 60, 26.2, 60, 0, 29.533, 90, 0, 33.933, 90]}]}