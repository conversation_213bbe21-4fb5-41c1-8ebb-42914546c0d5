# -*- coding: utf-8 -*-
"""
推送消息管理器模块
用于管理WebSocket推送的VCP日志和Agent消息
"""

import logging
import uuid
import re
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class PushMessageManager:
    """推送消息管理器"""
    
    def __init__(self, max_messages: int = 50):
        """初始化推送消息管理器"""
        self.messages: List[Dict[str, Any]] = []
        self.max_messages = max_messages
        self.auto_cleanup = True
        
        logger.info(f"推送消息管理器初始化，最大消息数量: {max_messages}")
    
    def add_message(self, msg_type: str, content: str, status: str = "info", 
                   source: str = "unknown", extra_data: Optional[Dict] = None) -> Dict[str, Any]:
        """添加推送消息"""
        message = {
            'id': str(uuid.uuid4()),
            'type': msg_type,
            'content': content,
            'status': status,
            'source': source,
            'timestamp': datetime.now(),
            'extra_data': extra_data or {}
        }
        
        # 新消息插入到列表开头（最新在前）
        self.messages.insert(0, message)
        
        # 自动清理旧消息
        if self.auto_cleanup and len(self.messages) > self.max_messages:
            removed_count = len(self.messages) - self.max_messages
            self.messages = self.messages[:self.max_messages]
            logger.debug(f"自动清理了 {removed_count} 条旧消息")
        
        logger.debug(f"添加推送消息: {msg_type} - {content[:50]}...")
        return message
    
    def get_message_count(self) -> int:
        """获取消息总数"""
        return len(self.messages)
    
    def get_latest_message(self) -> Optional[Dict[str, Any]]:
        """获取最新消息"""
        return self.messages[0] if self.messages else None
    
    def get_preview_text(self, max_length: int = 30) -> str:
        """获取最新消息的预览文本"""
        if not self.messages:
            return "暂无推送消息"
        
        latest = self.messages[0]
        preview_content = latest['content'].replace('\n', ' ').strip()
        
        if len(preview_content) > max_length:
            preview_content = preview_content[:max_length] + "..."
        
        # 根据消息类型添加前缀
        type_prefix = self._get_type_prefix(latest['type'])
        return f"{type_prefix}{preview_content}"
    
    def get_messages(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取消息列表"""
        if limit:
            return self.messages[:limit]
        return self.messages.copy()
    
    def clear_all(self) -> int:
        """清空所有消息"""
        count = len(self.messages)
        self.messages.clear()
        logger.info(f"清空了 {count} 条推送消息")
        return count
    
    def remove_message(self, message_id: str) -> bool:
        """移除指定ID的消息"""
        for i, msg in enumerate(self.messages):
            if msg['id'] == message_id:
                removed_msg = self.messages.pop(i)
                logger.debug(f"移除消息: {removed_msg['type']} - {removed_msg['content'][:30]}...")
                return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取消息统计信息"""
        if not self.messages:
            return {
                'total': 0,
                'by_type': {},
                'by_status': {},
                'latest_timestamp': None
            }
        
        # 按类型和状态统计
        type_stats = {}
        status_stats = {}
        
        for msg in self.messages:
            msg_type = msg['type']
            msg_status = msg['status']
            
            type_stats[msg_type] = type_stats.get(msg_type, 0) + 1
            status_stats[msg_status] = status_stats.get(msg_status, 0) + 1
        
        return {
            'total': len(self.messages),
            'by_type': type_stats,
            'by_status': status_stats,
            'latest_timestamp': self.messages[0]['timestamp']
        }
    
    def _get_type_prefix(self, msg_type: str) -> str:
        """根据消息类型获取显示前缀"""
        prefix_map = {
            'VCP工具': '🛠️ ',
            'Agent通知': '📢 ',
            'Agent消息': '🤖 ',
            '系统消息': '⚙️ ',
            '错误消息': '❌ ',
            '警告消息': '⚠️ '
        }
        return prefix_map.get(msg_type, '📝 ')
    
    def set_max_messages(self, max_messages: int):
        """设置最大消息数量"""
        old_max = self.max_messages
        self.max_messages = max_messages
        
        # 如果新限制更小，立即清理
        if len(self.messages) > max_messages:
            removed_count = len(self.messages) - max_messages
            self.messages = self.messages[:max_messages]
            logger.info(f"消息数量限制从 {old_max} 调整为 {max_messages}，清理了 {removed_count} 条消息")
        
        logger.info(f"最大消息数量已更新为: {max_messages}")


class MessageFormatter:
    """消息格式化工具类"""
    
    @staticmethod
    def format_vcp_message(message_data: Dict[str, Any]) -> Dict[str, str]:
        """格式化VCP日志消息"""
        data = message_data.get('data', {})
        tool_name = data.get('tool_name', 'Unknown Tool')
        status = data.get('status', 'unknown')
        content = data.get('content', '')
        source = data.get('source', 'unknown')
        
        # 状态映射
        status_map = {
            'success': 'success',
            'error': 'error',
            'running': 'info',
            'pending': 'info'
        }
        
        mapped_status = status_map.get(status.lower(), 'info')
        
        # 简化内容显示 - 清理空白行
        display_content = MessageFormatter._clean_content(content)
        
        return {
            'type': 'VCP工具',
            'content': f"{tool_name} [{status.upper()}]\n{display_content}",
            'status': mapped_status,
            'source': source,
            'extra_data': {
                'tool_name': tool_name,
                'original_status': status
            }
        }
    
    @staticmethod
    def format_agent_message(message_data: Dict[str, Any]) -> Dict[str, str]:
        """格式化Agent消息 - 优化版本，提取真正的消息内容"""
        try:
            # 尝试从多个可能的字段提取消息
            message_content = message_data.get('message', '')
            original_content = message_data.get('originalContent', '')
            recipient = message_data.get('recipient', 'AI助手')
            
            # 优先使用message字段，然后是originalContent
            raw_content = message_content or original_content or str(message_data)
            
            # 提取真正的消息内容
            actual_message = MessageFormatter._extract_agent_message(raw_content)
            
            # 如果提取失败，尝试从JSON中解析
            if not actual_message or actual_message == raw_content:
                actual_message = MessageFormatter._extract_from_json(raw_content)
            
            # 清理消息内容
            clean_message = MessageFormatter._clean_content(actual_message)
            
            # 如果消息太长，只显示关键部分
            if len(clean_message) > 200:
                clean_message = clean_message[:200] + "..."
            
            return {
                'type': 'Agent消息',
                'content': clean_message,
                'status': 'info',
                'source': 'agent_message',
                'extra_data': {
                    'recipient': recipient,
                    'original_length': len(raw_content)
                }
            }
            
        except Exception as e:
            logger.error(f"格式化Agent消息时出错: {e}")
            return {
                'type': 'Agent消息',
                'content': "消息格式化失败",
                'status': 'error',
                'source': 'agent_message',
                'extra_data': {'error': str(e)}
            }
    
    @staticmethod
    def _extract_agent_message(content: str) -> str:
        """从内容中提取Agent消息的核心部分"""
        if not content:
            return ""
        
        # 方法1：尝试提取「始」...「末」之间的内容
        begin_marker = "「始」"
        end_marker = "「末」"
        if begin_marker in content and end_marker in content:
            start_pos = content.find(begin_marker) + len(begin_marker)
            end_pos = content.find(end_marker, start_pos)
            if end_pos > start_pos:
                extracted = content[start_pos:end_pos].strip()
                if extracted:
                    return extracted
        
        # 方法2：尝试提取时间戳后的内容
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            # 跳过时间戳行（格式：2025-06-02 00:10:26）
            if re.match(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', line):
                continue
            # 跳过空行和特殊标记
            if not line or line.startswith('{') or line.startswith('[') or line.startswith('<<<'):
                continue
            # 找到实际消息内容
            if len(line) > 3 and not line.startswith('tool_name') and not line.startswith('message'):
                return line
        
        # 方法3：尝试从JSON结构中提取
        try:
            if content.strip().startswith('{'):
                data = json.loads(content)
                if isinstance(data, dict):
                    # 尝试多个可能的字段
                    for field in ['message', 'content', 'text', 'data']:
                        if field in data and data[field]:
                            return str(data[field]).strip()
        except:
            pass
        
        # 方法4：清理并返回内容的前几行（排除明显的技术信息）
        clean_lines = []
        for line in lines[:5]:  # 只检查前5行
            line = line.strip()
            if (line and 
                not line.startswith('{') and 
                not line.startswith('[') and
                not line.startswith('<<<') and
                not re.match(r'\d{4}-\d{2}-\d{2}', line) and
                'tool_name' not in line and
                'SUCCESS' not in line and
                'original_plugin_output' not in line):
                clean_lines.append(line)
        
        if clean_lines:
            return ' '.join(clean_lines)
        
        return content[:100] + "..." if len(content) > 100 else content
    
    @staticmethod
    def _extract_from_json(content: str) -> str:
        """从复杂的JSON结构中提取消息"""
        try:
            # 尝试解析JSON
            if 'original_plugin_output' in content:
                # 提取JSON部分
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_content = content[json_start:json_end]
                    data = json.loads(json_content)
                    
                    # 从嵌套结构中提取消息
                    if 'original_plugin_output' in data:
                        plugin_output = data['original_plugin_output']
                        if isinstance(plugin_output, dict) and 'message' in plugin_output:
                            message = plugin_output['message']
                            # 提取时间戳后的实际消息
                            if '\n' in message:
                                lines = message.split('\n', 1)
                                if len(lines) > 1:
                                    actual_msg = lines[1].strip()
                                    if actual_msg:
                                        return actual_msg
                            return message.strip()
        except Exception as e:
            logger.debug(f"JSON解析失败: {e}")
        
        return content
    
    @staticmethod
    def _clean_content(content: str) -> str:
        """清理消息内容，移除多余的空白行和格式化字符"""
        if not content:
            return ""
        
        # 移除多余的空白字符
        content = content.strip()
        
        # 替换多个连续的换行符为单个换行符
        content = re.sub(r'\n\s*\n', '\n', content)
        
        # 移除行首行尾的空白
        lines = [line.strip() for line in content.split('\n')]
        
        # 过滤空行
        lines = [line for line in lines if line]
        
        # 重新组合
        cleaned = '\n'.join(lines)
        
        return cleaned