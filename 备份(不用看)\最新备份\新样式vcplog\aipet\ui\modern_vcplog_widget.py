#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化 VCPLog 通知组件
基于 VCPChat 设计的现代化通知栏实现
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QScrollArea, QSizePolicy, QTextEdit, QApplication,
    QGraphicsOpacityEffect, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, 
    QRect, QParallelAnimationGroup, QSequentialAnimationGroup
)
from PyQt5.QtGui import QFont, QPalette, QColor, QLinearGradient, QBrush

logger = logging.getLogger(__name__)

class ModernVCPLogItem(QFrame):
    """现代化 VCPLog 通知项"""
    
    # 信号定义
    copy_requested = pyqtSignal(str)  # 复制请求
    remove_requested = pyqtSignal(str)  # 删除请求
    
    def __init__(self, log_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.log_data = log_data
        self.original_raw_message = log_data.get('original_raw_message', '')
        self.is_expanded = False
        self.animation_group = None
        
        # 解析数据
        self._parse_log_data()
        
        # 设置基本属性
        self.setFrameStyle(QFrame.NoFrame)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.setMinimumHeight(60)
        self.setMaximumHeight(60)  # 初始折叠状态
        
        # 初始化UI
        self._init_ui()
        self._apply_modern_styles()
        self._setup_animations()
        
        logger.debug(f"创建现代化VCPLog项: {self.title_text}")
    
    def _parse_log_data(self):
        """解析日志数据 - 基于 VCPChat 的解析逻辑"""
        self.title_text = 'VCP 通知:'
        self.main_content = ''
        self.content_is_preformatted = False
        self.timestamp = datetime.now()
        
        # 获取原始消息用于复制
        self.text_to_copy = self.original_raw_message or json.dumps(self.log_data, ensure_ascii=False, indent=2)
        
        # 解析逻辑 - 参考 VCPChat notificationRenderer.js
        if (self.log_data and isinstance(self.log_data, dict) and 
            self.log_data.get('type') == 'vcp_log' and 
            isinstance(self.log_data.get('data'), dict)):
            
            vcp_data = self.log_data['data']
            if vcp_data.get('tool_name') and vcp_data.get('status'):
                self.title_text = f"{vcp_data['tool_name']} {vcp_data['status']}"
                
                if 'content' in vcp_data:
                    raw_content = str(vcp_data['content'])
                    self.main_content = raw_content
                    self.content_is_preformatted = True
                    
                    # 尝试解析内容中的JSON
                    try:
                        parsed_content = json.loads(raw_content)
                        title_suffix = ''
                        
                        if parsed_content.get('MaidName'):
                            title_suffix += f" by {parsed_content['MaidName']}"
                        
                        if (parsed_content.get('timestamp') and 
                            isinstance(parsed_content['timestamp'], str) and 
                            len(parsed_content['timestamp']) >= 16):
                            time_part = parsed_content['timestamp'][11:16]
                            title_suffix += f"{' ' if parsed_content.get('MaidName') else ''}@ {time_part}"
                        
                        if title_suffix:
                            self.title_text += f" ({title_suffix.strip()})"
                        
                        if 'original_plugin_output' in parsed_content:
                            if isinstance(parsed_content['original_plugin_output'], dict):
                                self.main_content = json.dumps(parsed_content['original_plugin_output'], ensure_ascii=False, indent=2)
                            else:
                                self.main_content = str(parsed_content['original_plugin_output'])
                                self.content_is_preformatted = False
                                
                    except (json.JSONDecodeError, KeyError):
                        pass  # 保持原始内容
                else:
                    self.main_content = '(无内容)'
            else:
                self.title_text = 'VCP 日志条目:'
                self.main_content = json.dumps(vcp_data, ensure_ascii=False, indent=2)
                self.content_is_preformatted = True
        
        elif (self.log_data and isinstance(self.log_data, dict) and 
              self.log_data.get('type') == 'daily_note_created' and 
              isinstance(self.log_data.get('data'), dict)):
            
            note_data = self.log_data['data']
            self.title_text = f"日记: {note_data.get('maidName', 'N/A')} ({note_data.get('dateString', 'N/A')})"
            
            if note_data.get('status') == 'success':
                self.main_content = note_data.get('message', '日记已成功创建。')
            else:
                self.main_content = note_data.get('message', f"日记处理状态: {note_data.get('status', '未知')}")
        
        else:
            # 通用处理
            self.title_text = 'VCP 消息:'
            if isinstance(self.log_data, dict):
                self.main_content = json.dumps(self.log_data, ensure_ascii=False, indent=2)
                self.content_is_preformatted = True
            else:
                self.main_content = str(self.log_data)
        
        # 生成预览文本
        preview = self.main_content.replace('\n', ' ').replace('\r', ' ')
        preview = ' '.join(preview.split())  # 移除多余空格
        self.preview_text = preview[:80] + "..." if len(preview) > 80 else preview
    
    def _init_ui(self):
        """初始化UI布局"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(12, 8, 12, 8)
        self.main_layout.setSpacing(6)
        
        # 头部区域（折叠状态）
        self._create_header()
        
        # 内容区域（展开状态）
        self._create_content()
        
        # 初始隐藏内容
        self.content_widget.hide()
    
    def _create_header(self):
        """创建头部区域"""
        self.header_widget = QWidget()
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(8)
        
        # 状态指示器
        self.status_indicator = QLabel("🔔")
        self.status_indicator.setFixedSize(20, 20)
        self.status_indicator.setAlignment(Qt.AlignCenter)
        
        # 标题
        self.title_label = QLabel(self.title_text)
        self.title_label.setFont(QFont("微软雅黑", 10, QFont.Bold))
        self.title_label.setStyleSheet("color: #2196F3;")
        
        # 时间戳
        time_str = self.timestamp.strftime("%H:%M:%S")
        self.time_label = QLabel(time_str)
        self.time_label.setFont(QFont("Consolas", 8))
        self.time_label.setStyleSheet("color: #666666;")
        
        # 预览内容
        self.preview_label = QLabel(self.preview_text)
        self.preview_label.setFont(QFont("微软雅黑", 9))
        self.preview_label.setWordWrap(True)
        self.preview_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 展开按钮
        self.toggle_btn = QPushButton("▼")
        self.toggle_btn.setFixedSize(24, 24)
        self.toggle_btn.clicked.connect(self.toggle_expanded)
        
        # 复制按钮
        self.copy_btn = QPushButton("📋")
        self.copy_btn.setFixedSize(24, 24)
        self.copy_btn.setToolTip("复制到剪贴板")
        self.copy_btn.clicked.connect(self._copy_content)
        
        # 添加到布局
        header_layout.addWidget(self.status_indicator)
        header_layout.addWidget(self.title_label)
        header_layout.addWidget(self.time_label)
        header_layout.addWidget(self.preview_label, 1)
        header_layout.addWidget(self.copy_btn)
        header_layout.addWidget(self.toggle_btn)
        
        self.main_layout.addWidget(self.header_widget)
    
    def _create_content(self):
        """创建内容区域"""
        self.content_widget = QWidget()
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setContentsMargins(0, 8, 0, 0)
        content_layout.setSpacing(8)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("color: #e0e0e0;")
        content_layout.addWidget(separator)
        
        # 完整内容
        self.content_text = QTextEdit()
        self.content_text.setPlainText(self.main_content)
        self.content_text.setReadOnly(True)
        self.content_text.setFont(QFont("Consolas" if self.content_is_preformatted else "微软雅黑", 9))
        self.content_text.setMaximumHeight(200)
        self.content_text.setLineWrapMode(QTextEdit.WidgetWidth)
        
        # 操作按钮
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.addStretch()
        
        remove_btn = QPushButton("删除")
        remove_btn.setFixedSize(60, 28)
        remove_btn.clicked.connect(self._remove_item)
        
        button_layout.addWidget(remove_btn)
        
        content_layout.addWidget(self.content_text)
        content_layout.addWidget(button_widget)
        
        self.main_layout.addWidget(self.content_widget)
    
    def _apply_modern_styles(self):
        """应用现代化样式"""
        # 基础样式 - 参考 VCPChat 的渐变背景
        self.setStyleSheet("""
            ModernVCPLogItem {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:0.4 #f8f9fa, 
                    stop:0.5 #e3f2fd, stop:0.6 #f8f9fa, stop:1 #f8f9fa);
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin: 2px;
            }
            ModernVCPLogItem:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e8f5e8, stop:0.4 #e8f5e8, 
                    stop:0.5 #c8e6c9, stop:0.6 #e8f5e8, stop:1 #e8f5e8);
                border-color: #4caf50;
            }
            QPushButton {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #2196F3;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
            QTextEdit {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
    
    def _setup_animations(self):
        """设置动画效果"""
        # 展开/折叠动画
        self.expand_animation = QPropertyAnimation(self, b"maximumHeight")
        self.expand_animation.setDuration(300)
        self.expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 透明度动画
        self.opacity_effect = QGraphicsOpacityEffect()
        self.content_widget.setGraphicsEffect(self.opacity_effect)
        
        self.opacity_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.opacity_animation.setDuration(200)
    
    def toggle_expanded(self):
        """切换展开/折叠状态"""
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()
    
    def expand(self):
        """展开项目"""
        if self.is_expanded:
            return
        
        self.is_expanded = True
        self.toggle_btn.setText("▲")
        
        # 显示内容
        self.content_widget.show()
        
        # 计算展开后的高度
        self.content_widget.adjustSize()
        expanded_height = self.header_widget.height() + self.content_widget.height() + 20
        
        # 执行展开动画
        self.expand_animation.setStartValue(60)
        self.expand_animation.setEndValue(min(expanded_height, 300))
        self.expand_animation.start()
        
        # 内容淡入
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        logger.debug(f"展开VCPLog项: {self.title_text}")
    
    def collapse(self):
        """折叠项目"""
        if not self.is_expanded:
            return
        
        self.is_expanded = False
        self.toggle_btn.setText("▼")
        
        # 内容淡出
        self.opacity_animation.setStartValue(1.0)
        self.opacity_animation.setEndValue(0.0)
        self.opacity_animation.finished.connect(lambda: self.content_widget.hide())
        self.opacity_animation.start()
        
        # 执行折叠动画
        self.expand_animation.setStartValue(self.height())
        self.expand_animation.setEndValue(60)
        self.expand_animation.start()
        
        logger.debug(f"折叠VCPLog项: {self.title_text}")
    
    def _copy_content(self):
        """复制内容到剪贴板"""
        QApplication.clipboard().setText(self.text_to_copy)
        
        # 视觉反馈
        original_text = self.copy_btn.text()
        self.copy_btn.setText("✓")
        self.copy_btn.setStyleSheet("background-color: #4caf50; color: white;")
        
        QTimer.singleShot(1000, lambda: (
            self.copy_btn.setText(original_text),
            self.copy_btn.setStyleSheet("")
        ))
        
        logger.info("VCPLog内容已复制到剪贴板")
    
    def _remove_item(self):
        """删除项目"""
        item_id = self.log_data.get('id', str(id(self)))
        self.remove_requested.emit(item_id)
    
    def update_theme(self, is_dark_theme: bool):
        """更新主题"""
        if is_dark_theme:
            self.setStyleSheet("""
                ModernVCPLogItem {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #2d3142, stop:0.4 #2d3142, 
                        stop:0.5 #3d4252, stop:0.6 #2d3142, stop:1 #2d3142);
                    border: 1px solid #4f5666;
                    border-radius: 8px;
                    color: #ffffff;
                }
                ModernVCPLogItem:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3d4252, stop:0.4 #3d4252, 
                        stop:0.5 #4d5262, stop:0.6 #3d4252, stop:1 #3d4252);
                    border-color: #64b5f6;
                }
                QPushButton {
                    background-color: #3d4252;
                    border: 1px solid #4f5666;
                    color: #ffffff;
                }
                QPushButton:hover {
                    background-color: #4d5262;
                    border-color: #64b5f6;
                }
                QTextEdit {
                    background-color: #2d3142;
                    border: 1px solid #4f5666;
                    color: #ffffff;
                }
            """)
            self.title_label.setStyleSheet("color: #64b5f6;")
            self.time_label.setStyleSheet("color: #aaaaaa;")
        else:
            self._apply_modern_styles()
            self.title_label.setStyleSheet("color: #2196F3;")
            self.time_label.setStyleSheet("color: #666666;")


class ModernVCPLogWidget(QFrame):
    """现代化 VCPLog 通知栏组件"""

    # 信号定义
    status_changed = pyqtSignal(str)  # 状态变化信号
    item_count_changed = pyqtSignal(int)  # 项目数量变化信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_items = []  # 存储日志项
        self.max_items = 100  # 最大项目数
        self.is_dark_theme = False
        self.connection_status = "未连接"

        # 设置基本属性
        self.setFrameStyle(QFrame.NoFrame)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumWidth(300)
        self.setMaximumWidth(600)
        self.setMinimumHeight(150)  # 确保组件有足够的最小高度

        # 初始化UI
        self._init_ui()
        self._apply_modern_styles()

        logger.info("现代化VCPLog通知栏初始化完成")

    def _init_ui(self):
        """初始化UI布局"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # 创建头部
        self._create_header()

        # 创建状态栏
        self._create_status_bar()

        # 创建内容区域
        self._create_content_area()

        # 创建底部操作栏
        self._create_action_bar()

    def _create_header(self):
        """创建头部区域"""
        self.header_widget = QWidget()
        self.header_widget.setObjectName("vcplog_header")
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(15, 10, 15, 10)
        header_layout.setSpacing(10)

        # 标题
        title_label = QLabel("🔔 VCP 通知")
        title_label.setFont(QFont("微软雅黑", 12, QFont.Bold))

        # 时间显示
        self.time_label = QLabel()
        self.time_label.setFont(QFont("Consolas", 10))
        self._update_time_display()

        # 定时更新时间
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self._update_time_display)
        self.time_timer.start(1000)  # 每秒更新

        # 操作按钮
        self.clear_btn = QPushButton("清空")
        self.clear_btn.setFixedSize(50, 28)
        self.clear_btn.clicked.connect(self.clear_all_items)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.time_label)
        header_layout.addWidget(self.clear_btn)

        self.main_layout.addWidget(self.header_widget)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_widget = QWidget()
        self.status_widget.setObjectName("vcplog_status")
        status_layout = QHBoxLayout(self.status_widget)
        status_layout.setContentsMargins(15, 8, 15, 8)

        self.status_label = QLabel(f"VCPLog: {self.connection_status}")
        self.status_label.setFont(QFont("微软雅黑", 9))
        self.status_label.setAlignment(Qt.AlignCenter)

        status_layout.addWidget(self.status_label)
        self.main_layout.addWidget(self.status_widget)

    def _create_content_area(self):
        """创建内容区域"""
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setFrameStyle(QFrame.NoFrame)

        # 内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(8, 8, 8, 8)
        self.content_layout.setSpacing(4)

        # 添加空状态提示
        self.empty_state_label = QLabel("暂无VCP通知\n\n当有新的工具调用时，通知将显示在这里")
        self.empty_state_label.setAlignment(Qt.AlignCenter)
        self.empty_state_label.setStyleSheet("""
            QLabel {
                color: #999999;
                font-size: 12px;
                padding: 40px;
                background-color: transparent;
            }
        """)
        self.content_layout.addWidget(self.empty_state_label)

        # 添加弹性空间
        self.content_layout.addStretch()

        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area, 1)

    def _create_action_bar(self):
        """创建底部操作栏"""
        self.action_widget = QWidget()
        self.action_widget.setObjectName("vcplog_actions")
        action_layout = QHBoxLayout(self.action_widget)
        action_layout.setContentsMargins(15, 8, 15, 8)
        action_layout.setSpacing(8)

        # 统计信息
        self.stats_label = QLabel("总计: 0 条")
        self.stats_label.setFont(QFont("Consolas", 9))

        # 操作按钮
        self.expand_all_btn = QPushButton("全部展开")
        self.expand_all_btn.setFixedSize(70, 28)
        self.expand_all_btn.clicked.connect(self.expand_all_items)

        self.collapse_all_btn = QPushButton("全部折叠")
        self.collapse_all_btn.setFixedSize(70, 28)
        self.collapse_all_btn.clicked.connect(self.collapse_all_items)

        action_layout.addWidget(self.stats_label)
        action_layout.addStretch()
        action_layout.addWidget(self.expand_all_btn)
        action_layout.addWidget(self.collapse_all_btn)

        self.main_layout.addWidget(self.action_widget)

    def _apply_modern_styles(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            ModernVCPLogWidget {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }

            #vcplog_header {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f0f0f0);
                border-bottom: 1px solid #e0e0e0;
                border-radius: 8px 8px 0 0;
            }

            #vcplog_status {
                background-color: #e3f2fd;
                border-bottom: 1px solid #e0e0e0;
            }

            #vcplog_status[status="connected"] {
                background-color: #e8f5e8;
            }

            #vcplog_status[status="error"] {
                background-color: #ffebee;
            }

            #vcplog_status[status="connecting"] {
                background-color: #fff3e0;
            }

            #vcplog_actions {
                background-color: #f5f5f5;
                border-top: 1px solid #e0e0e0;
                border-radius: 0 0 8px 8px;
            }

            QPushButton {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 9pt;
            }

            QPushButton:hover {
                background-color: #f0f0f0;
                border-color: #2196F3;
            }

            QPushButton:pressed {
                background-color: #e0e0e0;
            }

            QScrollArea {
                background-color: transparent;
                border: none;
            }
        """)

    def _update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now()
        time_str = current_time.strftime("%H:%M:%S")
        date_str = current_time.strftime("%m-%d")
        self.time_label.setText(f"{date_str} {time_str}")

    def add_vcp_log(self, log_data: Dict[str, Any]):
        """添加VCP日志项"""
        try:
            # 添加时间戳和ID
            if 'timestamp' not in log_data:
                log_data['timestamp'] = datetime.now().isoformat()
            elif isinstance(log_data['timestamp'], datetime):
                log_data['timestamp'] = log_data['timestamp'].isoformat()
            if 'id' not in log_data:
                log_data['id'] = str(id(log_data)) + str(datetime.now().timestamp())

            # 隐藏空状态提示（如果这是第一个项目）
            if len(self.log_items) == 0:
                self.empty_state_label.hide()

            # 创建日志项
            log_item = ModernVCPLogItem(log_data, self)
            log_item.copy_requested.connect(self._handle_copy_request)
            log_item.remove_requested.connect(self._handle_remove_request)

            # 插入到顶部（在空状态标签之前）
            self.content_layout.insertWidget(0, log_item)
            self.log_items.insert(0, log_item)

            # 限制最大数量
            if len(self.log_items) > self.max_items:
                old_item = self.log_items.pop()
                old_item.setParent(None)
                old_item.deleteLater()

            # 更新统计
            self._update_stats()

            # 滚动到顶部
            QTimer.singleShot(100, lambda: self.scroll_area.verticalScrollBar().setValue(0))

            # 发送信号
            self.item_count_changed.emit(len(self.log_items))

            logger.info(f"添加VCPLog项: {log_data.get('type', 'unknown')}")

        except Exception as e:
            logger.error(f"添加VCPLog项失败: {e}")

    def update_connection_status(self, status: str, message: str = ""):
        """更新连接状态"""
        self.connection_status = message or status
        self.status_label.setText(f"VCPLog: {self.connection_status}")

        # 更新状态样式
        status_map = {
            'connected': 'connected',
            'open': 'connected',
            'closed': 'error',
            'error': 'error',
            'connecting': 'connecting'
        }

        status_class = status_map.get(status, 'error')
        self.status_widget.setProperty('status', status_class)
        self.status_widget.style().unpolish(self.status_widget)
        self.status_widget.style().polish(self.status_widget)

        self.status_changed.emit(status)
        logger.info(f"VCPLog连接状态更新: {status} - {message}")

    def clear_all_items(self):
        """清空所有项目"""
        for item in self.log_items:
            item.setParent(None)
            item.deleteLater()

        self.log_items.clear()
        self._update_stats()
        self.item_count_changed.emit(0)

        # 显示空状态提示
        self.empty_state_label.show()

        logger.info("已清空所有VCPLog项")

    def expand_all_items(self):
        """展开所有项目"""
        for item in self.log_items:
            if not item.is_expanded:
                item.expand()

    def collapse_all_items(self):
        """折叠所有项目"""
        for item in self.log_items:
            if item.is_expanded:
                item.collapse()

    def _update_stats(self):
        """更新统计信息"""
        count = len(self.log_items)
        self.stats_label.setText(f"总计: {count} 条")
        self.clear_btn.setEnabled(count > 0)

    def _handle_copy_request(self, content: str):
        """处理复制请求"""
        QApplication.clipboard().setText(content)
        logger.info("内容已复制到剪贴板")

    def _handle_remove_request(self, item_id: str):
        """处理删除请求"""
        for i, item in enumerate(self.log_items):
            if item.log_data.get('id') == item_id:
                item.setParent(None)
                item.deleteLater()
                self.log_items.pop(i)
                break

        self._update_stats()
        self.item_count_changed.emit(len(self.log_items))

    def update_theme(self, is_dark_theme: bool):
        """更新主题"""
        self.is_dark_theme = is_dark_theme

        if is_dark_theme:
            self.setStyleSheet("""
                ModernVCPLogWidget {
                    background-color: #2d3142;
                    border: 1px solid #4f5666;
                    border-radius: 8px;
                    color: #ffffff;
                }

                #vcplog_header {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3d4252, stop:1 #2d3142);
                    border-bottom: 1px solid #4f5666;
                    border-radius: 8px 8px 0 0;
                }

                #vcplog_status {
                    background-color: #1e3a5f;
                    border-bottom: 1px solid #4f5666;
                    color: #ffffff;
                }

                #vcplog_status[status="connected"] {
                    background-color: #2e5d31;
                }

                #vcplog_status[status="error"] {
                    background-color: #5d2e2e;
                }

                #vcplog_status[status="connecting"] {
                    background-color: #5d4e2e;
                }

                #vcplog_actions {
                    background-color: #3d4252;
                    border-top: 1px solid #4f5666;
                    border-radius: 0 0 8px 8px;
                }

                QPushButton {
                    background-color: #4d5262;
                    border: 1px solid #5f6672;
                    color: #ffffff;
                }

                QPushButton:hover {
                    background-color: #5d6272;
                    border-color: #64b5f6;
                }

                QPushButton:pressed {
                    background-color: #3d4252;
                }
            """)
        else:
            self._apply_modern_styles()

        # 更新所有项目的主题
        for item in self.log_items:
            item.update_theme(is_dark_theme)

    def get_item_count(self) -> int:
        """获取项目数量"""
        return len(self.log_items)

    def set_max_items(self, max_items: int):
        """设置最大项目数"""
        self.max_items = max_items

        # 如果当前项目数超过限制，删除多余的
        while len(self.log_items) > self.max_items:
            old_item = self.log_items.pop()
            old_item.setParent(None)
            old_item.deleteLater()

        self._update_stats()
