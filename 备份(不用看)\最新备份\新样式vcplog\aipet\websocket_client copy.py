# -*- coding: utf-8 -*-
"""
WebSocket客户端模块 - 用于接收VCPLog的实时推送消息
"""

import json
import threading
import time
import logging
from typing import Optional, Callable, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread

try:
    import websocket
except ImportError:
    websocket = None

logger = logging.getLogger(__name__)

class VCPLogClient(QObject):
    """VCPLog WebSocket客户端，用于接收VCP工具调用日志推送"""
    
    # 信号定义
    vcp_log_received = pyqtSignal(dict)         # 收到VCP日志消息
    agent_message_received = pyqtSignal(dict)   # 收到Agent消息通知
    connection_status_changed = pyqtSignal(bool)   # 连接状态变化
    error_occurred = pyqtSignal(str)               # 发生错误
    
    def __init__(self, server_url: str = "ws://vcp.012255.xyz", vcp_key: str = "123456", app_controller=None):
        super().__init__()
        # 构建VCPLog特定的URL格式
        self.base_url = server_url.rstrip('/')
        self.vcp_key = vcp_key
        self.url = f"{self.base_url}/VCPlog/VCP_Key={self.vcp_key}"
        self.app_controller = app_controller
        self.ws = None
        self.is_connected = False
        self.should_reconnect = True
        self.reconnect_timer = QTimer()
        self.reconnect_timer.timeout.connect(self.connect)
        self.reconnect_timer.setSingleShot(True)
        
        # 检查websocket-client是否可用
        if websocket is None:
            logger.error("websocket-client库未安装，请运行: pip install websocket-client")
            self.error_occurred.emit("WebSocket库未安装")
            
        if not self.vcp_key:
            logger.warning("VCP_Key未设置，连接可能失败")
    
    def connect(self):
        """连接到VCPLog WebSocket服务器"""
        if websocket is None:
            return
            
        if self.is_connected:
            return
            
        if not self.vcp_key:
            self.error_occurred.emit("VCP_Key未设置，无法连接")
            return
            
        try:
            logger.info(f"正在连接到VCPLog服务器: {self.url}")
            
            # 创建WebSocket连接
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # 在单独线程中运行WebSocket
            self.ws_thread = threading.Thread(target=self.ws.run_forever, daemon=True)
            self.ws_thread.start()
            
        except Exception as e:
            logger.error(f"VCPLog WebSocket连接失败: {e}")
            self.error_occurred.emit(f"连接失败: {e}")
            self._schedule_reconnect()
    
    def disconnect(self):
        """断开WebSocket连接"""
        self.should_reconnect = False
        self.reconnect_timer.stop()
        
        if self.ws:
            self.ws.close()
            self.ws = None
        
        if hasattr(self, 'ws_thread') and self.ws_thread.is_alive():
            self.ws_thread.join(timeout=1.0)
    
    def _on_open(self, ws):
        """WebSocket连接打开回调"""
        logger.info("VCPLog WebSocket连接已建立")
        self.is_connected = True
        self.connection_status_changed.emit(True)
        
        # VCPLog通过URL中的VCP_Key进行认证，不需要发送额外的认证消息
        # 连接成功即表示认证通过
        logger.info("VCPLog连接已认证，等待接收日志推送...")
    
    def _on_message(self, ws, message):
        """收到WebSocket消息回调"""
        try:
            data = json.loads(message)
            msg_type = data.get('type', 'unknown')
            logger.info(f"收到WebSocket消息: {msg_type}")
            
            if msg_type == 'connection_ack':
                logger.info(f"连接确认: {data.get('message', '')}")
            elif msg_type == 'vcp_log':
                # 这是VCP工具调用日志
                vcp_data = data.get('data', {})
                tool_name = vcp_data.get('tool_name', 'Unknown')
                status = vcp_data.get('status', 'unknown')
                content_preview = vcp_data.get('content', '')[:50]
                logger.info(f"收到VCP日志: {tool_name} [{status}] - {content_preview}...")
                self.vcp_log_received.emit(data)
            elif msg_type == 'agent_message':
                # 这是AgentMessage插件推送的用户通知
                message_content = data.get('message', '')
                recipient = data.get('recipient', '')
                timestamp = data.get('timestamp', '')
                original_content = data.get('originalContent', '')
                content_preview = message_content[:50] if message_content else original_content[:50]
                logger.info(f"收到Agent消息: {recipient} - {content_preview}...")
                self.agent_message_received.emit(data)
            else:
                logger.debug(f"收到未知类型消息: {msg_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"解析VCPLog消息失败: {e}")
        except Exception as e:
            logger.error(f"处理VCPLog消息时出错: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"VCPLog WebSocket错误: {error}")
        self.error_occurred.emit(f"VCPLog连接错误: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭回调"""
        logger.info(f"VCPLog WebSocket连接已关闭 (代码: {close_status_code}, 消息: {close_msg})")
        self.is_connected = False
        self.connection_status_changed.emit(False)
        
        # 如果应该重连，则安排重连
        if self.should_reconnect:
            self._schedule_reconnect()
    
    def _schedule_reconnect(self):
        """安排重新连接"""
        if not self.should_reconnect:
            return
            
        # 5秒后重连
        logger.info("5秒后尝试重新连接VCPLog服务器...")
        self.reconnect_timer.start(5000)
    
    def update_vcp_key(self, new_key: str):
        """更新VCP密钥并重新构建连接URL"""
        self.vcp_key = new_key
        self.url = f"{self.base_url}/VCPlog/VCP_Key={self.vcp_key}"
        logger.info("VCP_Key已更新，下次连接将使用新密钥")


class AgentMessageHandler:
    """Agent消息处理器"""
    
    @staticmethod
    def format_agent_message(message_data: Dict[str, Any]) -> Dict[str, str]:
        """格式化Agent消息为聊天界面可显示的格式"""
        message_content = message_data.get('message', '')
        recipient = message_data.get('recipient', '')
        timestamp = message_data.get('timestamp', '')
        original_content = message_data.get('originalContent', '')
        
        # 解析消息格式（时间戳 - 发送者\n消息内容）
        lines = message_content.split('\n', 1) if message_content else []
        if len(lines) >= 2:
            header = lines[0]  # 包含时间戳和发送者信息
            content = lines[1]  # 实际消息内容
            
            # 尝试从header中提取发送者名称（格式：时间戳 - 发送者）
            if ' - ' in header:
                timestamp_part, sender_part = header.split(' - ', 1)
                sender = sender_part or recipient or 'AI助手'
            else:
                sender = recipient or 'AI助手'
        else:
            # 如果消息格式不标准，使用原始内容
            content = message_content or original_content
            sender = recipient or 'AI助手'
        
        # 根据发送者添加合适的前缀图标
        if sender and sender != 'AI助手':
            display_content = f"📢 **{sender}**: {content}"
        else:
            display_content = f"🤖 {content}"
        
        return {
            'sender': sender,
            'content': display_content,
            'message_id': f"agent_{int(time.time() * 1000)}",
            'timestamp': int(time.time() * 1000),
            'source': 'agent_message',
            'raw_message': message_content,
            'original_content': original_content
        }
    
    @staticmethod
    def is_agent_message(message_data: Dict[str, Any]) -> bool:
        """检查是否为Agent消息"""
        return message_data.get('type') == 'agent_message'


class VCPLogMessageHandler:
    """VCP日志消息处理器"""
    
    @staticmethod
    def format_vcp_log_message(message_data: Dict[str, Any]) -> Dict[str, str]:
        """格式化VCP日志消息为聊天界面可显示的格式"""
        data = message_data.get('data', {})
        tool_name = data.get('tool_name', 'Unknown Tool')
        status = data.get('status', 'unknown')
        content = data.get('content', '')
        source = data.get('source', 'unknown')
        
        # 根据状态设置不同的前缀和样式
        status_prefix = "✅" if status == "success" else "❌" if status == "error" else "ℹ️"
        
        formatted_content = f"{status_prefix} **{tool_name}** [{status.upper()}]\n{content}"
        if source != 'unknown':
            formatted_content += f"\n\n_来源: {source}_"
        
        return {
            'sender': 'VCP工具',
            'content': formatted_content,
            'message_id': f"vcp_{int(time.time() * 1000)}",
            'timestamp': int(time.time() * 1000),
            'source': 'vcp_log',
            'tool_name': tool_name,
            'status': status
        }
    
    @staticmethod
    def is_vcp_log_message(message_data: Dict[str, Any]) -> bool:
        """检查是否为VCP日志消息"""
        return message_data.get('type') == 'vcp_log'


# 保持向后兼容性的别名
WebSocketClient = VCPLogClient
CompanionMessageHandler = VCPLogMessageHandler
AgentHandler = AgentMessageHandler  # 新增别名