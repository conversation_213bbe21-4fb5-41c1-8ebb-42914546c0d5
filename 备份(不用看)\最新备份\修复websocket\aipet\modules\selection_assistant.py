"""
划词助手模块 - 基于 VCPChat 的全局文本选择功能
提供全局文本选择监听和悬浮工具条功能
"""

import sys
import time
import logging
from typing import Optional, Callable, Tuple
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QPushButton, QApplication, 
                             QFrame, QGraphicsDropShadowEffect)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QPoint
from PyQt5.QtGui import QCursor, QClipboard

# Windows 特定导入
if sys.platform == "win32":
    try:
        import win32gui
        import win32con
        import win32api
        import win32clipboard
        WINDOWS_AVAILABLE = True
    except ImportError:
        WINDOWS_AVAILABLE = False
        logging.warning("Windows API 不可用，划词助手功能将受限")
else:
    WINDOWS_AVAILABLE = False

logger = logging.getLogger(__name__)

class SelectionMonitor(QThread):
    """全局文本选择监听线程"""
    text_selected = pyqtSignal(str, QPoint)  # 选中的文本和鼠标位置

    def __init__(self):
        super().__init__()
        self.running = False
        self.last_clipboard_text = ""
        self.last_check_time = 0
        self.check_interval = 0.3  # 检查间隔（秒）
        self.clipboard = QApplication.clipboard()

        # 连接剪贴板变化信号
        self.clipboard.dataChanged.connect(self.on_clipboard_changed)

    def run(self):
        """监听线程主循环"""
        self.running = True
        logger.info("划词助手监听线程已启动")

        while self.running:
            try:
                # 定期检查（作为备用机制）
                current_time = time.time()
                if current_time - self.last_check_time > self.check_interval:
                    self.check_selection_backup()
                    self.last_check_time = current_time

                time.sleep(0.1)  # 短暂休眠
            except Exception as e:
                logger.error(f"选择监听错误: {e}")
                time.sleep(1)

    def on_clipboard_changed(self):
        """剪贴板内容变化时触发"""
        try:
            current_text = self.clipboard.text()

            # 检查是否是有效的文本选择
            if self.is_valid_selection(current_text):
                cursor_pos = QCursor.pos()
                self.text_selected.emit(current_text.strip(), cursor_pos)
                logger.debug(f"检测到文本选择: {current_text[:50]}...")

        except Exception as e:
            logger.debug(f"剪贴板变化处理错误: {e}")

    def check_selection_backup(self):
        """备用选择检测机制"""
        try:
            current_text = self.clipboard.text()

            if self.is_valid_selection(current_text):
                # 检查是否是新的选择（基于时间间隔）
                current_time = time.time()
                if current_time - self.last_check_time > 1.0:  # 至少1秒间隔
                    cursor_pos = QCursor.pos()
                    self.text_selected.emit(current_text.strip(), cursor_pos)

        except Exception as e:
            logger.debug(f"备用选择检测错误: {e}")

    def is_valid_selection(self, text: str) -> bool:
        """检查是否是有效的文本选择"""
        if not text or text == self.last_clipboard_text:
            return False

        text = text.strip()

        # 基本长度检查
        if len(text) < 3 or len(text) > 1000:
            return False

        # 排除一些明显不是选择的内容
        if text.startswith(('http://', 'https://', 'file://', 'ftp://')):
            return False

        # 排除单个字符或数字
        if len(text) == 1 or text.isdigit():
            return False

        # 更新最后的文本
        self.last_clipboard_text = text
        return True

    def stop(self):
        """停止监听"""
        self.running = False
        logger.info("正在停止划词助手监听线程")

        # 断开剪贴板信号
        try:
            self.clipboard.dataChanged.disconnect(self.on_clipboard_changed)
        except:
            pass

        self.quit()
        self.wait(3000)  # 等待最多3秒

class FloatingToolbar(QWidget):
    """悬浮工具条"""
    action_triggered = pyqtSignal(str, str)  # 动作类型，选中文本
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_text = ""
        self.setup_ui()
        self.setup_style()
        
        # 自动隐藏定时器
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide)
        self.hide_timer.setSingleShot(True)
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主容器
        self.container = QFrame(self)
        layout = QHBoxLayout(self.container)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)
        
        # 操作按钮
        self.actions = [
            ("翻译", "translate", "🌐"),
            ("总结", "summarize", "📝"),
            ("解释", "explain", "💡"),
            ("搜索", "search", "🔍"),
        ]
        
        self.buttons = {}
        for name, action, icon in self.actions:
            btn = QPushButton(f"{icon} {name}")
            btn.clicked.connect(lambda checked, a=action: self.trigger_action(a))
            btn.setFixedHeight(32)
            btn.setMinimumWidth(60)
            layout.addWidget(btn)
            self.buttons[action] = btn
        
        # 设置主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.container)
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(40, 40, 44, 0.95);
                border: 1px solid rgba(100, 100, 100, 0.3);
                border-radius: 8px;
            }
            QPushButton {
                background-color: rgba(70, 70, 74, 0.8);
                color: #e0e0e0;
                border: 1px solid rgba(100, 100, 100, 0.2);
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: rgba(90, 90, 94, 0.9);
                border-color: rgba(120, 120, 120, 0.4);
            }
            QPushButton:pressed {
                background-color: rgba(60, 60, 64, 0.9);
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(Qt.black)
        self.container.setGraphicsEffect(shadow)
    
    def show_at_position(self, text: str, position: QPoint):
        """在指定位置显示工具条"""
        self.selected_text = text

        # 确保工具条大小已计算
        self.adjustSize()
        toolbar_size = self.size()

        # 调整位置，确保不超出屏幕边界
        screen = QApplication.primaryScreen().geometry()

        x = position.x() - toolbar_size.width() // 2
        y = position.y() + 30  # 在鼠标下方显示，增加间距

        # 边界检查
        if x < 10:
            x = 10
        elif x + toolbar_size.width() > screen.width() - 10:
            x = screen.width() - toolbar_size.width() - 10

        if y + toolbar_size.height() > screen.height() - 10:
            y = position.y() - toolbar_size.height() - 20  # 在鼠标上方显示

        self.move(x, y)
        self.show()
        self.raise_()
        self.activateWindow()  # 确保窗口激活

        # 设置自动隐藏
        self.hide_timer.start(8000)  # 8秒后自动隐藏

        logger.info(f"悬浮工具条已显示在位置 ({x}, {y})，文本: {text[:30]}...")
    
    def trigger_action(self, action: str):
        """触发动作"""
        if self.selected_text:
            self.action_triggered.emit(action, self.selected_text)
        self.hide()
    
    def enterEvent(self, event):
        """鼠标进入时停止自动隐藏"""
        self.hide_timer.stop()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开时重新开始自动隐藏"""
        self.hide_timer.start(2000)  # 2秒后隐藏
        super().leaveEvent(event)

class SelectionAssistant:
    """划词助手主类"""

    def __init__(self, app_controller):
        self.app_controller = app_controller
        self.monitor = None
        self.toolbar = None
        self.enabled = False
        self.manual_trigger_shortcut = None
        
    def start(self):
        """启动划词助手"""
        if self.enabled:
            return

        try:
            # 创建监听器
            self.monitor = SelectionMonitor()
            self.monitor.text_selected.connect(self.on_text_selected)

            # 创建工具条
            self.toolbar = FloatingToolbar()
            self.toolbar.action_triggered.connect(self.handle_action)

            # 设置快捷键 (Ctrl+Shift+S)
            self.setup_manual_trigger()

            # 启动监听
            self.monitor.start()
            self.enabled = True

            logger.info("划词助手已启动 (快捷键: Ctrl+Shift+S)")

        except Exception as e:
            logger.error(f"启动划词助手失败: {e}")

    def setup_manual_trigger(self):
        """设置手动触发快捷键"""
        try:
            from PyQt5.QtWidgets import QShortcut
            from PyQt5.QtGui import QKeySequence

            # 尝试在主窗口设置快捷键
            main_window = None
            if hasattr(self.app_controller, 'chat_window') and self.app_controller.chat_window:
                main_window = self.app_controller.chat_window
            elif hasattr(self.app_controller, 'app') and self.app_controller.app:
                # 尝试获取主窗口
                for widget in self.app_controller.app.topLevelWidgets():
                    if widget.isVisible():
                        main_window = widget
                        break

            if main_window:
                self.manual_trigger_shortcut = QShortcut(QKeySequence("Ctrl+Shift+S"), main_window)
                self.manual_trigger_shortcut.activated.connect(self.manual_trigger)
                logger.info("快捷键 Ctrl+Shift+S 已设置")
            else:
                logger.warning("无法设置快捷键：找不到主窗口")

        except Exception as e:
            logger.warning(f"设置快捷键失败: {e}")

    def manual_trigger(self):
        """手动触发划词功能"""
        try:
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtGui import QCursor

            # 获取剪贴板文本
            clipboard = QApplication.clipboard()
            text = clipboard.text()

            if text and len(text.strip()) > 2:
                cursor_pos = QCursor.pos()
                self.on_text_selected(text.strip(), cursor_pos)
                logger.info(f"手动触发划词功能: {text[:30]}...")
            else:
                logger.info("手动触发失败：剪贴板中没有有效文本")

        except Exception as e:
            logger.error(f"手动触发失败: {e}")
    
    def stop(self):
        """停止划词助手"""
        if not self.enabled:
            return

        try:
            if self.monitor:
                self.monitor.stop()
                self.monitor = None

            if self.toolbar:
                self.toolbar.hide()
                self.toolbar = None

            # 清理快捷键
            if self.manual_trigger_shortcut:
                self.manual_trigger_shortcut.setParent(None)
                self.manual_trigger_shortcut = None

            self.enabled = False
            logger.info("划词助手已停止")

        except Exception as e:
            logger.error(f"停止划词助手失败: {e}")
    
    def on_text_selected(self, text: str, position: QPoint):
        """处理文本选择事件"""
        if not self.enabled or not text.strip():
            return
            
        # 过滤太短或太长的文本
        if len(text.strip()) < 3 or len(text) > 500:
            return
            
        # 显示工具条
        if self.toolbar:
            self.toolbar.show_at_position(text, position)
    
    def handle_action(self, action: str, text: str):
        """处理用户操作"""
        try:
            # 根据动作类型生成提示词
            prompts = {
                "translate": f"请将以下文本翻译成中文：\n\n{text}",
                "summarize": f"请总结以下文本的主要内容：\n\n{text}",
                "explain": f"请解释以下文本的含义：\n\n{text}",
                "search": f"请帮我搜索关于以下内容的信息：\n\n{text}",
            }
            
            prompt = prompts.get(action, f"请处理以下文本：\n\n{text}")
            
            # 通过 app_controller 发送消息
            if self.app_controller and hasattr(self.app_controller, 'send_message'):
                self.app_controller.send_message(prompt)
            else:
                logger.warning("无法发送消息：app_controller 不可用")
                
        except Exception as e:
            logger.error(f"处理动作失败: {e}")
    
    def is_enabled(self) -> bool:
        """检查是否已启用"""
        return self.enabled
