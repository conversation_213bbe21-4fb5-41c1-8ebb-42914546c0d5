#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代化VCPLog通知系统
向运行中的程序发送测试通知
"""

import json
import time
import websocket
from datetime import datetime

def send_test_notification():
    """发送测试VCP通知"""
    
    # VCPLog服务器地址和密钥
    server_url = "ws://103.189.141.240:6005/VCPlog/VCP_Key=123456"
    
    # 测试消息数据
    test_message = {
        "type": "vcp_log",
        "data": {
            "tool_name": "测试工具",
            "status": "success",
            "content": json.dumps({
                "MaidName": "测试助手",
                "timestamp": datetime.now().isoformat(),
                "original_plugin_output": {
                    "action": "现代化通知栏测试",
                    "message": "这是一条测试消息，用于验证新的现代化VCPLog通知栏是否正常工作。",
                    "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }, ensure_ascii=False)
        }
    }
    
    def on_open(ws):
        print("✓ WebSocket连接已建立")
        print("📤 发送测试消息...")
        
        # 发送测试消息
        ws.send(json.dumps(test_message))
        print("✅ 测试消息已发送")
        
        # 等待一秒后关闭连接
        time.sleep(1)
        ws.close()
    
    def on_message(ws, message):
        print(f"📨 收到响应: {message}")
    
    def on_error(ws, error):
        print(f"❌ WebSocket错误: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print("🔌 WebSocket连接已关闭")
    
    try:
        print("🚀 开始测试现代化VCPLog通知系统...")
        print(f"🔗 连接到: {server_url}")
        
        # 创建WebSocket连接
        ws = websocket.WebSocketApp(
            server_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        # 运行WebSocket
        ws.run_forever()
        
        print("✅ 测试完成！")
        print("💡 请检查您的聊天窗口，应该能看到新的现代化通知栏显示测试消息。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    send_test_notification()
