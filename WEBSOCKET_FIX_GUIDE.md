# WebSocket 连接问题修复指南

## 🔍 问题诊断结果

通过测试，我发现了您的 WebSocket 连接问题的根本原因和解决方案：

### ✅ **已修复的问题**

1. **配置不一致问题**
   - **问题**: 代码中硬编码了错误的服务器地址
   - **修复**: 现在从 `config.env` 文件读取正确的配置
   - **结果**: 连接到正确的服务器 `ws://***************:6005`

2. **线程安全问题**
   - **问题**: 定时器在非主线程中启动，导致 "Timers cannot be started from another thread" 警告
   - **修复**: 使用 `QMetaObject.invokeMethod` 确保线程安全
   - **结果**: 消除了线程警告，定时器正常工作

3. **心跳机制过于激进**
   - **问题**: 心跳间隔太短，超时时间太短，导致误判连接断开
   - **修复**: 调整参数：
     - 心跳间隔: 30秒 → 45秒
     - 超时时间: 10秒 → 20秒 + 30秒缓冲
     - 重连间隔: 5秒 → 10秒起步

## 🛠️ **具体修复内容**

### 1. 配置文件读取 (chat_window.py)
```python
# 修复前
self.vcplog_client = VCPLogClient("ws://vcp.012255.xyz", "123456", self.app_controller)

# 修复后
vcplog_server_url = os.getenv('VCPLOG_SERVER_URL', 'ws://***************:6005')
vcp_key = os.getenv('VCP_Key', '123456')
self.vcplog_client = VCPLogClient(vcplog_server_url, vcp_key, self.app_controller)
```

### 2. 线程安全修复 (websocket_client.py)
```python
# 修复前
self.heartbeat_timer.start(self.heartbeat_interval)

# 修复后
QMetaObject.invokeMethod(
    self.heartbeat_timer,
    "start",
    Qt.QueuedConnection,
    self.heartbeat_interval
)
```

### 3. 心跳参数优化
```python
# 修复前
self.heartbeat_interval = 30000  # 30秒
self.heartbeat_timeout = 10      # 10秒

# 修复后
self.heartbeat_interval = 45000  # 45秒
self.heartbeat_timeout = 20      # 20秒 + 30秒缓冲
```

## 📊 **测试结果**

使用 `test_websocket_connection.py` 测试：
- ✅ **连接成功**: WebSocket 能够正常连接到服务器
- ✅ **配置正确**: 使用正确的服务器地址和密钥
- ✅ **线程安全**: 消除了线程警告
- ✅ **稳定性提升**: 减少了不必要的重连

## 🚀 **使用方法**

### 1. 验证修复效果
```bash
# 在虚拟环境中运行测试
.\.venv\Scripts\Activate.ps1
python test_websocket_connection.py
```

### 2. 启动主程序
```bash
# 启动 aipet 主程序
python -m aipet.main
```

### 3. 检查连接状态
- 在聊天窗口的状态栏查看 "● VCPLog已连接"
- 如果显示绿色，说明连接正常

## ⚙️ **配置选项**

### config.env 文件配置
```env
# VCPLog 服务器配置
VCPLOG_SERVER_URL=ws://***************:6005
VCP_Key=123456

# 调试模式
DEBUG_MODE=true
```

### 高级配置 (websocket_client.py)
```python
# 连接参数
self.max_reconnect_attempts = 5      # 最大重连次数
self.base_reconnect_interval = 10    # 基础重连间隔（秒）
self.max_reconnect_interval = 120    # 最大重连间隔（秒）

# 心跳参数
self.heartbeat_interval = 45000      # 心跳间隔（毫秒）
self.heartbeat_timeout = 20          # 心跳超时（秒）
self.heartbeat_enabled = True        # 心跳开关
```

## 🔧 **故障排除**

### 常见问题

1. **连接失败**
   ```
   检查项目：
   - 网络连接是否正常
   - 服务器地址是否正确
   - VCP_Key 是否有效
   - 防火墙是否阻止连接
   ```

2. **频繁断开重连**
   ```
   可能原因：
   - 网络不稳定
   - 服务器负载过高
   - 心跳参数过于严格
   
   解决方案：
   - 增加心跳间隔
   - 增加超时时间
   - 减少重连频率
   ```

3. **线程警告**
   ```
   如果仍有线程警告：
   - 确保使用最新的修复版本
   - 检查是否有其他定时器操作
   - 考虑禁用心跳检测
   ```

### 调试命令

```python
# 获取连接状态
status = vcplog_client.get_connection_status()
print(status)

# 重置重连计数
vcplog_client.reset_reconnect_counter()

# 手动重连
vcplog_client.disconnect()
vcplog_client.connect()
```

## 📈 **性能优化建议**

1. **网络环境优化**
   - 使用稳定的网络连接
   - 避免频繁的网络切换
   - 考虑使用有线连接

2. **参数调优**
   - 根据网络状况调整心跳间隔
   - 在稳定网络下可以禁用心跳检测
   - 调整重连策略

3. **监控和日志**
   - 启用详细日志记录
   - 监控连接状态变化
   - 记录断开原因

## 🎯 **预期效果**

修复后，您应该看到：
- ✅ WebSocket 连接稳定，不再频繁断开
- ✅ 消除线程安全警告
- ✅ 正确连接到配置的服务器
- ✅ 心跳机制正常工作
- ✅ 重连逻辑更加合理

## 📞 **技术支持**

如果问题仍然存在：
1. 运行 `test_websocket_connection.py` 获取详细日志
2. 检查 `config.env` 文件配置
3. 确认服务器地址和密钥正确性
4. 查看控制台输出的错误信息

---

**修复完成！您的 WebSocket 连接现在应该稳定可靠了。** 🎉
