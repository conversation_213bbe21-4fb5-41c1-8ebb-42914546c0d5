from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, 
                             QPushButton, QMessageBox, QListWidgetItem, QDialogButtonBox)
from PyQt5.QtCore import Qt
from typing import Optional

# 尝试从相对路径导入
try:
    from .assistant_manager import Assistant<PERSON>anager
    from .assistant_config import AssistantConfig
    from .new_assistant_dialog import NewAssistantDialog # 用于编辑功能
except ImportError:
    from assistant_manager import AssistantManager
    from assistant_config import AssistantConfig
    from new_assistant_dialog import NewAssistantDialog

class ManageAssistantsDialog(QDialog):
    def __init__(self, parent=None, assistant_manager: Assistant<PERSON>anager = None):
        super().__init__(parent)
        self.assistant_manager = assistant_manager
        
        self.setWindowTitle("管理助手")
        self.setModal(True)
        self.setMinimumWidth(500)
        self.setMinimumHeight(350)

        self._setup_ui()
        self._load_assistants()

    def _setup_ui(self):
        main_layout = QVBoxLayout(self)

        self.assistants_list_widget = QListWidget()
        self.assistants_list_widget.itemDoubleClicked.connect(self._edit_selected_assistant)
        main_layout.addWidget(self.assistants_list_widget)

        buttons_layout = QHBoxLayout()
        edit_button = QPushButton("编辑选定助手")
        edit_button.clicked.connect(self._edit_selected_assistant)
        delete_button = QPushButton("删除选定助手")
        delete_button.clicked.connect(self._delete_selected_assistant)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)
        
        # 关闭按钮
        self.close_button_box = QDialogButtonBox(QDialogButtonBox.Close)
        self.close_button_box.rejected.connect(self.reject) # Close button acts as reject
        main_layout.addWidget(self.close_button_box)

    def _load_assistants(self):
        self.assistants_list_widget.clear()
        if not self.assistant_manager:
            return
        
        assistants = self.assistant_manager.get_all_assistants()
        if not assistants:
            no_assistants_item = QListWidgetItem("没有可用的助手。")
            no_assistants_item.setFlags(no_assistants_item.flags() & ~Qt.ItemIsSelectable)
            self.assistants_list_widget.addItem(no_assistants_item)
        else:
            for assistant in sorted(assistants, key=lambda a: a.name.lower()): # 按名称排序
                item_text = f"{assistant.name} (ID: {assistant.id}) - 模型: {assistant.model_path.split('/')[-1]}"
                list_item = QListWidgetItem(item_text)
                list_item.setData(Qt.UserRole, assistant.id) # 将助手ID存储在项目中
                self.assistants_list_widget.addItem(list_item)

    def _get_selected_assistant_id(self) -> Optional[str]:
        selected_items = self.assistants_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "没有选择", "请先从列表中选择一个助手。")
            return None
        return selected_items[0].data(Qt.UserRole)

    def _edit_selected_assistant(self):
        assistant_id = self._get_selected_assistant_id()
        if not assistant_id or not self.assistant_manager:
            return

        assistant_to_edit = self.assistant_manager.get_assistant(assistant_id)
        if not assistant_to_edit:
            QMessageBox.critical(self, "错误", f"找不到ID为 '{assistant_id}' 的助手数据。")
            self._load_assistants() # 刷新列表，以防数据不一致
            return

        # ----- 开始：为 NewAssistantDialog 添加编辑功能的设想 -----
        # 这部分逻辑需要 NewAssistantDialog 的支持
        # 我们将创建一个新的对话框实例，但用现有数据填充它
        edit_dialog = NewAssistantDialog(self, self.assistant_manager, assistant_to_edit)
        
        if edit_dialog.exec_() == QDialog.Accepted:
            # NewAssistantDialog 内部的 accept 方法应该处理更新
            # 或者在这里调用一个 manager.update_assistant()
            # 重要的是，NewAssistantDialog 在编辑模式下不能尝试创建新ID
            QMessageBox.information(self, "成功", f"助手 '{assistant_to_edit.name}' 已更新。")
            self._load_assistants() # 刷新列表
        # ----- 结束：为 NewAssistantDialog 添加编辑功能的设想 -----

    def _delete_selected_assistant(self):
        assistant_id = self._get_selected_assistant_id()
        if not assistant_id or not self.assistant_manager:
            return

        assistant_to_delete = self.assistant_manager.get_assistant(assistant_id)
        if not assistant_to_delete:
            QMessageBox.critical(self, "错误", f"找不到ID为 '{assistant_id}' 的助手数据。")
            self._load_assistants()
            return
        
        # 确认删除
        reply = QMessageBox.question(self, "确认删除", 
                                     f"确定要删除助手 '{assistant_to_delete.name}' 吗？此操作无法撤销。",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if self.assistant_manager.delete_assistant(assistant_id):
                QMessageBox.information(self, "删除成功", f"助手 '{assistant_to_delete.name}' 已被删除。")
                self._load_assistants() # 刷新列表
            else:
                QMessageBox.critical(self, "删除失败", f"无法删除助手 '{assistant_to_delete.name}'。详情请查看控制台输出。")

# 为了使编辑功能正常工作，NewAssistantDialog 需要进行修改。
# 以下是在 NewAssistantDialog 中需要添加/修改的方法的草稿：

'''
# In aipet/new_assistant_dialog.py:

class NewAssistantDialog(QDialog):
    # ... (existing __init__ and _setup_ui)

    def __init__(self, parent=None, assistant_manager: AssistantManager = None, assistant_to_edit: Optional[AssistantConfig] = None):
        super().__init__(parent)
        self.assistant_manager = assistant_manager
        self.assistant_to_edit = assistant_to_edit # 新增：存储待编辑的助手
        
        self.setWindowTitle("新建助手" if not assistant_to_edit else f"编辑助手 - {assistant_to_edit.name}")
        self.setModal(True)
        self.setMinimumWidth(450)
        self._setup_ui()

        if self.assistant_to_edit:
            self._load_data_for_editing()

    def _load_data_for_editing(self):
        if not self.assistant_to_edit:
            return
        self.name_edit.setText(self.assistant_to_edit.name)
        self.model_path_edit.setText(self.assistant_to_edit.model_path)
        self.prompt_edit.setPlainText(self.assistant_to_edit.system_prompt)
        # 编辑时不允许修改名称（因为名称可能用于生成ID，如果ID基于名称）
        # 或者，如果ID是独立的UUID，则名称可以修改，但需确保 update_assistant 处理得当
        # 为简单起见，如果ID与名称相关，编辑时最好禁用名称字段
        # self.name_edit.setReadOnly(True) # 可选

    def accept(self):
        name = self.name_edit.text().strip()
        model_path = self.model_path_edit.text().strip()
        system_prompt = self.prompt_edit.toPlainText().strip()

        if not name:
            QMessageBox.warning(self, "输入错误", "助手名称不能为空。")
            self.name_edit.setFocus()
            return
        
        if not model_path:
            QMessageBox.warning(self, "输入错误", "Live2D 模型路径不能为空。")
            return

        # 系统提示词可为空，之前的确认逻辑保持
        if not system_prompt:
            reply = QMessageBox.question(self, "确认", "系统提示词为空，是否继续？",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                self.prompt_edit.setFocus()
                return

        if not self.assistant_manager:
            QMessageBox.critical(self, "错误", "AssistantManager 未初始化！")
            return

        if self.assistant_to_edit: # 编辑模式
            # 更新现有助手
            # 确保ID不变，只更新其他字段
            updated_config = AssistantConfig(
                id=self.assistant_to_edit.id, # 保持原始ID
                name=name, 
                model_path=model_path, 
                system_prompt=system_prompt,
                created_time=self.assistant_to_edit.created_time, # 保持原始创建时间
                last_used=self.assistant_to_edit.last_used # 可以更新，或者由manager处理
            )
            if self.assistant_manager.update_assistant(updated_config):
                QMessageBox.information(self, "成功", f"助手 '{name}' 已成功更新！")
                super().accept()
            else:
                QMessageBox.critical(self, "更新失败", "无法更新助手，请检查控制台输出。")
        else: # 创建模式
            new_assistant = self.assistant_manager.create_assistant(name, model_path, system_prompt)
            if new_assistant:
                QMessageBox.information(self, "成功", f"助手 '{name}' 已成功创建！")
                super().accept()
            else:
                QMessageBox.critical(self, "创建失败", "无法创建助手，请检查控制台。")

# --- End of NewAssistantDialog modifications --- 
'''

if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys
    import os # 确保 os 已导入

    # 这是一个简单的测试用例
    # 实际使用时，AssistantManager 实例会由 ChatWindow 传入
    class MockLive2DWidget:
        def switch_model(self, model_path: str) -> bool: return True
    class MockChatSystem:
        def __init__(self): self.system_prompt = ""
        def update_system_prompt(self, prompt: str): self.system_prompt = prompt

    if not os.path.exists("aipet"):
        os.makedirs("aipet")
        
    mock_assistant_manager = AssistantManager(config_dir="aipet")
    # 为测试创建一些助手
    mock_assistant_manager.create_assistant("助手1", "path/to/model1.model3.json", "提示1")
    mock_assistant_manager.create_assistant("助手2 Beta", "path/to/model2.json", "提示2")

    app = QApplication(sys.argv)
    dialog = ManageAssistantsDialog(assistant_manager=mock_assistant_manager)
    dialog.exec_()
    print("管理助手对话框已关闭。")

    # 清理测试文件
    if os.path.exists("aipet/assistants.json"):
        os.remove("aipet/assistants.json")
    if os.path.exists("aipet"):
        try: os.rmdir("aipet")
        except OSError: print("aipet 目录不为空，未删除。") 