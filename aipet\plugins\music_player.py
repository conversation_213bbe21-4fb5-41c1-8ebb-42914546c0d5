"""
音乐播放插件

这个插件提供音乐播放相关的功能，包括：
- 获取音乐列表
- 播放音乐
- 停止播放
- 暂停/恢复播放
- 获取播放状态
- 调节音量

支持的音频格式：mp3, wav, ogg, flac, m4a
"""

import os
import json
import threading
import time
from typing import List, Dict, Any, Optional
import pygame
from pathlib import Path

# 音乐目录路径 - 相对于项目根目录
MUSIC_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "Music")

class MusicPlayer:
    def __init__(self):
        self.current_song = None
        self.is_playing = False
        self.is_paused = False
        self.volume = 0.7
        self.position = 0
        self.playlist = []
        self.current_index = -1
        self._init_pygame()
        
    def _init_pygame(self):
        """初始化pygame音频系统"""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            print("🎵 音乐播放器初始化成功")
        except Exception as e:
            print(f"❌ 音乐播放器初始化失败: {e}")
    
    def get_music_list(self) -> List[Dict[str, str]]:
        """获取音乐目录下的所有音乐文件"""
        music_files = []
        supported_formats = {'.mp3', '.wav', '.ogg', '.flac', '.m4a'}
        
        if not os.path.exists(MUSIC_DIR):
            return []
            
        try:
            for file in os.listdir(MUSIC_DIR):
                file_path = os.path.join(MUSIC_DIR, file)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in supported_formats:
                        music_files.append({
                            "name": os.path.splitext(file)[0],
                            "filename": file,
                            "path": file_path,
                            "format": file_ext[1:]
                        })
        except Exception as e:
            print(f"❌ 获取音乐列表失败: {e}")
            
        return sorted(music_files, key=lambda x: x['name'])
    
    def play_music(self, music_name: str) -> str:
        """播放指定的音乐"""
        try:
            music_list = self.get_music_list()
            target_music = None
            
            # 查找音乐文件
            for music in music_list:
                if music_name.lower() in music['name'].lower() or music_name == music['filename']:
                    target_music = music
                    break
            
            if not target_music:
                return f"❌ 未找到音乐: {music_name}"
            
            # 停止当前播放
            if self.is_playing:
                pygame.mixer.music.stop()
            
            # 加载并播放音乐
            pygame.mixer.music.load(target_music['path'])
            pygame.mixer.music.set_volume(self.volume)
            pygame.mixer.music.play()
            
            self.current_song = target_music
            self.is_playing = True
            self.is_paused = False
            
            return f"🎵 正在播放: {target_music['name']}"
            
        except Exception as e:
            return f"❌ 播放音乐失败: {str(e)}"
    
    def stop_music(self) -> str:
        """停止播放音乐"""
        try:
            if self.is_playing or self.is_paused:
                pygame.mixer.music.stop()
                self.is_playing = False
                self.is_paused = False
                self.current_song = None
                return "⏹️ 音乐已停止"
            else:
                return "ℹ️ 当前没有播放音乐"
        except Exception as e:
            return f"❌ 停止音乐失败: {str(e)}"
    
    def pause_music(self) -> str:
        """暂停/恢复播放"""
        try:
            if self.is_playing and not self.is_paused:
                pygame.mixer.music.pause()
                self.is_paused = True
                return "⏸️ 音乐已暂停"
            elif self.is_paused:
                pygame.mixer.music.unpause()
                self.is_paused = False
                return "▶️ 音乐已恢复播放"
            else:
                return "ℹ️ 当前没有播放音乐"
        except Exception as e:
            return f"❌ 暂停/恢复失败: {str(e)}"
    
    def set_volume(self, volume: float) -> str:
        """设置音量 (0.0 - 1.0)"""
        try:
            volume = max(0.0, min(1.0, volume))  # 限制在0-1范围内
            self.volume = volume
            pygame.mixer.music.set_volume(volume)
            return f"🔊 音量已设置为: {int(volume * 100)}%"
        except Exception as e:
            return f"❌ 设置音量失败: {str(e)}"
    
    def get_status(self) -> Dict[str, Any]:
        """获取播放状态"""
        status = {
            "is_playing": self.is_playing,
            "is_paused": self.is_paused,
            "current_song": self.current_song['name'] if self.current_song else None,
            "volume": int(self.volume * 100),
            "music_count": len(self.get_music_list())
        }
        return status

# 全局音乐播放器实例
_music_player = MusicPlayer()

def get_music_list() -> str:
    """获取音乐列表"""
    try:
        music_list = _music_player.get_music_list()
        if not music_list:
            return "📁 音乐目录为空或不存在"
        
        result = "🎵 可用音乐列表:\n"
        for i, music in enumerate(music_list, 1):
            result += f"{i}. {music['name']} ({music['format']})\n"
        
        return result
    except Exception as e:
        return f"❌ 获取音乐列表失败: {str(e)}"

def play_music(music_name: str) -> str:
    """播放音乐"""
    return _music_player.play_music(music_name)

def stop_music() -> str:
    """停止播放"""
    return _music_player.stop_music()

def pause_music() -> str:
    """暂停/恢复播放"""
    return _music_player.pause_music()

def set_volume(volume: int) -> str:
    """设置音量 (0-100)"""
    volume_float = volume / 100.0
    return _music_player.set_volume(volume_float)

def get_player_status() -> str:
    """获取播放器状态"""
    try:
        status = _music_player.get_status()
        result = "🎵 音乐播放器状态:\n"
        result += f"播放状态: {'播放中' if status['is_playing'] else '已停止'}\n"
        if status['is_paused']:
            result += "状态: 已暂停\n"
        if status['current_song']:
            result += f"当前歌曲: {status['current_song']}\n"
        result += f"音量: {status['volume']}%\n"
        result += f"音乐库: {status['music_count']} 首歌曲"
        return result
    except Exception as e:
        return f"❌ 获取状态失败: {str(e)}"

def get_tools():
    """返回插件提供的工具定义"""
    return {
        "get_music_list": {
            "function": get_music_list,
            "definition": {
                "type": "function",
                "function": {
                    "name": "get_music_list",
                    "description": "获取Music目录下所有可播放的音乐文件列表，支持mp3、wav、ogg、flac、m4a格式",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "play_music": {
            "function": play_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "play_music",
                    "description": "播放指定的音乐。可以使用歌曲名称（支持模糊匹配）或完整文件名",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "music_name": {
                                "type": "string",
                                "description": "要播放的音乐名称或文件名"
                            }
                        },
                        "required": ["music_name"]
                    }
                }
            }
        },
        "stop_music": {
            "function": stop_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "stop_music",
                    "description": "停止当前播放的音乐",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "pause_music": {
            "function": pause_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "pause_music",
                    "description": "暂停当前播放的音乐，如果已暂停则恢复播放",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "set_volume": {
            "function": set_volume,
            "definition": {
                "type": "function",
                "function": {
                    "name": "set_volume",
                    "description": "设置音乐播放音量，范围0-100",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "volume": {
                                "type": "integer",
                                "description": "音量大小，范围0-100",
                                "minimum": 0,
                                "maximum": 100
                            }
                        },
                        "required": ["volume"]
                    }
                }
            }
        },
        "get_player_status": {
            "function": get_player_status,
            "definition": {
                "type": "function",
                "function": {
                    "name": "get_player_status",
                    "description": "获取音乐播放器的当前状态，包括播放状态、当前歌曲、音量等信息",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        }
    }
