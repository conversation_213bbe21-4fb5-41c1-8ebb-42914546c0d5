"""
音乐播放插件

这个插件提供音乐播放相关的功能，包括：
- 获取音乐列表
- 播放音乐
- 停止播放
- 暂停/恢复播放
- 获取播放状态
- 调节音量

支持的音频格式：mp3, wav, ogg, flac, m4a
"""

import os
import json
import threading
import time
from typing import List, Dict, Any, Optional
import pygame
from pathlib import Path

# 音乐目录路径 - 相对于项目根目录
MUSIC_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "Music")

class MusicPlayer:
    def __init__(self):
        self.current_song = None
        self.is_playing = False
        self.is_paused = False
        self.volume = 0.7
        self.position = 0
        self.playlist = []
        self.current_index = -1
        self.play_mode = "normal"  # normal, repeat_one, repeat_all
        self.auto_play_next = False
        self._music_end_event = None
        self._init_pygame()
        self._setup_music_events()
        
    def _init_pygame(self):
        """初始化pygame音频系统"""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            print("🎵 音乐播放器初始化成功")
        except Exception as e:
            print(f"❌ 音乐播放器初始化失败: {e}")

    def _setup_music_events(self):
        """设置音乐事件处理"""
        try:
            # 设置音乐结束事件
            pygame.mixer.music.set_endevent(pygame.USEREVENT + 1)
        except Exception as e:
            print(f"⚠️ 音乐事件设置失败: {e}")

    def _handle_music_end(self):
        """处理音乐播放结束事件"""
        if self.play_mode == "repeat_one":
            # 单曲循环
            self._repeat_current_song()
        elif self.play_mode == "repeat_all":
            # 列表循环
            self._play_next_in_playlist()
        elif self.auto_play_next:
            # 自动播放下一首
            self._play_next_in_playlist()
        else:
            # 正常模式，停止播放
            self.is_playing = False
            self.current_song = None

    def _repeat_current_song(self):
        """重复播放当前歌曲"""
        if self.current_song:
            try:
                pygame.mixer.music.load(self.current_song['path'])
                pygame.mixer.music.set_volume(self.volume)
                pygame.mixer.music.play()
            except Exception as e:
                print(f"❌ 重复播放失败: {e}")
                self.is_playing = False

    def _play_next_in_playlist(self):
        """播放播放列表中的下一首"""
        if not self.playlist:
            self.playlist = self.get_music_list()

        if self.playlist:
            # 找到当前歌曲在播放列表中的位置
            if self.current_song:
                try:
                    current_index = next(i for i, music in enumerate(self.playlist)
                                       if music['name'] == self.current_song['name'])
                    next_index = (current_index + 1) % len(self.playlist)
                except StopIteration:
                    next_index = 0
            else:
                next_index = 0

            next_song = self.playlist[next_index]
            self.current_index = next_index
            self._play_song_directly(next_song)

    def _play_song_directly(self, song_info):
        """直接播放指定歌曲（内部方法）"""
        try:
            pygame.mixer.music.load(song_info['path'])
            pygame.mixer.music.set_volume(self.volume)
            pygame.mixer.music.play()
            self.current_song = song_info
            self.is_playing = True
            self.is_paused = False
        except Exception as e:
            print(f"❌ 播放歌曲失败: {e}")
            self.is_playing = False

    def set_play_mode(self, mode: str) -> str:
        """设置播放模式"""
        valid_modes = {
            "normal": "正常播放",
            "repeat_one": "单曲循环",
            "repeat_all": "列表循环"
        }

        if mode not in valid_modes:
            return f"❌ 无效的播放模式。可用模式: {', '.join(valid_modes.keys())}"

        self.play_mode = mode
        return f"🔄 播放模式已设置为: {valid_modes[mode]}"

    def play_next(self) -> str:
        """播放下一首"""
        if not self.playlist:
            self.playlist = self.get_music_list()

        if not self.playlist:
            return "📁 音乐目录为空，无法播放下一首"

        if self.current_song:
            try:
                current_index = next(i for i, music in enumerate(self.playlist)
                                   if music['name'] == self.current_song['name'])
                next_index = (current_index + 1) % len(self.playlist)
            except StopIteration:
                next_index = 0
        else:
            next_index = 0

        next_song = self.playlist[next_index]
        self.current_index = next_index
        self._play_song_directly(next_song)
        return f"⏭️ 播放下一首: {next_song['name']} ({next_song['format']})"

    def play_previous(self) -> str:
        """播放上一首"""
        if not self.playlist:
            self.playlist = self.get_music_list()

        if not self.playlist:
            return "📁 音乐目录为空，无法播放上一首"

        if self.current_song:
            try:
                current_index = next(i for i, music in enumerate(self.playlist)
                                   if music['name'] == self.current_song['name'])
                prev_index = (current_index - 1) % len(self.playlist)
            except StopIteration:
                prev_index = len(self.playlist) - 1
        else:
            prev_index = len(self.playlist) - 1

        prev_song = self.playlist[prev_index]
        self.current_index = prev_index
        self._play_song_directly(prev_song)
        return f"⏮️ 播放上一首: {prev_song['name']} ({prev_song['format']})"

    def start_playlist_playback(self) -> str:
        """开始播放列表播放"""
        if not self.playlist:
            self.playlist = self.get_music_list()

        if not self.playlist:
            return "📁 音乐目录为空，无法开始播放列表"

        # 从第一首开始播放
        first_song = self.playlist[0]
        self.current_index = 0
        self.auto_play_next = True
        self._play_song_directly(first_song)
        return f"📋 开始播放列表: {first_song['name']} ({first_song['format']})\n🔄 播放模式: {self._get_play_mode_display()}"
    
    def get_music_list(self) -> List[Dict[str, str]]:
        """获取音乐目录下的所有音乐文件"""
        music_files = []
        supported_formats = {'.mp3', '.wav', '.ogg', '.flac', '.m4a'}
        
        if not os.path.exists(MUSIC_DIR):
            return []
            
        try:
            for file in os.listdir(MUSIC_DIR):
                file_path = os.path.join(MUSIC_DIR, file)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in supported_formats:
                        music_files.append({
                            "name": os.path.splitext(file)[0],
                            "filename": file,
                            "path": file_path,
                            "format": file_ext[1:]
                        })
        except Exception as e:
            print(f"❌ 获取音乐列表失败: {e}")
            
        return sorted(music_files, key=lambda x: x['name'])
    
    def play_music(self, music_name: str) -> str:
        """播放指定的音乐"""
        try:
            music_list = self.get_music_list()
            target_music = None

            # 查找音乐文件 - 支持多种匹配方式
            for music in music_list:
                # 完全匹配文件名
                if music_name == music['filename']:
                    target_music = music
                    break
                # 完全匹配歌曲名
                elif music_name == music['name']:
                    target_music = music
                    break
                # 模糊匹配歌曲名
                elif music_name.lower() in music['name'].lower():
                    target_music = music
                    break

            if not target_music:
                # 提供更友好的错误信息和建议
                available_songs = [music['name'] for music in music_list[:5]]  # 显示前5首作为建议
                suggestion = f"\n💡 可用的音乐包括: {', '.join(available_songs)}"
                if len(music_list) > 5:
                    suggestion += f" 等共{len(music_list)}首"
                return f"❌ 未找到音乐: {music_name}{suggestion}"

            # 停止当前播放
            if self.is_playing:
                pygame.mixer.music.stop()

            # 加载并播放音乐
            pygame.mixer.music.load(target_music['path'])
            pygame.mixer.music.set_volume(self.volume)
            pygame.mixer.music.play()

            self.current_song = target_music
            self.is_playing = True
            self.is_paused = False

            return f"🎵 正在播放: {target_music['name']} ({target_music['format']})"

        except Exception as e:
            return f"❌ 播放音乐失败: {str(e)}"
    
    def stop_music(self) -> str:
        """停止播放音乐"""
        try:
            if self.is_playing or self.is_paused:
                pygame.mixer.music.stop()
                self.is_playing = False
                self.is_paused = False
                self.current_song = None
                return "⏹️ 音乐已停止"
            else:
                return "ℹ️ 当前没有播放音乐"
        except Exception as e:
            return f"❌ 停止音乐失败: {str(e)}"
    
    def pause_music(self) -> str:
        """暂停/恢复播放"""
        try:
            if self.is_playing and not self.is_paused:
                pygame.mixer.music.pause()
                self.is_paused = True
                return "⏸️ 音乐已暂停"
            elif self.is_paused:
                pygame.mixer.music.unpause()
                self.is_paused = False
                return "▶️ 音乐已恢复播放"
            else:
                return "ℹ️ 当前没有播放音乐"
        except Exception as e:
            return f"❌ 暂停/恢复失败: {str(e)}"
    
    def set_volume(self, volume: float) -> str:
        """设置音量 (0.0 - 1.0)"""
        try:
            volume = max(0.0, min(1.0, volume))  # 限制在0-1范围内
            self.volume = volume
            pygame.mixer.music.set_volume(volume)
            return f"🔊 音量已设置为: {int(volume * 100)}%"
        except Exception as e:
            return f"❌ 设置音量失败: {str(e)}"
    
    def get_status(self) -> Dict[str, Any]:
        """获取播放状态"""
        status = {
            "is_playing": self.is_playing,
            "is_paused": self.is_paused,
            "current_song": self.current_song['name'] if self.current_song else None,
            "current_song_format": self.current_song['format'] if self.current_song else None,
            "volume": int(self.volume * 100),
            "music_count": len(self.get_music_list()),
            "music_directory": MUSIC_DIR,
            "play_mode": self.play_mode,
            "play_mode_display": self._get_play_mode_display(),
            "auto_play_next": self.auto_play_next,
            "current_index": self.current_index,
            "playlist_size": len(self.playlist)
        }
        return status

    def _get_play_mode_display(self) -> str:
        """获取播放模式的显示名称"""
        mode_names = {
            "normal": "正常播放",
            "repeat_one": "单曲循环",
            "repeat_all": "列表循环"
        }
        return mode_names.get(self.play_mode, "未知模式")

    def play_random_music(self) -> str:
        """随机播放一首音乐"""
        try:
            import random
            music_list = self.get_music_list()
            if not music_list:
                return "📁 音乐目录为空，无法随机播放"

            random_music = random.choice(music_list)
            return self.play_music(random_music['name'])
        except Exception as e:
            return f"❌ 随机播放失败: {str(e)}"

# 全局音乐播放器实例
_music_player = MusicPlayer()

def get_music_list() -> str:
    """获取音乐列表"""
    try:
        music_list = _music_player.get_music_list()
        if not music_list:
            return "📁 音乐目录为空或不存在"
        
        result = "🎵 可用音乐列表:\n"
        for i, music in enumerate(music_list, 1):
            result += f"{i}. {music['name']} ({music['format']})\n"
        
        return result
    except Exception as e:
        return f"❌ 获取音乐列表失败: {str(e)}"

def play_music(music_name: str) -> str:
    """播放音乐"""
    return _music_player.play_music(music_name)

def stop_music() -> str:
    """停止播放"""
    return _music_player.stop_music()

def pause_music() -> str:
    """暂停/恢复播放"""
    return _music_player.pause_music()

def set_volume(volume: int) -> str:
    """设置音量 (0-100)"""
    volume_float = volume / 100.0
    return _music_player.set_volume(volume_float)

def get_player_status() -> str:
    """获取播放器状态"""
    try:
        status = _music_player.get_status()
        result = "🎵 音乐播放器状态:\n"
        result += f"播放状态: {'播放中' if status['is_playing'] else '已停止'}\n"
        if status['is_paused']:
            result += "状态: 已暂停\n"
        if status['current_song']:
            result += f"当前歌曲: {status['current_song']}"
            if status['current_song_format']:
                result += f" ({status['current_song_format']})"
            result += "\n"
        result += f"播放模式: {status['play_mode_display']}\n"
        result += f"音量: {status['volume']}%\n"
        result += f"音乐库: {status['music_count']} 首歌曲\n"
        if status['playlist_size'] > 0:
            result += f"播放列表: {status['playlist_size']} 首歌曲\n"
            if status['current_index'] >= 0:
                result += f"当前位置: {status['current_index'] + 1}/{status['playlist_size']}\n"
        result += f"音乐目录: {status['music_directory']}"
        return result
    except Exception as e:
        return f"❌ 获取状态失败: {str(e)}"

def play_random_music() -> str:
    """随机播放音乐"""
    return _music_player.play_random_music()

def set_play_mode(mode: str) -> str:
    """设置播放模式"""
    return _music_player.set_play_mode(mode)

def play_next() -> str:
    """播放下一首"""
    return _music_player.play_next()

def play_previous() -> str:
    """播放上一首"""
    return _music_player.play_previous()

def start_playlist() -> str:
    """开始播放列表播放"""
    return _music_player.start_playlist_playback()

def get_tools():
    """返回插件提供的工具定义"""
    return {
        "get_music_list": {
            "function": get_music_list,
            "definition": {
                "type": "function",
                "function": {
                    "name": "get_music_list",
                    "description": "获取Music目录下所有可播放的音乐文件列表，支持mp3、wav、ogg、flac、m4a格式",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "play_music": {
            "function": play_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "play_music",
                    "description": "播放指定的音乐。可以使用歌曲名称（支持模糊匹配）或完整文件名",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "music_name": {
                                "type": "string",
                                "description": "要播放的音乐名称或文件名"
                            }
                        },
                        "required": ["music_name"]
                    }
                }
            }
        },
        "stop_music": {
            "function": stop_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "stop_music",
                    "description": "停止当前播放的音乐",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "pause_music": {
            "function": pause_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "pause_music",
                    "description": "暂停当前播放的音乐，如果已暂停则恢复播放",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "set_volume": {
            "function": set_volume,
            "definition": {
                "type": "function",
                "function": {
                    "name": "set_volume",
                    "description": "设置音乐播放音量，范围0-100",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "volume": {
                                "type": "integer",
                                "description": "音量大小，范围0-100",
                                "minimum": 0,
                                "maximum": 100
                            }
                        },
                        "required": ["volume"]
                    }
                }
            }
        },
        "get_player_status": {
            "function": get_player_status,
            "definition": {
                "type": "function",
                "function": {
                    "name": "get_player_status",
                    "description": "获取音乐播放器的当前状态，包括播放状态、当前歌曲、音量等信息",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "play_random_music": {
            "function": play_random_music,
            "definition": {
                "type": "function",
                "function": {
                    "name": "play_random_music",
                    "description": "从音乐库中随机选择一首歌曲播放",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "set_play_mode": {
            "function": set_play_mode,
            "definition": {
                "type": "function",
                "function": {
                    "name": "set_play_mode",
                    "description": "设置播放模式。可选模式: normal(正常播放), repeat_one(单曲循环), repeat_all(列表循环)",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "mode": {
                                "type": "string",
                                "description": "播放模式: normal, repeat_one, repeat_all",
                                "enum": ["normal", "repeat_one", "repeat_all"]
                            }
                        },
                        "required": ["mode"]
                    }
                }
            }
        },
        "play_next": {
            "function": play_next,
            "definition": {
                "type": "function",
                "function": {
                    "name": "play_next",
                    "description": "播放下一首歌曲",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "play_previous": {
            "function": play_previous,
            "definition": {
                "type": "function",
                "function": {
                    "name": "play_previous",
                    "description": "播放上一首歌曲",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "start_playlist": {
            "function": start_playlist,
            "definition": {
                "type": "function",
                "function": {
                    "name": "start_playlist",
                    "description": "开始播放列表播放，从第一首歌曲开始按顺序播放所有音乐",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        }
    }
