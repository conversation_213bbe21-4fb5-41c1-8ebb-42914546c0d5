# This module will manage all Automatic Speech Recognition (ASR) related functionalities. 

import threading
import time
import traceback

from PyQt5.QtCore import QObject, pyqtSignal

# 安全导入asr模块，避免因缺少依赖导致整个程序无法启动
# asr.py 位于 aipet/ 目录下, 此文件位于 aipet/core/ 目录下, 因此使用相对导入
try:
    from aipet import asr # 使用绝对路径导入
    ASR_AVAILABLE = True
    print("✓ ASRManager: ASR模块导入成功")
except ImportError as e:
    print(f"⚠️ ASRManager: ASR模块导入失败: {e}")
    print("提示: 请安装缺少的依赖: pip install onnxruntime jieba")
    print("语音输入功能将不可用。")
    asr = None
    ASR_AVAILABLE = False

class ASRManager(QObject):
    """
    管理所有自动语音识别 (ASR) 相关的功能。
    包括启动/停止监听线程，处理识别结果，并与主应用控制器通信。
    """
    text_recognized_signal = pyqtSignal(str) # 识别出文本时发出
    status_changed_signal = pyqtSignal(bool)  # ASR 状态变化时发出 (开启/关闭)
    error_signal = pyqtSignal(str) # 发生错误时发出

    def __init__(self, parent=None):
        super().__init__(parent)

        self.asr_thread: threading.Thread | None = None
        self.asr_stop_event = threading.Event()
        self.asr_continue_event = threading.Event()
        self.is_active = False

        if not ASR_AVAILABLE:
            return

        # 将 ASRManager 的事件传递给底层的 asr 模块
        # 假设 asr 模块有一个 set_asr_continue_event 函数
        if hasattr(asr, 'set_asr_continue_event'):
            asr.set_asr_continue_event(self.asr_continue_event)


    def _preload_models(self) -> bool:
        """预加载或检查ASR和VAD模型"""
        if not ASR_AVAILABLE:
            print("ASRManager: ASR模块不可用，跳过模型预加载")
            return False
            
        print("ASRManager: 预加载/检查 ASR 和 VAD 模型...")
        models_ok = True
        try:
            # VAD model check
            if not asr.vad_model:
                print("ASRManager: VAD 模型未加载，尝试加载...")
                if not asr.load_vad_model():
                    print("ASRManager: VAD 模型加载失败。")
                    models_ok = False
            
            # ASR model check
            if not asr.asr_model and models_ok:
                print("ASRManager: ASR 模型未加载，尝试加载...")
                # 使用短静音音频进行预加载测试
                dummy_audio = b'\x00' * (asr.CHUNK * 2 * 1)
                test_result = asr.recognize_audio(dummy_audio)
                if "ASR模型加载失败" in test_result:
                    print("ASRManager: ASR 模型加载失败。")
                    models_ok = False
            
            if models_ok:
                print("ASRManager: ASR 和 VAD 模型检查/加载完毕。")
            else:
                self.error_signal.emit("语音识别模型加载失败，无法启动语音输入。")

            return models_ok
        except Exception as e:
            print(f"ASRManager: 预加载ASR/VAD模型时发生错误: {e}")
            traceback.print_exc()
            self.error_signal.emit(f"预加载模型时出错: {e}")
            return False

    def toggle_listening(self):
        """切换ASR监听状态。由 AppController 调用。"""
        if not ASR_AVAILABLE:
            self.error_signal.emit("ASR模块不可用，请安装依赖: pip install onnxruntime jieba")
            self.status_changed_signal.emit(False)
            return
            
        target_state = not self.is_active
        print(f"ASRManager: 尝试切换ASR状态到: {'开启' if target_state else '关闭'}")

        if target_state: # 尝试开启
            if self.asr_thread is None or not self.asr_thread.is_alive():
                if not self._preload_models():
                    self.status_changed_signal.emit(False) # 确保UI状态正确
                    return

                print("ASRManager: 启动 ASR 监听线程...")
                self.asr_stop_event.clear()
                self.asr_continue_event.set() # 初始时允许ASR运行
                self.asr_thread = threading.Thread(target=self._listen_loop, daemon=True)
                self.asr_thread.start()
                self.is_active = True
                self.status_changed_signal.emit(True)
            else:
                print("ASRManager: ASR 线程已在运行。")
                self.is_active = True # 确保状态一致
                self.status_changed_signal.emit(True)
        else: # 尝试关闭
            if self.asr_thread and self.asr_thread.is_alive():
                print("ASRManager: 停止 ASR 监听线程...")
                self.asr_stop_event.set()
                self.asr_continue_event.set() # 确保如果ASR在等待，也能被唤醒并检测到停止
            else:
                print("ASRManager: ASR 线程未运行或已停止。")
            self.is_active = False
            self.status_changed_signal.emit(False)

    def _listen_loop(self):
        """ASR监听线程的实际目标函数。"""
        if not ASR_AVAILABLE:
            print("[ASR Thread] ASR模块不可用，线程退出")
            return
            
        print("[ASR Thread] ASR 监听线程启动。")
        while not self.asr_stop_event.is_set():
            try:
                audio_data = asr.record_with_vad()

                if self.asr_stop_event.is_set():
                    print("[ASR Thread] 录音被停止事件中断。")
                    break
                if not audio_data:
                    if not self.asr_stop_event.wait(0.05):
                         continue
                    else:
                         break

                print("[ASR Thread] 开始识别音频...")
                recognized_text = asr.recognize_audio(audio_data)

                if self.asr_stop_event.is_set():
                    print("[ASR Thread] 识别后检测到停止。")
                    break

                if recognized_text and recognized_text.strip():
                    if "ASR模型加载失败" in recognized_text or "识别过程中出错" in recognized_text:
                        print(f"[ASR Thread] 识别出错: {recognized_text}")
                        self.error_signal.emit(f"{recognized_text}")
                        # 识别出错后也应该允许下一轮
                        self.allow_next_listening_cycle()
                    else:
                        print(f"[ASR Thread] 识别结果: {recognized_text}")
                        self.text_recognized_signal.emit(recognized_text)

                        # 等待主流程处理完毕
                        print("[ASR Thread] 等待主流程通过 asr_continue_event 放行...")
                        self.asr_continue_event.clear()
                        while not self.asr_stop_event.is_set():
                            if self.asr_continue_event.wait(timeout=0.2):
                                break
                        if self.asr_stop_event.is_set():
                            print("[ASR Thread] 在等待放行时被停止。")
                            break
                        print("[ASR Thread] 主流程已放行。")
                else:
                    # 未识别到文本，自动允许下一轮尝试
                    if not self.asr_stop_event.is_set():
                        self.allow_next_listening_cycle()

            except Exception as e:
                print(f"[ASR Thread] 监听循环发生错误: {e}")
                traceback.print_exc()
                self.error_signal.emit(f"ASR监听循环发生错误: {e}")
                if not self.asr_stop_event.is_set():
                    time.sleep(1)
                else:
                    break
        print("[ASR Thread] ASR 监听线程已退出。")

    def allow_next_listening_cycle(self):
        """
        供外部（如 AppController）调用，以允许 ASR 线程开始下一轮监听。
        通常在 AI 回复和 TTS 播放完成后调用。
        """
        if ASR_AVAILABLE and hasattr(self, 'asr_continue_event'):
            self.asr_continue_event.set()

    def cleanup(self):
        """在应用退出时进行清理。"""
        print("ASRManager: Cleaning up...")
        if self.asr_thread is not None and self.asr_thread.is_alive():
            self.asr_stop_event.set()
            self.asr_continue_event.set()

        if ASR_AVAILABLE:
            try:
                if hasattr(asr, 'cleanup_pyaudio'):
                    asr.cleanup_pyaudio()
            except Exception as e:
                print(f"ASRManager: 调用asr.cleanup_pyaudio时出错: {e}")
        print("ASRManager: Cleanup complete.") 