import os
import re
import html
import time
import logging
import threading
import requests
import socket
import urllib.parse
from datetime import datetime
from typing import Optional

from PyQt5.QtWidgets import QFrame, QHBoxLayout, QVBoxLayout, QLabel, QWidget
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRectF
from PyQt5.QtGui import QPixmap, QPainter, QPainterPath

logger = logging.getLogger(__name__)

class MessageBubble(QFrame):
    """现代化消息气泡"""
    
    # 定义信号用于线程安全的图片加载
    image_loaded_signal = pyqtSignal(object, object)  # placeholder, pixmap
    
    def __init__(self, sender: str, content: str = "", timestamp: datetime = None, parent=None, is_companion_mode: bool = False, avatar_path: Optional[str] = None, role: str = "unknown"):
        super().__init__(parent)
        self.sender = sender.strip()
        self.content = content # 原始传入内容，可能为空
        self.timestamp = timestamp or datetime.now()
        self.role = role.lower() if role else "unknown"
        self.is_user = self.role == "user"
        self.is_ai = self.role == "assistant"
        self.is_system = self.role == "system"
        self.is_companion_mode = is_companion_mode  # 陪玩模式标识
        
        # 优化建议添加的属性
        self.current_content = None # 用于 update_content 优化
        self.last_image_path = None # 用于 set_image 优化
        self._added_images = set() # 用于跟踪已添加的图片，避免重复

        # UI组件
        self.content_label = None  # 将在解析内容时动态创建
        self.content_container = None  # 混合内容容器
        self.content_layout = None  # 混合内容布局
        self.image_label = None  # 兼容性引用
        self.time_label = None
        self.avatar_label = None
        self.avatar_path = avatar_path
        
        # 主题管理器引用
        self.theme_manager = None
        self._find_theme_manager()
        
        self.setup_ui()
        self.apply_styling()
        self.setup_animations()
        
        # 连接信号到槽函数，确保UI更新在主线程中进行
        self.image_loaded_signal.connect(self._handle_image_loaded)
    
    def _find_theme_manager(self):
        """查找主题管理器"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'theme_manager'):
                self.theme_manager = parent.theme_manager
                break
            parent = parent.parent()
    
    def setup_ui(self):
        """设置UI布局 - 优化缓存计算"""
        # 在开头添加空内容检查 (优化建议 1)
        if not self.content and not self.image_label: # 检查初始文本内容和是否有图片标签
             # 如果初始内容为空且没有图片，可以先设置一个最小高度或直接返回
             # 这里我们假设如果内容为空，初始高度应为0，除非后续添加了图片
             # 注意：如果后续调用 set_image，需要确保高度能正确更新
             # 一个更稳妥的方式是允许初始布局，但在 update_content 和 set_image 中处理显隐
             # 暂时按建议，如果初始 content 为空，先不进行复杂布局计算
             # 但这可能导致初始为空，后续 update_content 时布局跳动，需要测试验证
             # 考虑到 MessageBubble 的动画，初始高度为0可能更符合动画效果
             self.setFixedHeight(0) # 尝试设置固定高度为0
             # return # 如果返回，下面的布局代码将不执行

        self.setContentsMargins(0, 0, 0, 0)
        
        # 主容器
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 8, 15, 8)
        
        if self.is_user:
            # 用户消息：右对齐
            main_layout.addStretch()
            self.bubble_container = self.create_user_bubble()
            main_layout.addWidget(self.bubble_container)
        else:
            # AI/系统消息：左对齐
            self.bubble_container = self.create_ai_bubble()
            main_layout.addWidget(self.bubble_container)
            main_layout.addStretch()
    
    def create_user_bubble(self) -> QWidget:
        """创建用户消息气泡"""
        container = QFrame()
        container.setObjectName("userBubbleContainer")
        container.setMaximumWidth(300)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(6)
        
        # 混合内容容器（支持文本和图片混合显示）
        self.content_container = QWidget()
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        self.content_container.setObjectName("userContentContainer")
        
        # 为了兼容性，保留 content_label 和 image_label 引用
        self.content_label = None  # 将在解析内容时动态创建
        self.image_label = self.content_container  # 兼容性引用
        
        # 时间标签
        self.time_label = QLabel(self.timestamp.strftime("%H:%M"))
        self.time_label.setAlignment(Qt.AlignRight)
        self.time_label.setObjectName("userTimeLabel")
        
        layout.addWidget(self.content_container)
        layout.addWidget(self.time_label)
        
        # 设置初始内容
        if self.content:
            self.update_content(self.content)
        
        return container
    
    def create_ai_bubble(self) -> QWidget:
        """创建AI/系统消息气泡"""
        container = QFrame()
        container.setObjectName("aiBubbleContainer")
        container.setMaximumWidth(350)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)
        
        # 头部（头像+发送者名称）
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        # 头像
        self.avatar_label = QLabel()
        self.avatar_label.setFixedSize(48, 48)
        self.avatar_label.setAlignment(Qt.AlignCenter)
        self.avatar_label.setObjectName("avatarLabel")
        self.avatar_label.setStyleSheet("background: transparent; border: none;")
        
        if self.is_ai and self.avatar_path:
            pixmap = QPixmap(self.avatar_path)
            size = 48
            if not pixmap.isNull():
                # 裁剪为圆形并填满label
                square_pixmap = pixmap.scaled(size, size, Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
                rounded = QPixmap(size, size)
                rounded.fill(Qt.transparent)
                painter = QPainter(rounded)
                painter.setRenderHint(QPainter.Antialiasing)
                path = QPainterPath()
                path.addEllipse(0, 0, size, size)
                painter.setClipPath(path)
                painter.drawPixmap(0, 0, square_pixmap)
                painter.end()
                self.avatar_label.setPixmap(rounded)
            else:
                self.avatar_label.setText("🤖")
        elif self.is_ai:
            self.avatar_label.setText("🤖")
        elif self.is_system:
            self.avatar_label.setText("⚙️")
        else:
            self.avatar_label.setText("💬")
        
        # 发送者标签
        sender_label = QLabel(self.sender)
        sender_label.setObjectName("senderLabel")
        
        header_layout.addWidget(self.avatar_label)
        header_layout.addWidget(sender_label)
        header_layout.addStretch()
        
        # 混合内容容器（支持文本和图片混合显示）
        self.content_container = QWidget()
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        self.content_container.setObjectName("aiContentContainer")
        
        # 为了兼容性，保留 content_label 和 image_label 引用
        self.content_label = None  # 将在解析内容时动态创建
        self.image_label = self.content_container  # 兼容性引用
        
        # 底部（时间戳）
        bottom_layout = QHBoxLayout()
        self.time_label = QLabel(self.timestamp.strftime("%H:%M"))
        self.time_label.setObjectName("aiTimeLabel")
        bottom_layout.addStretch()
        bottom_layout.addWidget(self.time_label)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.content_container)
        layout.addLayout(bottom_layout)
        
        # 设置初始内容
        if self.content:
            self.update_content(self.content)
        
        return container
    
    def apply_styling(self):
        """应用样式"""
        if not self.theme_manager:
            logger.warning("ThemeManager not found for MessageBubble.")
            return

        theme = self.theme_manager.get_current_theme()
        
        if self.is_user:
            # 用户消息样式
            user_text_color = "#FFFFFF" # 白色文本，适合深色背景
            user_time_color = "rgba(255, 255, 255, 0.8)"

            style = f"""
            QFrame#userBubbleContainer {{
                background: {theme['bubble_user_bg']}; /* 蓝色渐变背景 */
                border-radius: 18px;
                border: none;
            }}
            QLabel#userContentLabel {{
                color: {user_text_color};       /* 文字颜色设为白色 */
                background-color: transparent; /* 关键：确保 QLabel 背景透明 */
                font-size: 14px;
                line-height: 1.4;
                padding: 2px; /* 可选：微调内边距，防止文字太贴边 */
            }}
            QLabel#userTimeLabel {{
                color: {user_time_color};
                background-color: transparent; /* 时间标签背景也设为透明 */
                font-size: 11px;
            }}
            QWidget#userContentContainer {{
                background-color: transparent; /* 内容容器背景设为透明 */
            }}
            QLabel#mixedContentLabel, QLabel#imagePlaceholder {{
                background-color: transparent; /* 混合内容标签背景也设为透明 */
            }}
            """
        else: # AI 或系统消息
            # AI/系统消息的文本颜色通常是 theme['text_primary']
            # 其气泡背景 theme['bubble_ai_bg'] 或 theme['bubble_system_bg'] 通常是浅色或半透明深色
            # 我们也应该确保这些 QLabel 的背景是透明的，以显示 bubble_container 的背景

            ai_text_color = theme['text_primary']
            ai_time_color = theme['text_tertiary']
            sender_name_color = theme['text_secondary']

            bg_color = theme['bubble_ai_bg']
            if self.is_system:
                bg_color = theme['bubble_system_bg']
            
            style = f"""
            QFrame#aiBubbleContainer {{
                background: {bg_color}; /* AI/系统气泡背景 */
                border-radius: 18px;
                border: 1px solid {theme['border_color']};
            }}
            QLabel#avatarLabel {{ /* 头像的背景可以保持原样 */
                background-color: {theme['primary']};
                border-radius: 16px;
                font-size: 16px;
                color: {theme['text_inverse']};
            }}
            QLabel#senderLabel {{
                color: {sender_name_color};
                background-color: transparent; /* 发送者名称背景透明 */
                font-size: 12px;
                font-weight: 600;
            }}
            QLabel#aiContentLabel {{
                color: {ai_text_color};
                background-color: transparent; /* AI 内容标签背景透明 */
                font-size: 14px;
                line-height: 1.4;
                padding: 2px; /* 可选 */
            }}
            QLabel#aiTimeLabel {{
                color: {ai_time_color};
                background-color: transparent; /* AI 时间标签背景透明 */
                font-size: 11px;
            }}
            QWidget#aiContentContainer {{
                background-color: transparent; /* AI 内容容器背景透明 */
            }}
            QLabel#mixedContentLabel, QLabel#imagePlaceholder {{
                background-color: transparent; /* 混合内容标签背景透明 */
            }}
            """
        
        self.setStyleSheet(style)
    
    def setup_animations(self):
        """设置动画"""
        # 进入动画
        self.setMaximumHeight(0)
        self.expand_animation = QPropertyAnimation(self, b"maximumHeight")
        self.expand_animation.setDuration(300)
        self.expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 延迟启动动画
        QTimer.singleShot(50, self.start_enter_animation)
    
    def start_enter_animation(self):
        """开始进入动画"""
        # 获取实际高度
        self.setMaximumHeight(16777215)  # 移除高度限制
        actual_height = self.sizeHint().height()
        self.setMaximumHeight(0)  # 重新设置为0
        
        # 设置动画
        self.expand_animation.setStartValue(0)
        self.expand_animation.setEndValue(actual_height + 10)  # 加一点余量
        self.expand_animation.finished.connect(lambda: self.setMaximumHeight(16777215))
        self.expand_animation.start()
    
    def update_content(self, new_content: str):
        """更新内容 - 支持文本和图片混合显示"""
        if not hasattr(self, 'content_layout'):
            return
        
        # 只在内容实际改变时更新
        if new_content == self.current_content:
            return
            
        self.current_content = new_content

        # 清空现有内容
        self._clear_content_container()
        
        # 解析并按顺序显示混合内容
        self._parse_and_display_mixed_content(new_content)
        
        # 更新大小
        self.adjustSize()
        
        # 如果有动画正在运行，更新目标高度
        if hasattr(self, 'expand_animation') and self.expand_animation.state() == QPropertyAnimation.Running:
            new_height = self.sizeHint().height()
            self.expand_animation.setEndValue(new_height + 10)
    
    def _update_streaming_text(self, text_content: str):
        """流式更新文本内容（不重建混合内容）"""
        if not hasattr(self, 'content_layout'):
            return
            
        # 在流式更新过程中，寻找或创建一个纯文本标签来显示累积的文本
        streaming_label = None
        
        # 查找是否已经有流式文本标签
        for i in range(self.content_layout.count()):
            widget = self.content_layout.itemAt(i).widget()
            if widget and widget.objectName() == "streamingTextLabel":
                streaming_label = widget
                break
        
        # 如果没有找到，创建一个新的流式文本标签
        if not streaming_label:
            streaming_label = QLabel()
            streaming_label.setWordWrap(True)
            streaming_label.setTextFormat(Qt.RichText)
            streaming_label.setOpenExternalLinks(True)
            streaming_label.setTextInteractionFlags(Qt.TextSelectableByMouse | Qt.LinksAccessibleByMouse)
            streaming_label.setObjectName("streamingTextLabel")
            
            # 设置样式
            if self.is_user:
                streaming_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
            else:
                theme = self.theme_manager.get_current_theme() if self.theme_manager else {}
                text_color = theme.get('text_primary', '#000000')
                streaming_label.setStyleSheet(f"color: {text_color}; background-color: transparent;")
            
            # 添加到内容容器的开头
            self.content_layout.insertWidget(0, streaming_label)
            
            # 为兼容性保留第一个文本标签的引用
            if not self.content_label:
                self.content_label = streaming_label
        
        # 处理并更新文本内容
        processed_content = self._process_content(text_content)
        streaming_label.setText(processed_content)
        
        # 重新调整大小
        self.adjustSize()
    
    def _clean_incomplete_img_tags(self, content: str) -> str:
        """清理不完整的img标签，尝试修复截断的标签"""
        
        # 查找不完整的img标签模式
        # 1. 查找以 <img 开始但没有 > 结尾的标签
        incomplete_start_pattern = r'<img[^>]*$'
        
        # 2. 查找可能的img标签片段，尝试重构完整标签
        # 模式：寻找可能的图片URL片段
        url_pattern = r'(https?://[^\s<>"]+(?:\.(?:jpg|jpeg|png|gif|webp|bmp))?)'
        
        # 先移除明显不完整的开始标签
        cleaned_content = re.sub(incomplete_start_pattern, '', content)
        
        # 查找所有可能的图片URL
        urls = re.findall(url_pattern, cleaned_content)
        
        for url in urls:
            # 检查URL前后是否有img标签的片段
            # 如果找到裸露的URL（前面可能有不完整的img src="），尝试构建完整标签
            url_pos = cleaned_content.find(url)
            if url_pos > 0:
                before_url = cleaned_content[max(0, url_pos-20):url_pos]
                after_url = cleaned_content[url_pos+len(url):url_pos+len(url)+10]
                
                # 检查是否是被截断的img标签
                if 'src="' in before_url or 'src=\'' in before_url:
                    # 构建完整的img标签
                    complete_tag = f'<img src="{url}">'
                    # 替换原来可能不完整的结构
                    pattern = r'<img[^>]*src=["\']?' + re.escape(url) + r'["\']?[^>]*>?'
                    if not re.search(pattern, cleaned_content):
                        # 如果没有找到完整标签，则构建一个
                        cleaned_content = cleaned_content.replace(url, complete_tag)
        
        return cleaned_content
    
    def _finalize_mixed_content(self, final_content: str):
        """智能完成混合内容构建，避免重复清空已加载的图片"""
        if not hasattr(self, 'content_layout'):
            return
            
        # 清理可能的不完整img标签
        cleaned_content = self._clean_incomplete_img_tags(final_content)
        
        # 解析最终内容中的图片
        img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
        final_images = re.findall(img_pattern, cleaned_content)
        
        # 检查当前已加载的图片
        existing_images = set()
        streaming_text_widget = None
        
        for i in range(self.content_layout.count()):
            widget = self.content_layout.itemAt(i).widget()
            if widget:
                if widget.objectName() == "streamingTextLabel":
                    streaming_text_widget = widget
                elif widget.objectName() == "imagePlaceholder":
                    img_url = widget.property("img_url")
                    if img_url:
                        existing_images.add(img_url)
        
        # 如果需要的图片都已存在，只更新文本
        if set(final_images).issubset(existing_images):
            # 只更新流式文本标签的内容为去除图片的最终文本
            final_text = self._remove_img_tags(cleaned_content)
            if streaming_text_widget:
                processed_content = self._process_content(final_text)
                streaming_text_widget.setText(processed_content)
        else:
            # 有新图片需要加载，使用完整重建
            self.update_content(cleaned_content)
        
        # 重新调整大小
        self.adjustSize()
    
    def _process_content(self, content: str) -> str:
        """处理内容，支持简单的markdown和表情"""
        if not content:
            return ""
        
        # HTML转义
        processed = html.escape(content)
        
        # 换行处理
        processed = processed.replace('\n', '<br>')
        
        # 简单的markdown支持
        # 粗体
        processed = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', processed)
        # 斜体
        processed = re.sub(r'\*(.*?)\*', r'<i>\1</i>', processed)
        # 代码
        processed = re.sub(r'`(.*?)`', r'<code style="background-color: rgba(128,128,128,0.2); padding: 2px 4px; border-radius: 3px;">\1</code>', processed)
        
        # 链接处理
        url_pattern = r'(https?://[^\s<>"]+)'
        processed = re.sub(url_pattern, r'<a href="\1" style="color: #007AFF;">\1</a>', processed)
        
        return processed
    
    def _remove_img_tags(self, content: str) -> str:
        """移除HTML img标签（包括不完整的标签）- MessageBubble版本"""
        # 移除完整的img标签
        content = re.sub(r'<img[^>]*>', '', content)
        # 移除不完整的img标签开始部分
        content = re.sub(r'<img[^>]*$', '', content)
        return content
    
    def _clear_content_container(self):
        """清空内容容器"""
        if hasattr(self, 'content_layout') and self.content_layout:
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
    
    def _parse_and_display_mixed_content(self, content: str):
        """解析并按顺序显示文本和图片的混合内容"""
        if not content.strip():
            return
        
        # 使用正则表达式分割内容，保留img标签的位置信息
        img_pattern = r'(<img[^>]*src=["\']([^"\']+)["\'][^>]*>)'
        parts = re.split(img_pattern, content)
          # parts 的结构：[text, img_tag, img_url, text, img_tag, img_url, ...]
        i = 0
        while i < len(parts):
            part = parts[i]
            if not part.strip():
                i += 1
                continue
                
            if part.startswith('<img'):
                # 这是一个图片标签，下一个元素应该是URL
                if i + 1 < len(parts):
                    img_url = parts[i + 1]
                    
                    placeholder = self._create_image_placeholder(img_url)
                    self.content_layout.addWidget(placeholder)
                    
                    # 异步加载图片
                    if img_url:
                        self._load_image_for_placeholder(placeholder, img_url)
                    
                    i += 2  # 跳过img_tag和img_url
                else:
                    i += 1
            elif not part.startswith('http'):  # 跳过单独的URL部分
                # 这是文本内容
                text_content = self._process_content(part)
                if text_content.strip():
                    
                    text_label = QLabel()
                    text_label.setWordWrap(True)
                    text_label.setTextFormat(Qt.RichText)
                    text_label.setOpenExternalLinks(True)
                    text_label.setTextInteractionFlags(Qt.TextSelectableByMouse | Qt.LinksAccessibleByMouse)
                    text_label.setText(text_content)
                    text_label.setObjectName("mixedContentLabel")
                    
                    # 设置样式
                    if self.is_user:
                        text_label.setStyleSheet("color: #FFFFFF; background-color: transparent;")
                    else:
                        theme = self.theme_manager.get_current_theme() if self.theme_manager else {}
                        text_color = theme.get('text_primary', '#000000')
                        text_label.setStyleSheet(f"color: {text_color}; background-color: transparent;")
                    
                    self.content_layout.addWidget(text_label)
                    
                    # 为兼容性保留第一个文本标签的引用
                    if not self.content_label:
                        self.content_label = text_label
                
                i += 1
            else:
                i += 1
    
    def _create_image_placeholder(self, img_url: str) -> QLabel:
        """为图片创建占位符"""
        placeholder = QLabel("🖼️ 加载中...")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("""
            QLabel {
                color: #888;
                background-color: rgba(128, 128, 128, 0.1);
                border: 1px dashed #ccc;
                border-radius: 8px;
                padding: 10px;
                min-height: 60px;
                min-width: 100px;
            }
        """)
        placeholder.setObjectName("imagePlaceholder")
        
        # 保存图片URL到占位符
        placeholder.setProperty("img_url", img_url)
        
        # 添加一个标记，用于验证占位符的有效性
        placeholder.setProperty("creation_time", time.time())
        
        return placeholder
    
    def _is_placeholder_valid(self, placeholder: QLabel) -> bool:
        """检查占位符是否仍然有效"""
        try:
            if not placeholder:
                return False
            if not hasattr(placeholder, 'setPixmap'):
                return False
            if placeholder.isHidden():
                return False
            # 检查创建时间，如果超过5分钟就认为无效
            creation_time = placeholder.property("creation_time")
            if creation_time and time.time() - creation_time > 300:
                return False
            return True
        except Exception:
            return False
    
    def _load_image_for_placeholder(self, placeholder: QLabel, img_url: str):
        """为占位符异步加载图片"""
        # 先验证占位符有效性
        if not placeholder or not hasattr(placeholder, 'setText'):
            return
        
        def load_and_replace():
            
            def show_error(error_msg: str):
                """显示错误信息"""
                def update_error():
                    try:
                        # 使用新的有效性检查方法
                        if self._is_placeholder_valid(placeholder):
                            placeholder.setText(f"🖼️ {error_msg}")
                            placeholder.setStyleSheet("""
                                QLabel {
                                    color: #FF6B6B;
                                    background-color: rgba(255, 107, 107, 0.1);
                                    border: 1px solid #FF6B6B;
                                    border-radius: 8px;
                                    padding: 10px;
                                }
                            """)
                    except Exception:
                        pass
                
                QTimer.singleShot(0, update_error)
            
            pixmap = None
            try:
                # 测试本地服务器连接
                if "localhost" in img_url or "127.0.0.1" in img_url:
                    try:
                        parsed = urllib.parse.urlparse(img_url)
                        host = parsed.hostname or 'localhost'
                        port = parsed.port or 6005
                        
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(5)
                        result = sock.connect_ex((host, port))
                        sock.close()
                        
                        if result != 0:
                            logger.error(f"本地服务器连接失败: {host}:{port}, 错误码: {result}")
                    except Exception:
                        pass
                
                # 发起网络请求
                response = requests.get(img_url, timeout=15, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                })
                
                if response.status_code != 200:
                    show_error(f"HTTP {response.status_code}")
                    return
                
                # 验证下载内容
                if len(response.content) == 0:
                    show_error("图片内容为空")
                    return
                
                # 直接从内存创建QPixmap
                pixmap = QPixmap()
                success = pixmap.loadFromData(response.content)
                
                if not success or pixmap.isNull():
                    show_error("图片格式错误")
                    return
                
                # 使用信号发射来实现线程安全的UI更新
                self.image_loaded_signal.emit(placeholder, pixmap)
                
            except requests.exceptions.Timeout:
                show_error("加载超时")
            except requests.exceptions.ConnectionError:
                show_error("连接失败")
            except Exception:
                show_error("加载失败")
            finally:
                # 清理资源
                if 'response' in locals():
                    try:
                        response.close()
                    except:
                        pass
        
        # 启动异步加载线程
        threading.Thread(target=load_and_replace, daemon=True).start()
    
    def _handle_image_loaded(self, placeholder, pixmap):
        """槽函数：在主线程中处理图片加载完成的信号"""
        try:
            # 使用新的有效性检查方法
            if not self._is_placeholder_valid(placeholder):
                return
            
            # 再次验证pixmap有效性
            if not pixmap or pixmap.isNull():
                placeholder.setText("图片无效")
                return
            
                        # 缩放图片
            scaled_pixmap = pixmap
            if pixmap.width() > 150:
                scaled_pixmap = pixmap.scaledToWidth(150, Qt.SmoothTransformation)
            
            # 添加圆角 - 增加错误处理
            try:
                rounded_pixmap = self._create_rounded_pixmap(scaled_pixmap, 12)
                if rounded_pixmap.isNull():
                    rounded_pixmap = scaled_pixmap
            except Exception:
                rounded_pixmap = scaled_pixmap
            
            # 更新占位符
            placeholder.setPixmap(rounded_pixmap)
            placeholder.setText("")  # 清除"加载中"文本
            placeholder.setStyleSheet("background-color: transparent; border: none;")
            placeholder.adjustSize()
              # 触发父容器重新布局
            try:
                parent = placeholder.parent()
                layout_updated = False
                while parent and not layout_updated:
                    if hasattr(parent, 'adjustSize'):
                        parent.adjustSize()
                        layout_updated = True
                    elif hasattr(parent, 'updateGeometry'):
                        parent.updateGeometry()
                        layout_updated = True
                    parent = parent.parent()
            except Exception:
                pass
            
        except Exception:
            placeholder.setText("显示失败")

    def set_image(self, image_path: str, max_width: int = 200):
        """兼容性方法：添加图片到消息末尾（用于历史记录等场景）"""
        if not hasattr(self, 'content_layout') or not os.path.exists(image_path):
            logger.warning(f"Cannot add image: layout missing or file not found: {image_path}")
            return

        try:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 缩放图片
                if pixmap.width() > max_width:
                    pixmap = pixmap.scaledToWidth(max_width, Qt.SmoothTransformation)
                
                # 添加圆角效果
                rounded_pixmap = self._create_rounded_pixmap(pixmap, 12)
                
                # 创建图片标签并添加到内容容器末尾
                image_label = QLabel()
                image_label.setAlignment(Qt.AlignCenter)
                image_label.setPixmap(rounded_pixmap)
                image_label.adjustSize()
                image_label.setObjectName("standaloneImageLabel")
                
                self.content_layout.addWidget(image_label)
                
                # 重新调整大小
                self.adjustSize()
                
                logger.info(f"Standalone image added successfully: {image_path}")
                
        except Exception as e:
            logger.error(f"Failed to add standalone image: {e}")
    def _create_rounded_pixmap(self, pixmap: QPixmap, radius: int) -> QPixmap:
        """创建圆角图片"""
        try:
            if pixmap.isNull() or pixmap.width() == 0 or pixmap.height() == 0:
                return pixmap
            
            size = pixmap.size()
            rounded = QPixmap(size)
            rounded.fill(Qt.transparent)
            
            painter = QPainter(rounded)
            if not painter.isActive():
                return pixmap
            
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
            
            # 创建圆角路径
            path = QPainterPath()
            rect = QRectF(0, 0, size.width(), size.height())
            path.addRoundedRect(rect, radius, radius)
            
            # 设置裁剪路径并绘制
            painter.setClipPath(path)
            painter.drawPixmap(0, 0, pixmap)
            painter.end()
            
            if rounded.isNull():
                return pixmap
                
            return rounded
            
        except Exception:
            return pixmap  # 出错时返回原图
    
    def clear_images(self):
        """清除所有内容（包括文本和图片）"""
        if hasattr(self, 'content_layout') and self.content_layout:
            # 移除所有内容
            while self.content_layout.count():
                child = self.content_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            
            # 重置content_label引用
            self.content_label = None
            
            # 清理已添加图片的记录
            if hasattr(self, '_added_images'):
                self._added_images.clear()
                
            logger.debug("All mixed content cleared from message bubble")
    
    def add_typing_effect(self, text: str, speed: int = 30):
        """添加打字机效果"""
        if not self.content_label:
            return
        
        self.typing_text = text
        self.typing_index = 0
        
        self.typing_timer = QTimer()
        self.typing_timer.timeout.connect(self._type_next_character)
        self.typing_timer.start(speed)
    
    def _type_next_character(self):
        """打字机效果的下一个字符"""
        if self.typing_index < len(self.typing_text):
            current_text = self.typing_text[:self.typing_index + 1]
            self.update_content(current_text)
            self.typing_index += 1
        else:
            self.typing_timer.stop()
    
    def highlight_briefly(self, duration: int = 1000):
        """短暂高亮"""
        if not self.bubble_container:
            return
        
        # 保存原始样式
        original_style = self.bubble_container.styleSheet()
        
        # 应用高亮样式
        highlight_style = original_style + """
        QFrame {
            border: 2px solid #007AFF;
        }
        """
        self.bubble_container.setStyleSheet(highlight_style)
        
        # 定时恢复
        QTimer.singleShot(duration, lambda: self.bubble_container.setStyleSheet(original_style))