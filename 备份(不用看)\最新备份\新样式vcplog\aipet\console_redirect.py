"""
控制台输出重定向模块
用于屏蔽 Live2D 底层 C++ 代码产生的警告信息
"""

import os
import sys
import contextlib
from typing import Optional


class NullWriter:
    """空写入器，丢弃所有写入的内容"""
    def write(self, txt):
        pass
    
    def flush(self):
        pass


@contextlib.contextmanager
def suppress_console_output():
    """
    上下文管理器：临时屏蔽所有控制台输出
    用于在 Live2D 初始化和模型加载期间屏蔽警告
    """
    # 保存原始的输出流
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    # Windows 特定的底层重定向
    original_handles = None
    if sys.platform == "win32":
        try:
            import ctypes
            from ctypes import wintypes
            
            kernel32 = ctypes.windll.kernel32
            
            # 获取当前句柄
            stdout_handle = kernel32.GetStdHandle(-11)  # STD_OUTPUT_HANDLE
            stderr_handle = kernel32.GetStdHandle(-12)  # STD_ERROR_HANDLE
            
            # 创建 null 设备句柄
            null_handle = kernel32.CreateFileW(
                "NUL",
                0x40000000,  # GENERIC_WRITE
                0x3,         # FILE_SHARE_READ | FILE_SHARE_WRITE
                None,
                0x3,         # OPEN_EXISTING
                0,
                None
            )
            
            if null_handle != -1:
                original_handles = (stdout_handle, stderr_handle, null_handle)
                
                # 重定向到 null 设备
                kernel32.SetStdHandle(-11, null_handle)  # STD_OUTPUT_HANDLE
                kernel32.SetStdHandle(-12, null_handle)  # STD_ERROR_HANDLE
                
        except Exception:
            original_handles = None
    
    try:
        # Python 层面的重定向
        sys.stdout = NullWriter()
        sys.stderr = NullWriter()
        
        yield
        
    finally:
        # 恢复 Python 输出流
        sys.stdout = original_stdout
        sys.stderr = original_stderr
        
        # 恢复 Windows 底层句柄
        if original_handles and sys.platform == "win32":
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                stdout_handle, stderr_handle, null_handle = original_handles
                
                # 恢复原始句柄
                kernel32.SetStdHandle(-11, stdout_handle)  # STD_OUTPUT_HANDLE  
                kernel32.SetStdHandle(-12, stderr_handle)  # STD_ERROR_HANDLE
                
                # 关闭 null 句柄
                kernel32.CloseHandle(null_handle)
                
            except Exception:
                pass


@contextlib.contextmanager  
def suppress_stderr_only():
    """
    上下文管理器：只屏蔽 stderr 输出
    保留 stdout，适合在需要看到正常输出时使用
    """
    # 保存原始的 stderr
    original_stderr = sys.stderr
    
    # Windows 特定的 stderr 重定向
    original_stderr_handle = None
    null_handle = None
    
    if sys.platform == "win32":
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            
            # 获取当前 stderr 句柄
            original_stderr_handle = kernel32.GetStdHandle(-12)  # STD_ERROR_HANDLE
            
            # 创建 null 设备句柄
            null_handle = kernel32.CreateFileW(
                "NUL",
                0x40000000,  # GENERIC_WRITE
                0x3,         # FILE_SHARE_READ | FILE_SHARE_WRITE
                None,
                0x3,         # OPEN_EXISTING
                0,
                None
            )
            
            if null_handle != -1:
                # 重定向 stderr 到 null 设备
                kernel32.SetStdHandle(-12, null_handle)  # STD_ERROR_HANDLE
                
        except Exception:
            original_stderr_handle = None
            null_handle = None
    
    try:
        # Python 层面的 stderr 重定向
        sys.stderr = NullWriter()
        
        yield
        
    finally:
        # 恢复 Python stderr
        sys.stderr = original_stderr
        
        # 恢复 Windows stderr 句柄
        if original_stderr_handle and null_handle and sys.platform == "win32":
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                
                # 恢复原始 stderr 句柄
                kernel32.SetStdHandle(-12, original_stderr_handle)  # STD_ERROR_HANDLE
                
                # 关闭 null 句柄
                kernel32.CloseHandle(null_handle)
                
            except Exception:
                pass