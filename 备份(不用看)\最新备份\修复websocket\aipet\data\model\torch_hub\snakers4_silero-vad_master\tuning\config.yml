jit_model_path: ''  # путь до Silero-VAD модели в формате jit, эта модель будет использована для дообучения. Если оставить поле пустым, то модель будет загружена автоматически
use_torchhub: True  # jit модель будет загружена через torchhub, если True, или через pip, если False

tune_8k: False  # дообучает 16к голову, если False, и 8к голову, если True
train_dataset_path: 'train_dataset_path.feather'  # путь до датасета в формате feather для дообучения, подробности в README
val_dataset_path: 'val_dataset_path.feather'  # путь до датасета в формате feather для валидации, подробности в README
model_save_path: 'model_save_path.jit'  # путь сохранения дообученной модели

noise_loss: 0.5  # коэффициент, применяемый к лоссу на неречевых окнах
max_train_length_sec: 8  # во время тюнинга аудио длиннее будут обрезаны до данного значения
aug_prob: 0.4  # вероятность применения аугментаций к аудио в процессе дообучения

learning_rate: 5e-4  # темп дообучения модели
batch_size: 128  # размер батча при дообучении и валидации
num_workers: 4  # количество потоков, используемых для даталоадеров
num_epochs: 20  # количество эпох дообучения, 1 эпоха = полный прогон тренировочных данных
device: 'cuda'  # cpu или cuda, на чем будет производится дообучение