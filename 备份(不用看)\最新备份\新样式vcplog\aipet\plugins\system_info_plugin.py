"""
System Info Provider Plugin

This plugin does not provide any tools for the AI to call.
Instead, it implements the `get_context()` function, which is called by the
PluginManager before each AI interaction.

It provides essential context like the current system time and the predefined user.
"""

from datetime import datetime
from typing import Optional
import os
import json

def resolve_placeholder(placeholder_name: str) -> Optional[str]:
    """
    Resolves system-related placeholders.
    This plugin can resolve {{系统时间}} and {{当前用户}}.
    
    Args:
        placeholder_name: The name of the placeholder to resolve.
        
    Returns:
        The resolved value as a string, or None if the placeholder is not supported.
    """
    if placeholder_name == "系统时间":
        now = datetime.now()
        return now.strftime("%Y-%m-%d %H:%M:%S")
    
    elif placeholder_name == "当前用户":
        # 从配置文件加载用户名
        try:
            # 构建配置文件的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            aipet_dir = os.path.dirname(current_dir)
            config_path = os.path.join(aipet_dir, "config.json")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("user_name", "（未设置用户名）")
        except (FileNotFoundError, json.JSONDecodeError):
            return "（无法加载用户名）"
        
    # If the placeholder is not recognized, return None
    return None

# Note: This plugin intentionally does not have a `get_tools()` function. 