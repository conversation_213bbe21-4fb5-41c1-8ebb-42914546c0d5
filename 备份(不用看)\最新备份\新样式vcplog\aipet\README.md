# AIPet - AI 智能桌面宠物

![Version](https://img.shields.io/badge/version-1.6.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.8%2B-green.svg)
![License](https://img.shields.io/badge/license-MIT-orange.svg)

一个基于 Live2D 的智能 AI 桌面宠物，支持多种 AI 模型、语音合成、语音识别、插件系统等丰富功能。

![AIPet 预览图](assets/preview.png)

## 🌟 主要特性

### 🎭 Live2D 角色系统
- **多角色支持**：内置芊芊、甘雨、丛雨等多个 Live2D 角色
- **实时动画**：支持眨眼、呼吸、表情变化等自然动画
- **鼠标交互**：视线跟踪、点击响应、拖拽移动
- **自定义模型**：支持导入自定义 Live2D 模型

### 🤖 AI 智能对话
- **多模型支持**：支持 DeepSeek、Gemini、Claude 等多种 AI 模型
- **模型热切换**：无需重启即可切换不同的 AI 模型
- **上下文记忆**：智能话题管理，保持长期对话记忆
- **插件扩展**：丰富的插件系统，支持工具调用

### 🗣️ 语音功能
- **TTS 语音合成**：支持 Edge-TTS、GPT-SoVITS 等多种 TTS 引擎
- **ASR 语音识别**：实时语音输入，支持中英文识别
- **音色自定义**：多种音色选择，支持情感表达
- **播放控制**：支持暂停、继续、停止等播放控制

### 📸 视觉能力
- **屏幕截图**：支持全屏和区域截图功能
- **图像分析**：AI 可以分析和理解图片内容
- **拖拽上传**：支持拖拽图片到聊天窗口

### 🎮 游戏伴侣模式
- **游戏集成**：专为游戏玩家设计的伴侣功能
- **实时互动**：游戏过程中的智能陪伴
- **个性化配置**：可自定义游戏伴侣行为

### 🎨 现代化 UI
- **主题切换**：支持亮色/暗色主题
- **流畅动画**：丰富的 UI 动画效果
- **响应式设计**：自适应不同屏幕尺寸
- **消息气泡**：现代化的聊天界面

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- Windows 10/11 (推荐)
- 至少 4GB RAM
- 支持 OpenGL 的显卡

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/yourusername/aipet.git
cd aipet
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或者
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 复制配置文件模板
cp config/config.env.example config/config.env

# 编辑配置文件，填入你的 API 密钥
notepad config/config.env  # Windows
```

5. **运行程序**
```bash
python aipet/main.py
```

### 配置说明

在 `config/config.env` 文件中配置以下参数：

```env
# AI API 配置
API_BASE_URL=https://api.deepseek.com
API_KEY=your_api_key_here
DEFAULT_API_MODEL_NAME=deepseek-reasoner

# Live2D 配置
LIVE2D_API_PORT=8765

# 其他配置
USER_NAME=your_name
```

## 📖 使用指南

### 基本操作

1. **启动程序**：运行 `python aipet/main.py`
2. **召唤助手**：点击系统托盘图标或使用快捷键
3. **开始对话**：在聊天窗口中输入消息
4. **切换角色**：右键菜单 → 助手管理 → 选择角色
5. **更换模型**：右键菜单 → 选择模型

### 高级功能

#### 插件系统
AIPet 支持丰富的插件扩展：

- **系统信息插件**：获取系统状态信息
- **截图插件**：屏幕截图和图像分析
- **文件操作插件**：文件管理和处理
- **网络插件**：网页抓取和在线查询

使用示例：
```
@screenshot 截取当前屏幕
@weather 查询今天天气
@file 列出桌面文件
```

#### 语音控制
1. **启用语音识别**：设置 → 语音设置 → 开启 ASR
2. **语音对话**：按住空格键说话，松开结束
3. **TTS 设置**：可选择不同的语音引擎和音色

#### 自定义角色
1. 准备 Live2D 模型文件（.model3.json）
2. 助手管理 → 新建助手
3. 选择模型文件和头像
4. 设置系统提示词
5. 保存并切换

## 🔧 开发指南

### 项目结构
```
aipet/
├── main.py                 # 主程序入口
├── chat_window.py          # 聊天窗口主类
├── live2d_widget.py        # Live2D 显示组件
├── assistant_manager.py    # 助手管理器
├── model_manager.py        # 模型管理器
├── plugin_manager.py       # 插件管理器
├── core/                   # 核心模块
│   ├── data_manager.py     # 数据管理
│   ├── tts_manager.py      # TTS 管理
│   └── resource_manager.py # 资源管理
├── ui/                     # UI 组件
│   ├── theme.py           # 主题管理
│   ├── widgets.py         # 自定义组件
│   ├── input_area.py      # 输入区域
│   └── message_bubble.py  # 消息气泡
├── plugins/               # 插件目录
├── config/               # 配置文件
├── assets/               # 静态资源
└── live2dmodle/         # Live2D 模型
```

### 添加新插件

1. **创建插件文件**
```python
# plugins/my_plugin.py
class MyPlugin:
    def __init__(self):
        self.name = "我的插件"
    
    def get_tools(self):
        return [{
            "type": "function",
            "function": {
                "name": "my_function",
                "description": "我的功能描述",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "param": {"type": "string", "description": "参数描述"}
                    }
                }
            }
        }]
    
    def handle_tool_call(self, tool_name, arguments):
        if tool_name == "my_function":
            return f"执行结果: {arguments.get('param')}"
```

2. **注册插件**
在 `plugin_manager.py` 中添加你的插件。

### 添加新 TTS 引擎

1. **继承基类**
```python
from core.tts_manager import TTSEngine

class MyTTSEngine(TTSEngine):
    def synthesize(self, text, voice):
        # 实现语音合成逻辑
        pass
```

2. **注册引擎**
在 `tts_manager.py` 中注册新引擎。

## 🛠️ 故障排除

### 常见问题

**Q: 程序启动后没有显示界面**
A: 检查系统托盘，可能程序已在后台运行。

**Q: Live2D 模型无法加载**
A: 确保模型文件路径正确，检查 OpenGL 支持。

**Q: AI 对话没有响应**
A: 检查 API 密钥和网络连接，查看控制台错误信息。

**Q: 语音功能不工作**
A: 检查音频设备权限，确保麦克风和扬声器正常。

### 日志调试

程序会在控制台输出详细的调试信息：
```bash
python aipet/main.py --debug
```

日志文件位置：`logs/aipet.log`

### 性能优化

1. **减少动画频率**：Live2D 设置 → 降低帧率
2. **关闭不需要的功能**：禁用语音识别或 TTS
3. **清理缓存**：定期清理图片缓存目录

## 📱 PM2 部署

使用 PM2 实现程序的常驻运行：

```bash
# 安装 PM2
npm install -g pm2

# 启动程序
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs aipet

# 重启程序
pm2 restart aipet
```

## 🤝 贡献

欢迎为 AIPet 项目贡献代码！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发指南

- 遵循 PEP 8 代码规范
- 添加适当的注释和文档
- 编写单元测试
- 确保向后兼容性

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Live2D](https://www.live2d.com/) - Live2D 技术支持
- [PyQt5](https://www.riverbankcomputing.com/software/pyqt/) - GUI 框架
- [OpenAI](https://openai.com/) - AI 技术支持
- 所有贡献者和用户的支持

## 📞 联系方式

- 项目主页：[GitHub Repository](https://github.com/yourusername/aipet)
- 问题反馈：[GitHub Issues](https://github.com/yourusername/aipet/issues)
- 讨论交流：[GitHub Discussions](https://github.com/yourusername/aipet/discussions)

---

**享受与你的 AI 桌面宠物的美好时光！** 🎉