#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化 VCPLog 通知系统
集成侧边栏和浮动Toast的完整通知解决方案
"""

import logging
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QSizePolicy
from PyQt5.QtCore import Qt, pyqtSignal, QTimer

from aipet.ui.modern_vcplog_widget import ModernVCPLogWidget
from aipet.ui.floating_toast_widget import FloatingToastManager

logger = logging.getLogger(__name__)

class ModernVCPLogSystem(QWidget):
    """现代化VCPLog通知系统"""
    
    # 信号定义
    status_changed = pyqtSignal(str, str)  # 状态变化信号 (status, message)
    item_count_changed = pyqtSignal(int)  # 项目数量变化信号
    sidebar_toggle_requested = pyqtSignal(bool)  # 侧边栏切换请求
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_sidebar_visible = False
        self.is_dark_theme = False
        self.toast_enabled = True
        self.sidebar_enabled = True
        
        # 初始化组件
        self._init_components()
        self._init_ui()
        self._connect_signals()
        
        logger.info("现代化VCPLog通知系统初始化完成")
    
    def _init_components(self):
        """初始化组件"""
        # 侧边栏组件
        self.sidebar_widget = ModernVCPLogWidget(self)
        
        # Toast管理器
        self.toast_manager = FloatingToastManager()
        
        # 智能显示逻辑：当侧边栏激活时，不显示Toast
        self.smart_display = True
    
    def _init_ui(self):
        """初始化UI"""
        # 主布局
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 添加侧边栏（初始隐藏）
        self.main_layout.addWidget(self.sidebar_widget)
        self.sidebar_widget.hide()
        
        # 设置大小策略
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
    
    def _connect_signals(self):
        """连接信号"""
        # 侧边栏信号
        self.sidebar_widget.status_changed.connect(lambda status: self.status_changed.emit(status, ""))
        self.sidebar_widget.item_count_changed.connect(self.item_count_changed.emit)
    
    def add_vcp_log(self, log_data: Dict[str, Any]):
        """添加VCP日志 - 智能显示逻辑"""
        try:
            # 总是添加到侧边栏
            if self.sidebar_enabled:
                self.sidebar_widget.add_vcp_log(log_data)
            
            # 智能Toast显示逻辑
            should_show_toast = (
                self.toast_enabled and 
                (not self.smart_display or not self.is_sidebar_visible)
            )
            
            if should_show_toast:
                self.toast_manager.show_toast(log_data)
            
            logger.debug(f"VCPLog已处理: 侧边栏={'是' if self.sidebar_enabled else '否'}, "
                        f"Toast={'是' if should_show_toast else '否'}")
            
        except Exception as e:
            logger.error(f"处理VCPLog失败: {e}")
    
    def update_connection_status(self, status: str, message: str = ""):
        """更新连接状态"""
        if self.sidebar_enabled:
            self.sidebar_widget.update_connection_status(status, message)
        
        self.status_changed.emit(status, message)
        logger.info(f"VCPLog连接状态: {status} - {message}")
    
    def toggle_sidebar(self, visible: Optional[bool] = None) -> bool:
        """切换侧边栏显示状态"""
        if visible is None:
            visible = not self.is_sidebar_visible

        self.is_sidebar_visible = visible

        if visible:
            self.sidebar_widget.show()
            self.adjustSize()
        else:
            self.sidebar_widget.hide()

        # 发送切换信号
        self.sidebar_toggle_requested.emit(visible)

        logger.info(f"VCPLog侧边栏{'显示' if visible else '隐藏'}")
        return visible

    def ensure_sidebar_visible(self):
        """确保侧边栏可见"""
        if not self.is_sidebar_visible:
            self.toggle_sidebar(True)
        self.sidebar_widget.show()
        logger.info("强制显示VCPLog侧边栏")
    
    def is_sidebar_active(self) -> bool:
        """检查侧边栏是否激活"""
        return self.is_sidebar_visible
    
    def clear_all_notifications(self):
        """清空所有通知"""
        if self.sidebar_enabled:
            self.sidebar_widget.clear_all_items()
        
        if self.toast_enabled:
            self.toast_manager.clear_all_toasts()
        
        logger.info("已清空所有VCPLog通知")
    
    def expand_all_items(self):
        """展开所有侧边栏项目"""
        if self.sidebar_enabled:
            self.sidebar_widget.expand_all_items()
    
    def collapse_all_items(self):
        """折叠所有侧边栏项目"""
        if self.sidebar_enabled:
            self.sidebar_widget.collapse_all_items()
    
    def update_theme(self, is_dark_theme: bool):
        """更新主题"""
        self.is_dark_theme = is_dark_theme
        
        if self.sidebar_enabled:
            self.sidebar_widget.update_theme(is_dark_theme)
        
        if self.toast_enabled:
            self.toast_manager.update_theme(is_dark_theme)
        
        logger.debug(f"VCPLog主题已更新: {'暗色' if is_dark_theme else '亮色'}")
    
    def set_toast_enabled(self, enabled: bool):
        """设置Toast通知开关"""
        self.toast_enabled = enabled
        
        if not enabled:
            self.toast_manager.clear_all_toasts()
        
        logger.info(f"Toast通知{'启用' if enabled else '禁用'}")
    
    def set_sidebar_enabled(self, enabled: bool):
        """设置侧边栏开关"""
        self.sidebar_enabled = enabled
        
        if not enabled and self.is_sidebar_visible:
            self.toggle_sidebar(False)
        
        logger.info(f"侧边栏{'启用' if enabled else '禁用'}")
    
    def set_smart_display(self, enabled: bool):
        """设置智能显示模式"""
        self.smart_display = enabled
        logger.info(f"智能显示模式{'启用' if enabled else '禁用'}")
    
    def set_max_items(self, max_items: int):
        """设置最大项目数"""
        if self.sidebar_enabled:
            self.sidebar_widget.set_max_items(max_items)
    
    def set_max_toasts(self, max_toasts: int):
        """设置最大Toast数"""
        if self.toast_enabled:
            self.toast_manager.set_max_toasts(max_toasts)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'sidebar_enabled': self.sidebar_enabled,
            'toast_enabled': self.toast_enabled,
            'sidebar_visible': self.is_sidebar_visible,
            'smart_display': self.smart_display,
            'sidebar_item_count': 0,
            'active_toast_count': 0
        }
        
        if self.sidebar_enabled:
            stats['sidebar_item_count'] = self.sidebar_widget.get_item_count()
        
        if self.toast_enabled:
            stats['active_toast_count'] = self.toast_manager.get_active_count()
        
        return stats
    
    def get_sidebar_widget(self) -> ModernVCPLogWidget:
        """获取侧边栏组件"""
        return self.sidebar_widget
    
    def get_toast_manager(self) -> FloatingToastManager:
        """获取Toast管理器"""
        return self.toast_manager


class VCPLogNotificationBar(QWidget):
    """VCPLog通知栏 - 用于替换原有的推送消息组件"""
    
    # 信号定义
    toggle_requested = pyqtSignal()  # 切换请求
    status_changed = pyqtSignal(str)  # 状态变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化现代化通知系统
        self.notification_system = ModernVCPLogSystem(self)
        
        # 设置布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.notification_system)
        
        # 连接信号
        self.notification_system.status_changed.connect(
            lambda status, msg: self.status_changed.emit(f"{status}: {msg}")
        )
        self.notification_system.sidebar_toggle_requested.connect(
            lambda visible: self.toggle_requested.emit()
        )
        
        logger.info("VCPLog通知栏初始化完成")
    
    def add_vcp_message(self, message_data: Dict[str, Any]):
        """添加VCP消息 - 兼容原有接口"""
        self.notification_system.add_vcp_log(message_data)
    
    def update_connection_status(self, status: str, message: str = ""):
        """更新连接状态 - 兼容原有接口"""
        self.notification_system.update_connection_status(status, message)
    
    def toggle_sidebar(self):
        """切换侧边栏"""
        return self.notification_system.toggle_sidebar()
    
    def is_sidebar_visible(self) -> bool:
        """检查侧边栏是否可见"""
        return self.notification_system.is_sidebar_active()
    
    def clear_messages(self):
        """清空消息 - 兼容原有接口"""
        self.notification_system.clear_all_notifications()
    
    def update_theme(self, is_dark_theme: bool):
        """更新主题 - 兼容原有接口"""
        self.notification_system.update_theme(is_dark_theme)
    
    def get_message_count(self) -> int:
        """获取消息数量 - 兼容原有接口"""
        stats = self.notification_system.get_statistics()
        return stats['sidebar_item_count']
    
    def configure(self, **kwargs):
        """配置通知系统"""
        if 'toast_enabled' in kwargs:
            self.notification_system.set_toast_enabled(kwargs['toast_enabled'])
        
        if 'sidebar_enabled' in kwargs:
            self.notification_system.set_sidebar_enabled(kwargs['sidebar_enabled'])
        
        if 'smart_display' in kwargs:
            self.notification_system.set_smart_display(kwargs['smart_display'])
        
        if 'max_items' in kwargs:
            self.notification_system.set_max_items(kwargs['max_items'])
        
        if 'max_toasts' in kwargs:
            self.notification_system.set_max_toasts(kwargs['max_toasts'])
        
        logger.info(f"VCPLog通知栏配置已更新: {kwargs}")
    
    def get_system(self) -> ModernVCPLogSystem:
        """获取通知系统实例"""
        return self.notification_system
