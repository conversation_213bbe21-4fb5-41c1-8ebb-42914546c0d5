{"Version": 3, "Meta": {"PhysicsSettingCount": 23, "TotalInputCount": 59, "TotalOutputCount": 107, "VertexCount": 115, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "左手"}, {"Id": "PhysicsSetting2", "Name": "右手"}, {"Id": "PhysicsSetting3", "Name": "右腿"}, {"Id": "PhysicsSetting4", "Name": "左腿"}, {"Id": "PhysicsSetting5", "Name": "鸭子游"}, {"Id": "PhysicsSetting6", "Name": "鸭子弹簧"}, {"Id": "PhysicsSetting7", "Name": "泡沫"}, {"Id": "PhysicsSetting8", "Name": "泡沫滴落"}, {"Id": "PhysicsSetting9", "Name": "整体"}, {"Id": "PhysicsSetting10", "Name": "动画脚"}, {"Id": "PhysicsSetting11", "Name": "左眼珠"}, {"Id": "PhysicsSetting12", "Name": "右眼珠"}, {"Id": "PhysicsSetting13", "Name": "左高光"}, {"Id": "PhysicsSetting14", "Name": "右高光"}, {"Id": "PhysicsSetting15", "Name": "前发"}, {"Id": "PhysicsSetting16", "Name": "震惊"}, {"Id": "PhysicsSetting17", "Name": "爱你"}, {"Id": "PhysicsSetting18", "Name": "生气"}, {"Id": "PhysicsSetting19", "Name": "问号"}, {"Id": "PhysicsSetting20", "Name": "爱心"}, {"Id": "PhysicsSetting21", "Name": "浴巾"}, {"Id": "PhysicsSetting22", "Name": "水"}, {"Id": "PhysicsSetting23", "Name": "发1"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh67"}, "VertexIndex": 1, "Scale": 60.983, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh67"}, "VertexIndex": 2, "Scale": 51.121, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh67"}, "VertexIndex": 3, "Scale": 41.979, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.94, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh65"}, "VertexIndex": 1, "Scale": 50.164, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh65"}, "VertexIndex": 2, "Scale": 39.987, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh0"}, "VertexIndex": 3, "Scale": 38.561, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh0"}, "VertexIndex": 4, "Scale": 29.695, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh1"}, "VertexIndex": 3, "Scale": 38.561, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh1"}, "VertexIndex": 4, "Scale": 29.695, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.94, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh63"}, "VertexIndex": 1, "Scale": 50.93, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh63"}, "VertexIndex": 2, "Scale": 39.987, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh20"}, "VertexIndex": 3, "Scale": 36.063, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh20"}, "VertexIndex": 4, "Scale": 24.97, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.94, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh70"}, "VertexIndex": 1, "Scale": 50.93, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh70"}, "VertexIndex": 2, "Scale": 39.987, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh70"}, "VertexIndex": 3, "Scale": 36.063, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh70"}, "VertexIndex": 4, "Scale": 24.97, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.94, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 0.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "Param9"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh75"}, "VertexIndex": 1, "Scale": 49.397, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh75"}, "VertexIndex": 2, "Scale": 37.032, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh75"}, "VertexIndex": 3, "Scale": 25.445, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 1, "Scale": 37.989, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 2, "Scale": 22.639, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 3, "Scale": 16.167, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param49"}, "VertexIndex": 1, "Scale": 37.876, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param50"}, "VertexIndex": 2, "Scale": 24.306, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param51"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param52"}, "VertexIndex": 1, "Scale": 30.832, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param53"}, "VertexIndex": 2, "Scale": 23.781, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param55"}, "VertexIndex": 1, "Scale": 48.424, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param57"}, "VertexIndex": 2, "Scale": 33.555, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param33"}, "VertexIndex": 1, "Scale": 107.477, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 1, "Scale": 107.477, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 1, "Scale": 107.477, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 1, "Scale": 107.477, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param37"}, "VertexIndex": 1, "Scale": 107.477, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param58"}, "VertexIndex": 1, "Scale": 58.395, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param59"}, "VertexIndex": 2, "Scale": 37.024, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param62"}, "VertexIndex": 1, "Scale": 58.395, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param63"}, "VertexIndex": 2, "Scale": 37.024, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param60"}, "VertexIndex": 1, "Scale": 75.031, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param61"}, "VertexIndex": 2, "Scale": 57.428, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param64"}, "VertexIndex": 1, "Scale": 75.031, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param65"}, "VertexIndex": 2, "Scale": 57.428, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh216"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh216"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh216"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh227"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh227"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh227"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh217"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh217"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh217"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh224"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh224"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh224"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh219"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh219"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh219"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh228"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh228"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh228"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh222"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh222"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh222"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh218"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh218"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh218"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh229"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh229"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh229"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh272"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh272"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh272"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh271"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh271"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh271"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh267"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh267"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh267"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh268"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh268"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh268"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh269"}, "VertexIndex": 1, "Scale": 58.437, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh269"}, "VertexIndex": 2, "Scale": 36.091, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh269"}, "VertexIndex": 3, "Scale": 26.665, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param72"}, "VertexIndex": 1, "Scale": 51.811, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param73"}, "VertexIndex": 2, "Scale": 36.12, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthOpenY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param76"}, "VertexIndex": 1, "Scale": 49.452, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param77"}, "VertexIndex": 2, "Scale": 32.554, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMouthForm"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param74"}, "VertexIndex": 1, "Scale": 49.858, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param75"}, "VertexIndex": 2, "Scale": 30.805, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh108"}, "VertexIndex": 1, "Scale": 77.787, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh108"}, "VertexIndex": 2, "Scale": 47.381, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh108"}, "VertexIndex": 3, "Scale": 33.101, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param78"}, "VertexIndex": 1, "Scale": 54.57, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param79"}, "VertexIndex": 2, "Scale": 34.075, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param85"}, "VertexIndex": 1, "Scale": 63.658, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param86"}, "VertexIndex": 2, "Scale": 40.373, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 70, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param87"}, "VertexIndex": 1, "Scale": 39.419, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param88"}, "VertexIndex": 2, "Scale": 24.038, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh223"}, "VertexIndex": 1, "Scale": 46.459, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh223"}, "VertexIndex": 2, "Scale": 33.349, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh223"}, "VertexIndex": 3, "Scale": 23.136, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh270"}, "VertexIndex": 1, "Scale": 46.459, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh270"}, "VertexIndex": 2, "Scale": 33.349, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh270"}, "VertexIndex": 3, "Scale": 23.136, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh270"}, "VertexIndex": 4, "Scale": 14.353, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh270"}, "VertexIndex": 4, "Scale": 14.353, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 0.96, "Acceleration": 0.93, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}