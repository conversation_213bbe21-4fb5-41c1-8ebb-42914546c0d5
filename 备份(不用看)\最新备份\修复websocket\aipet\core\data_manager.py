# This module will handle data persistence, including user data and topic history. 

import json
import os
import uuid
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any

class DataManager:
    """
    处理数据持久化，包括用户数据、应用配置和话题历史。
    """
    def __init__(self, user_data_path: str, default_model_name: str):
        self.user_data_path = user_data_path
        self.default_model_name = default_model_name

        # 应用级配置
        self.api_model_name: str = default_model_name
        
        # 对话数据
        self.topics: Dict[str, Dict[str, Any]] = {}
        self.active_topic_id: Optional[str] = None
        
        self._load_data()

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取当前活动话题的对话历史"""
        if self.active_topic_id and self.active_topic_id in self.topics:
            return self.topics[self.active_topic_id].get("conversation_history", [])
        return []

    def update_conversation_history(self, history: List[Dict[str, Any]]):
        """更新当前活动话题的对话历史"""
        if self.active_topic_id and self.active_topic_id in self.topics:
            self.topics[self.active_topic_id]["conversation_history"] = history
            self.topics[self.active_topic_id]["last_updated_at"] = datetime.now(timezone.utc).isoformat()

    def _generate_topic_id(self) -> str:
        return uuid.uuid4().hex

    def create_new_topic(self, title: Optional[str] = None) -> str:
        """创建一个新话题并设为活动话题"""
        # 保存旧话题的历史
        self.update_conversation_history(self.get_conversation_history())

        topic_id = self._generate_topic_id()
        now_iso = datetime.now(timezone.utc).isoformat()
        default_title = f"话题_{now_iso.split('T')[0]}_{now_iso.split('T')[1].split('.')[0].replace(':', '-')}"
        
        new_topic_data = {
            "id": topic_id,
            "title": title or default_title,
            "created_at": now_iso,
            "last_updated_at": now_iso,
            "conversation_history": []
        }
        self.topics[topic_id] = new_topic_data
        self.active_topic_id = topic_id
        return topic_id

    def switch_active_topic(self, topic_id: str) -> bool:
        """切换到指定的话题"""
        if topic_id in self.topics:
            # 在切换前，确保当前话题的历史已更新时间戳
            if self.active_topic_id and self.active_topic_id in self.topics:
                 self.topics[self.active_topic_id]["last_updated_at"] = datetime.now(timezone.utc).isoformat()

            self.active_topic_id = topic_id
            print(f"DataManager: Switched to topic: {self.topics[topic_id].get('title', topic_id)}")
            return True
        else:
            print(f"DataManager: Error - Topic ID '{topic_id}' not found.")
            return False

    def get_all_topics_for_display(self) -> List[Dict[str, str]]:
        """获取所有话题以供显示，按更新时间排序"""
        sorted_topic_ids = sorted(
            self.topics.keys(),
            key=lambda tid: self.topics[tid].get('last_updated_at', '1970-01-01T00:00:00Z'),
            reverse=True
        )
        return [
            {
                "id": tid, 
                "title": self.topics[tid].get("title", "无标题"), 
                "last_updated": self.topics[tid].get("last_updated_at", "")
            }
            for tid in sorted_topic_ids
        ]

    def delete_topic(self, topic_id: str) -> bool:
        """删除指定的话题"""
        if topic_id not in self.topics:
            print(f"DataManager: Topic {topic_id} does not exist, cannot delete.")
            return False
        
        topic_title = self.topics[topic_id].get("title", "无标题")
        del self.topics[topic_id]
        
        # 如果删除的是当前活动话题，需要切换到另一个话题
        if topic_id == self.active_topic_id:
            if not self.topics:
                # 如果是最后一个话题，创建一个新的
                self.create_new_topic(title="新对话")
            else:
                # 切换到最近更新的话题
                try:
                    latest_topic_id = max(
                        self.topics.keys(), 
                        key=lambda tid: self.topics[tid].get('last_updated_at', '1970-01-01T00:00:00Z')
                    )
                    self.active_topic_id = latest_topic_id
                except ValueError:
                    self.active_topic_id = None

        print(f"DataManager: Deleted topic: {topic_title}")
        return True

    def _create_initial_topic_if_needed(self):
        """检查并创建初始话题"""
        if not self.topics:
            self.create_new_topic(title="初始对话")
        elif not self.active_topic_id or self.active_topic_id not in self.topics:
            try:
                latest_topic_id = max(
                    self.topics.keys(), 
                    key=lambda tid: self.topics[tid].get('last_updated_at', '1970-01-01T00:00:00Z')
                )
                self.active_topic_id = latest_topic_id
            except ValueError:
                # 如果所有话题都没有时间戳，则创建一个新的
                self.create_new_topic(title="新对话")

    def _load_data(self):
        """从JSON文件加载数据"""
        try:
            if os.path.exists(self.user_data_path):
                with open(self.user_data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 加载应用配置
                self.api_model_name = data.get("api_model_name", self.default_model_name)
                
                # 加载对话数据
                self.topics = data.get("topics", {})
                self.active_topic_id = data.get("active_topic_id")
                
                self._create_initial_topic_if_needed()
            else:
                self._create_initial_topic_if_needed()
        except (json.JSONDecodeError, IOError) as e:
            print(f"DataManager: Could not read/parse user data file '{self.user_data_path}': {e}. Starting with fresh data.")
            self.topics = {}
            self.active_topic_id = None
            self._create_initial_topic_if_needed()

    def save_data(self):
        """将数据保存到JSON文件"""
        # 更新当前话题的历史记录
        self.update_conversation_history(self.get_conversation_history())

        data_to_save = {
            "api_model_name": self.api_model_name,
            "active_topic_id": self.active_topic_id,
            "topics": self.topics
        }
        try:
            with open(self.user_data_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=4)
        except IOError as e:
            print(f"DataManager: Error saving user data to '{self.user_data_path}': {e}") 