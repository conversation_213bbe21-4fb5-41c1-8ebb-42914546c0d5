from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QComboBox, 
    QDialogButtonBox, QLabel, QPushButton, QMessageBox, QWidget, QSpacerItem, QSizePolicy, QLineEdit,
    QFileDialog
)
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QFont
import os

class TTSSettingsDialog(QDialog):
    """
    TTS设置对话框，用于管理语音合成的各项参数。
    """
    # 信号：当设置被保存时发射，携带一个包含所有设置的字典
    # 例如: {'enabled': True, 'service_id': 'edge_tts', 'speaker': 'zh-CN-XiaoxiaoNeural'}
    settings_changed = pyqtSignal(dict)
    # 信号应在类级别定义
    test_finished = pyqtSignal(bool, str)

    def __init__(self, app_controller, parent=None):
        """
        初始化对话框。

        Args:
            app_controller: AppController的实例，用于获取TTS数据。
            parent: 父级窗口。
        """
        super().__init__(parent)
        if not hasattr(app_controller, 'tts_manager'):
            raise TypeError("传入的 app_controller 对象缺少 tts_manager 属性。")

        self.app_controller = app_controller
        self.tts_manager = app_controller.tts_manager # 为方便起见，直接引用tts_manager
        
        self.initial_settings = {} # 用于"取消"操作
        self.setWindowTitle("语音设置")
        self.setMinimumWidth(400)
        
        # 使用当前窗口的主题
        if hasattr(parent, 'theme_manager'):
            self.theme_manager = parent.theme_manager
            # 此处可以添加更多主题应用代码
            
        self.init_ui()
        self.connect_signals()
        self.load_settings()

    def init_ui(self):
        """初始化UI界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 1. 全局开关
        self.tts_enabled_checkbox = QCheckBox("启用语音合成 (TTS)")
        font = self.tts_enabled_checkbox.font()
        font.setPointSize(11)
        self.tts_enabled_checkbox.setFont(font)
        layout.addWidget(self.tts_enabled_checkbox)

        # 分隔线
        line1 = QWidget()
        line1.setFixedHeight(1)
        line1.setStyleSheet("background-color: rgba(0,0,0,0.1);")
        layout.addWidget(line1)

        # 2. 服务选择
        service_layout = QHBoxLayout()
        service_label = QLabel("选择服务:")
        self.service_combo = QComboBox()
        self.service_combo.setToolTip("选择一个文本转语音服务。")
        service_layout.addWidget(service_label)
        service_layout.addWidget(self.service_combo, 1)
        layout.addLayout(service_layout)

        # 3. 音色选择
        speaker_layout = QHBoxLayout()
        speaker_label = QLabel("选择音色:")
        self.speaker_combo = QComboBox()
        self.speaker_combo.setToolTip("为当前服务选择一个音色。")
        speaker_layout.addWidget(speaker_label)
        speaker_layout.addWidget(self.speaker_combo, 1)
        layout.addLayout(speaker_layout)

        # 4. GAG (GPT-SoVITS) 专属设置区域
        self.gag_specific_widget = QWidget()
        gag_layout = QVBoxLayout(self.gag_specific_widget)
        gag_layout.setContentsMargins(0, 10, 0, 0)
        gag_layout.setSpacing(10)

        # 参考音频
        ref_audio_layout = QHBoxLayout()
        ref_audio_label = QLabel("参考音频:")
        self.ref_audio_path_input = QLineEdit()
        self.ref_audio_path_input.setPlaceholderText("点击右侧按钮选择参考音频文件")
        self.browse_ref_audio_button = QPushButton("浏览...")
        ref_audio_layout.addWidget(ref_audio_label)
        ref_audio_layout.addWidget(self.ref_audio_path_input, 1)
        ref_audio_layout.addWidget(self.browse_ref_audio_button)
        gag_layout.addLayout(ref_audio_layout)

        # 提示文本
        prompt_text_layout = QHBoxLayout()
        prompt_text_label = QLabel("提示文本:")
        self.prompt_text_input = QLineEdit()
        self.prompt_text_input.setPlaceholderText("参考音频中对应的文字")
        prompt_text_layout.addWidget(prompt_text_label)
        prompt_text_layout.addWidget(self.prompt_text_input, 1)
        gag_layout.addLayout(prompt_text_layout)

        # 提示语言
        prompt_lang_layout = QHBoxLayout()
        prompt_lang_label = QLabel("提示语言:")
        self.prompt_lang_combo = QComboBox()
        self.prompt_lang_combo.addItems(["zh", "en", "ja"]) # 预置常用语言
        prompt_lang_layout.addWidget(prompt_lang_label)
        prompt_lang_layout.addWidget(self.prompt_lang_combo)
        prompt_lang_layout.addStretch(1)
        gag_layout.addLayout(prompt_lang_layout)
        
        layout.addWidget(self.gag_specific_widget)
        self.gag_specific_widget.setVisible(False) # 默认隐藏

        # 5. 测试区域
        test_layout = QHBoxLayout()
        test_layout.setContentsMargins(0, 10, 0, 0)
        test_label = QLabel("测试文本:")
        self.test_text_input = QLineEdit("你好，这是一个测试。")
        self.test_text_input.setPlaceholderText("输入要测试的文本")
        self.test_button = QPushButton("测试")
        self.test_button.setToolTip("测试当前选中的服务和音色是否可用。")
        test_layout.addWidget(test_label)
        test_layout.addWidget(self.test_text_input, 1)
        test_layout.addWidget(self.test_button)
        layout.addLayout(test_layout)
        
        # 增加垂直伸缩项
        layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 5. 底部按钮
        self.button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        layout.addWidget(self.button_box)

    def connect_signals(self):
        """连接所有信号和槽"""
        self.button_box.accepted.connect(self.save_and_accept)
        self.button_box.rejected.connect(self.reject)
        self.test_button.clicked.connect(self.test_tts)
        self.service_combo.currentIndexChanged.connect(self._on_service_changed)
        self.speaker_combo.currentIndexChanged.connect(self._on_speaker_changed)
        self.browse_ref_audio_button.clicked.connect(self._browse_for_ref_audio)

        # 当参考设置被用户编辑后，立即保存
        self.ref_audio_path_input.editingFinished.connect(self._on_reference_changed)
        self.prompt_text_input.editingFinished.connect(self._on_reference_changed)

        # 在这里只连接一次信号
        self.tts_manager.speaker_reference_updated_signal.connect(self.update_gag_reference_fields)
        self.test_finished.connect(self._on_test_finished)

    def load_settings(self):
        """从TTSManager加载当前设置并更新UI"""
        is_enabled = self.tts_manager.get_tts_enabled()
        self.tts_enabled_checkbox.setChecked(is_enabled)
        
        self.tts_enabled_checkbox.stateChanged.connect(self._toggle_controls_enabled)

        self.service_combo.clear()
        services = self.tts_manager.get_available_tts_services()
        current_service = self.tts_manager.get_current_tts_service()
        current_service_id = current_service['id'] if current_service else None

        if not services:
            self.service_combo.addItem("无可用服务")
            self.service_combo.setEnabled(False)
            return

        for service in services:
            self.service_combo.addItem(service['name'], userData=service['id'])

        idx = self.service_combo.findData(current_service_id)
        if idx != -1:
            self.service_combo.setCurrentIndex(idx)
        else:
            if self.service_combo.count() > 0:
                self.service_combo.setCurrentIndex(0)
        
        self._toggle_controls_enabled(self.tts_enabled_checkbox.isChecked())
        self._on_service_changed()
    
    def _toggle_controls_enabled(self, state):
        """根据复选框状态启用或禁用其他控件"""
        is_enabled = bool(state)
        self.service_combo.setEnabled(is_enabled)
        self.speaker_combo.setEnabled(is_enabled)
        self.test_button.setEnabled(is_enabled)
        self.test_text_input.setEnabled(is_enabled)
        self.gag_specific_widget.setEnabled(is_enabled)

    def _on_service_changed(self):
        """当服务选择变化时，更新音色列表和特定于服务的UI"""
        service_id = self.service_combo.currentData()
        if not service_id:
            self.speaker_combo.clear()
            self.gag_specific_widget.setVisible(False)
            return

        current_service_info = self.tts_manager.get_service_info(service_id)
        is_gag_service = current_service_info and current_service_info.type == 'gpt_sovits'
        self.gag_specific_widget.setVisible(is_gag_service)

        self.speaker_combo.clear()
        speakers = self.tts_manager.get_tts_service_speakers(service_id)
        current_speaker = self.tts_manager.get_current_speaker_for_service(service_id)

        if speakers:
            self.speaker_combo.addItems(speakers)
            if current_speaker and current_speaker in speakers:
                self.speaker_combo.setCurrentText(current_speaker)
        
        self._on_speaker_changed()
        
    def _on_speaker_changed(self):
        """当音色/模型选择变化时，触发后端更新，后端会通过信号来更新UI"""
        service_id = self.service_combo.currentData()
        speaker_name = self.speaker_combo.currentText()

        if not service_id or not speaker_name:
            return
            
        self.tts_manager.set_tts_speaker(service_id, speaker_name)
        
    def _on_reference_changed(self):
        """当参考设置输入框被用户编辑后，保存更改"""
        service_id = self.service_combo.currentData()
        current_service_info = self.tts_manager.get_service_info(service_id)

        if not (current_service_info and current_service_info.type == 'gpt_sovits'):
            return

        self.tts_manager.update_current_speaker_reference(
            audio_path=self.ref_audio_path_input.text(),
            text=self.prompt_text_input.text()
        )

    def _browse_for_ref_audio(self):
        """打开文件对话框选择参考音频"""
        start_dir = os.path.dirname(self.ref_audio_path_input.text()) if self.ref_audio_path_input.text() else os.path.expanduser("~")
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择参考音频", start_dir, "音频文件 (*.wav *.mp3 *.flac);;所有文件 (*)"
        )
        if file_path:
            self.ref_audio_path_input.setText(file_path)
            self._on_reference_changed()

    def update_gag_reference_fields(self, audio_path: str, text: str):
        """一个公共槽函数，用于从外部(如信号)更新GAG参考设置UI。"""
        # 临时断开editingFinished信号，防止在程序设置文本时触发_on_reference_changed
        try:
            self.ref_audio_path_input.editingFinished.disconnect(self._on_reference_changed)
            self.prompt_text_input.editingFinished.disconnect(self._on_reference_changed)
        except TypeError:
            pass # 忽略信号未连接的错误

        self.ref_audio_path_input.setText(audio_path)
        self.prompt_text_input.setText(text)

        # 重新连接信号
        self.ref_audio_path_input.editingFinished.connect(self._on_reference_changed)
        self.prompt_text_input.editingFinished.connect(self._on_reference_changed)

    def save_and_accept(self):
        """保存所有设置并关闭对话框"""
        self.tts_manager.set_tts_enabled(self.tts_enabled_checkbox.isChecked())
        
        service_id = self.service_combo.currentData()
        if service_id:
            self.tts_manager.set_tts_service(service_id)
            speaker_name = self.speaker_combo.currentText()
            if speaker_name:
                self.tts_manager.set_tts_speaker(service_id, speaker_name)

        self.accept()

    def test_tts(self):
        """测试当前的TTS设置"""
        self.test_button.setEnabled(False)
        self.test_button.setText("测试中...")

        service_id = self.service_combo.currentData()
        speaker = self.speaker_combo.currentText()
        text = self.test_text_input.text()
        
        kwargs_to_pass = {}
        if self.gag_specific_widget.isVisible():
            kwargs_to_pass['ref_audio_path'] = self.ref_audio_path_input.text()
            kwargs_to_pass['prompt_text'] = self.prompt_text_input.text()

        # 在单独的线程中运行测试，以防UI阻塞
        import threading
        def test_thread():
            success, message = self.tts_manager.test_tts_service(
                service_id=service_id,
                text=text,
                speaker=speaker,
                **kwargs_to_pass
            )
            self.test_finished.emit(success, message)
        
        threading.Thread(target=test_thread, daemon=True).start()

    def _on_test_finished(self, success, message):
        """处理测试完成后的UI反馈"""
        QMessageBox.information(self, "测试结果", message)
        self.test_button.setEnabled(True)
        self.test_button.setText("测试")

# 用于独立测试该对话框的示例代码
if __name__ == '__main__':
    # 创建一个模拟的AppController，用于测试
    class MockAppController:
        def get_tts_enabled(self):
            return True
        def get_available_tts_services(self):
            return [
                {'id': 'edge_tts', 'name': 'Edge TTS (在线)'},
                {'id': 'vits_local', 'name': 'VITS (本地)'}
            ]
        def get_current_tts_service(self):
            return {'id': 'edge_tts', 'name': 'Edge TTS (在线)'}
        def get_tts_service_speakers(self, service_id):
            if service_id == 'edge_tts':
                return ['zh-CN-XiaoxiaoNeural', 'zh-CN-YunxiNeural', 'zh-CN-YunjianNeural']
            elif service_id == 'vits_local':
                return ['苏瑶', '派蒙']
            return []
        def get_current_speaker_for_service(self, service_id):
            if service_id == 'edge_tts':
                return 'zh-CN-YunxiNeural'
            return '苏瑶'
        def set_current_speaker_for_service(self, service_id, speaker):
            print(f"为服务 {service_id} 保存音色: {speaker}")
            return True
        def test_tts_service(self, service_id, speaker, test_text):
            print(f"测试服务: {service_id}, 音色: {speaker}, 文本: {test_text}")
            # 模拟成功失败
            return 'Neural' in speaker or '苏瑶' in speaker

    app = QApplication(sys.argv)
    mock_controller = MockAppController()
    dialog = TTSSettingsDialog(app_controller=mock_controller)
    
    # 连接信号以打印结果
    dialog.settings_changed.connect(lambda s: print("设置已保存:", s))
    
    dialog.exec() 