#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浮动 Toast 通知组件
基于 VCPChat 设计的现代化浮动通知实现
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QSizePolicy, QApplication, QDesktopWidget,
    QGraphicsOpacityEffect, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, 
    QRect, QParallelAnimationGroup
)
from PyQt5.QtGui import QFont, QColor

logger = logging.getLogger(__name__)

class FloatingToastNotification(QFrame):
    """浮动 Toast 通知项"""
    
    # 信号定义
    close_requested = pyqtSignal()  # 关闭请求
    
    def __init__(self, log_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.log_data = log_data
        self.is_closing = False
        
        # 解析数据
        self._parse_log_data()
        
        # 设置基本属性
        self.setFrameStyle(QFrame.NoFrame)
        self.setFixedWidth(320)
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        # 移除透明背景属性，确保背景不透明
        # self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAutoFillBackground(True)
        
        # 初始化UI
        self._init_ui()
        self._apply_modern_styles()
        self._setup_animations()
        
        # 设置自动关闭
        self.auto_close_timer = QTimer()
        self.auto_close_timer.timeout.connect(self.close_with_animation)
        self.auto_close_timer.setSingleShot(True)
        
        logger.debug(f"创建浮动Toast通知: {self.title_text}")
    
    def _parse_log_data(self):
        """解析日志数据 - 复用 ModernVCPLogItem 的解析逻辑"""
        self.title_text = 'VCP 通知:'
        self.main_content = ''
        self.timestamp = datetime.now()
        
        # 简化的解析逻辑，适合Toast显示
        if (self.log_data and isinstance(self.log_data, dict) and 
            self.log_data.get('type') == 'vcp_log' and 
            isinstance(self.log_data.get('data'), dict)):
            
            vcp_data = self.log_data['data']
            if vcp_data.get('tool_name') and vcp_data.get('status'):
                self.title_text = f"{vcp_data['tool_name']} {vcp_data['status']}"
                
                if 'content' in vcp_data:
                    content = str(vcp_data['content'])
                    # Toast只显示前100个字符
                    self.main_content = content[:100] + "..." if len(content) > 100 else content
                else:
                    self.main_content = '(无内容)'
        
        elif (self.log_data and isinstance(self.log_data, dict) and 
              self.log_data.get('type') == 'daily_note_created'):
            
            note_data = self.log_data.get('data', {})
            self.title_text = f"日记: {note_data.get('maidName', 'N/A')}"
            self.main_content = note_data.get('message', '日记已创建')[:100]
        
        else:
            # 通用处理
            self.title_text = 'VCP 消息:'
            if isinstance(self.log_data, dict):
                self.main_content = str(self.log_data)[:100]
            else:
                self.main_content = str(self.log_data)[:100]
    
    def _init_ui(self):
        """初始化UI布局"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 12, 15, 12)
        self.main_layout.setSpacing(8)
        
        # 头部区域
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)
        
        # 图标
        self.icon_label = QLabel("🔔")
        self.icon_label.setFixedSize(20, 20)
        self.icon_label.setAlignment(Qt.AlignCenter)
        
        # 标题
        self.title_label = QLabel(self.title_text)
        self.title_label.setFont(QFont("微软雅黑", 10, QFont.Bold))
        self.title_label.setWordWrap(True)
        
        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(20, 20)
        self.close_btn.setToolTip("关闭")
        self.close_btn.clicked.connect(self.close_with_animation)
        
        header_layout.addWidget(self.icon_label)
        header_layout.addWidget(self.title_label, 1)
        header_layout.addWidget(self.close_btn)
        
        # 内容区域
        if self.main_content:
            self.content_label = QLabel(self.main_content)
            self.content_label.setFont(QFont("微软雅黑", 9))
            self.content_label.setWordWrap(True)
            self.content_label.setMaximumHeight(80)
            self.main_layout.addLayout(header_layout)
            self.main_layout.addWidget(self.content_label)
        else:
            self.main_layout.addLayout(header_layout)
        
        # 时间戳
        time_str = self.timestamp.strftime("%H:%M:%S")
        self.time_label = QLabel(time_str)
        self.time_label.setFont(QFont("Consolas", 8))
        self.time_label.setAlignment(Qt.AlignRight)
        self.main_layout.addWidget(self.time_label)
        
        # 点击关闭
        self.mousePressEvent = lambda event: self.close_with_animation()
    
    def _apply_modern_styles(self):
        """应用现代化样式 - 强制白色背景"""
        self.setStyleSheet("""
            FloatingToastNotification {
                background-color: white !important;
                border: 3px solid #2196F3 !important;
                border-radius: 12px;
                color: black !important;
            }

            QLabel {
                background-color: white !important;
                color: black !important;
                font-weight: bold;
                font-size: 12px;
            }

            QPushButton {
                background-color: #f0f0f0 !important;
                border: 1px solid #ccc !important;
                border-radius: 8px;
                color: black !important;
                font-weight: bold;
                font-size: 11px;
                padding: 4px;
            }

            QPushButton:hover {
                background-color: #e0e0e0 !important;
                color: black !important;
            }
        """)

        # 强制设置背景色
        self.setAutoFillBackground(True)
        palette = self.palette()
        palette.setColor(self.backgroundRole(), Qt.white)
        self.setPalette(palette)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
    
    def _setup_animations(self):
        """设置动画效果"""
        # 滑入动画
        self.slide_in_animation = QPropertyAnimation(self, b"geometry")
        self.slide_in_animation.setDuration(400)
        self.slide_in_animation.setEasingCurve(QEasingCurve.OutBack)
        
        # 滑出动画
        self.slide_out_animation = QPropertyAnimation(self, b"geometry")
        self.slide_out_animation.setDuration(300)
        self.slide_out_animation.setEasingCurve(QEasingCurve.InBack)
        self.slide_out_animation.finished.connect(self.hide)
        
        # 透明度动画
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)
    
    def show_at_position(self, x: int, y: int, auto_close_delay: int = 7000):
        """在指定位置显示Toast"""
        # 调整大小
        self.adjustSize()
        
        # 设置初始位置（屏幕右侧外）
        screen_width = QDesktopWidget().screenGeometry().width()
        start_x = screen_width
        final_x = x
        
        start_rect = QRect(start_x, y, self.width(), self.height())
        final_rect = QRect(final_x, y, self.width(), self.height())
        
        # 设置初始位置并显示
        self.setGeometry(start_rect)
        self.show()
        
        # 执行滑入动画
        self.slide_in_animation.setStartValue(start_rect)
        self.slide_in_animation.setEndValue(final_rect)
        self.slide_in_animation.start()
        
        # 设置自动关闭
        if auto_close_delay > 0:
            self.auto_close_timer.start(auto_close_delay)
        
        logger.debug(f"Toast通知已显示在位置 ({final_x}, {y})")
    
    def close_with_animation(self):
        """带动画的关闭"""
        if self.is_closing:
            return
        
        self.is_closing = True
        self.auto_close_timer.stop()
        
        # 获取当前位置
        current_rect = self.geometry()
        
        # 滑出到右侧
        screen_width = QDesktopWidget().screenGeometry().width()
        end_rect = QRect(screen_width, current_rect.y(), current_rect.width(), current_rect.height())
        
        # 执行滑出动画
        self.slide_out_animation.setStartValue(current_rect)
        self.slide_out_animation.setEndValue(end_rect)
        self.slide_out_animation.finished.connect(self.close_requested.emit)
        self.slide_out_animation.start()
        
        logger.debug("Toast通知开始关闭动画")
    
    def update_theme(self, is_dark_theme: bool):
        """更新主题"""
        if is_dark_theme:
            self.setStyleSheet("""
                FloatingToastNotification {
                    background-color: rgb(45, 49, 66);
                    border: 2px solid rgb(100, 181, 246);
                    border-radius: 12px;
                    color: rgb(255, 255, 255);
                }

                QLabel {
                    background: transparent;
                    color: rgb(255, 255, 255);
                    font-weight: 600;
                }

                QPushButton {
                    background-color: rgb(79, 86, 102);
                    border: 1px solid rgb(150, 150, 150);
                    border-radius: 10px;
                    color: rgb(255, 255, 255);
                    font-weight: bold;
                    font-size: 12px;
                }

                QPushButton:hover {
                    background-color: rgb(244, 67, 54);
                    color: white;
                }
            """)
        else:
            self._apply_modern_styles()


class FloatingToastManager(QWidget):
    """浮动Toast管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.active_toasts = []  # 活跃的Toast列表
        self.max_toasts = 5  # 最大同时显示数量
        self.toast_spacing = 10  # Toast之间的间距
        self.is_dark_theme = False
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_ShowWithoutActivating)
        
        logger.info("浮动Toast管理器初始化完成")
    
    def show_toast(self, log_data: Dict[str, Any], auto_close_delay: int = 7000):
        """显示Toast通知"""
        try:
            # 如果已达到最大数量，移除最旧的
            if len(self.active_toasts) >= self.max_toasts:
                oldest_toast = self.active_toasts[0]
                oldest_toast.close_with_animation()
            
            # 创建新的Toast
            toast = FloatingToastNotification(log_data, self)
            toast.close_requested.connect(lambda: self._remove_toast(toast))
            toast.update_theme(self.is_dark_theme)
            
            # 计算显示位置
            screen = QDesktopWidget().screenGeometry()
            x = screen.width() - toast.width() - 20
            y = self._calculate_toast_y_position()
            
            # 显示Toast
            toast.show_at_position(x, y, auto_close_delay)
            self.active_toasts.append(toast)
            
            logger.info(f"显示Toast通知: {log_data.get('type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"显示Toast通知失败: {e}")
    
    def _calculate_toast_y_position(self) -> int:
        """计算Toast的Y位置"""
        base_y = 60  # 距离顶部的基础距离
        
        # 计算已有Toast占用的高度
        total_height = 0
        for toast in self.active_toasts:
            total_height += toast.height() + self.toast_spacing
        
        return base_y + total_height
    
    def _remove_toast(self, toast: FloatingToastNotification):
        """移除Toast"""
        if toast in self.active_toasts:
            self.active_toasts.remove(toast)
            toast.setParent(None)
            toast.deleteLater()
            
            # 重新排列剩余的Toast
            self._rearrange_toasts()
    
    def _rearrange_toasts(self):
        """重新排列Toast位置"""
        base_y = 60
        current_y = base_y
        
        for toast in self.active_toasts:
            if not toast.is_closing:
                # 平滑移动到新位置
                current_rect = toast.geometry()
                new_rect = QRect(current_rect.x(), current_y, current_rect.width(), current_rect.height())
                
                # 创建移动动画
                move_animation = QPropertyAnimation(toast, b"geometry")
                move_animation.setDuration(200)
                move_animation.setEasingCurve(QEasingCurve.OutCubic)
                move_animation.setStartValue(current_rect)
                move_animation.setEndValue(new_rect)
                move_animation.start()
                
                current_y += toast.height() + self.toast_spacing
    
    def clear_all_toasts(self):
        """清空所有Toast"""
        for toast in self.active_toasts[:]:  # 复制列表避免修改时出错
            toast.close_with_animation()
    
    def update_theme(self, is_dark_theme: bool):
        """更新主题"""
        self.is_dark_theme = is_dark_theme
        
        # 更新所有活跃Toast的主题
        for toast in self.active_toasts:
            toast.update_theme(is_dark_theme)
    
    def set_max_toasts(self, max_toasts: int):
        """设置最大Toast数量"""
        self.max_toasts = max_toasts
        
        # 如果当前数量超过限制，移除多余的
        while len(self.active_toasts) > self.max_toasts:
            oldest_toast = self.active_toasts[0]
            oldest_toast.close_with_animation()
    
    def get_active_count(self) -> int:
        """获取活跃Toast数量"""
        return len(self.active_toasts)
