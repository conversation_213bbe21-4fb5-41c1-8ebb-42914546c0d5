# -*- coding: utf-8 -*-
"""
推送消息项组件
用于显示单个推送消息的UI组件
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
                            QPushButton, QFrame, QSizePolicy, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPalette

logger = logging.getLogger(__name__)

class PushMessageItem(QFrame):
    """单个推送消息项组件"""
    
    # 信号定义
    remove_requested = pyqtSignal(str)  # 请求移除消息信号
    
    def __init__(self, message_data, parent=None):
        super().__init__(parent)
        self.message_data = message_data
        self.is_expanded = False
        
        # 设置基本属性
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 改为Preferred让它自适应高度
        
        # 初始化UI
        self._init_ui()
        self._apply_styles()
        
        # 判断是否需要自动展开（长消息自动展开）
        content = self.message_data.get('content', '')
        if len(content) > 100:  # 超过100字符的消息自动展开
            QTimer.singleShot(100, self.expand)  # 延迟展开，确保UI初始化完成
        else:
            self.setFixedHeight(60)  # 短消息保持紧凑显示
        
        logger.debug(f"创建推送消息项: {message_data.get('type', 'Unknown')}")
    
    def _init_ui(self):
        """初始化UI布局"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 6, 8, 6)
        self.main_layout.setSpacing(4)
        
        # 折叠状态的头部布局
        self._create_header_layout()
        
        # 展开状态的内容布局
        self._create_content_layout()
        
        # 初始时隐藏内容
        self.content_widget.hide()
    
    def _create_header_layout(self):
        """创建头部布局（折叠状态显示）"""
        self.header_widget = QWidget()
        self.header_layout = QHBoxLayout(self.header_widget)
        self.header_layout.setContentsMargins(0, 0, 0, 0)
        self.header_layout.setSpacing(8)
        
        # 状态指示器
        self.status_label = QLabel(self._get_status_indicator())
        self.status_label.setFixedSize(16, 16)
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # 消息类型和时间
        self.type_label = QLabel(self.message_data.get('type', 'Unknown'))
        self.type_label.setFont(QFont("微软雅黑", 9, QFont.Bold))
        
        # 时间标签
        timestamp = self.message_data.get('timestamp', datetime.now())
        time_str = timestamp.strftime("%H:%M:%S")
        self.time_label = QLabel(time_str)
        self.time_label.setFont(QFont("Consolas", 8))
        
        # 预览内容 - 增加预览长度
        preview_text = self._get_preview_text(max_length=80)  # 增加到80字符
        self.preview_label = QLabel(preview_text)
        self.preview_label.setFont(QFont("微软雅黑", 8))
        self.preview_label.setWordWrap(True)  # 允许换行
        self.preview_label.setMaximumWidth(300)  # 限制预览标签最大宽度
        self.preview_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 展开/折叠按钮
        self.toggle_button = QPushButton("▼")
        self.toggle_button.setFixedSize(20, 20)
        self.toggle_button.clicked.connect(self.toggle_expanded)
        
        # 添加到布局
        self.header_layout.addWidget(self.status_label)
        self.header_layout.addWidget(self.type_label)
        self.header_layout.addWidget(self.time_label)
        self.header_layout.addWidget(self.preview_label, 1)
        self.header_layout.addWidget(self.toggle_button)
        
        self.main_layout.addWidget(self.header_widget)
    
    def _create_content_layout(self):
        """创建内容布局（展开状态显示）"""
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 8, 0, 0)
        self.content_layout.setSpacing(6)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        self.content_layout.addWidget(separator)
        
        # 完整消息内容 - 使用QTextEdit以支持长文本和滚动
        content_text = self.message_data.get('content', '')
        self.content_text_edit = QTextEdit()
        self.content_text_edit.setPlainText(content_text)
        self.content_text_edit.setReadOnly(True)
        self.content_text_edit.setFont(QFont("微软雅黑", 9))
        self.content_text_edit.setMaximumHeight(200)  # 设置最大高度，超出时显示滚动条
        self.content_text_edit.setMaximumWidth(400)   # 限制最大宽度，防止撑宽窗口
        self.content_text_edit.setLineWrapMode(QTextEdit.WidgetWidth)  # 启用换行
        self.content_text_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # 详细信息
        self._create_details_section()
        
        # 操作按钮
        self._create_action_buttons()
        
        self.content_layout.addWidget(self.content_text_edit)
        self.main_layout.addWidget(self.content_widget)
    
    def _create_details_section(self):
        """创建详细信息区域"""
        extra_data = self.message_data.get('extra_data', {})
        if not extra_data:
            return
        
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(0, 4, 0, 4)
        details_layout.setSpacing(2)
        
        # 显示额外信息
        for key, value in extra_data.items():
            if value and key not in ['original_content']:  # 跳过空值和原始内容
                detail_label = QLabel(f"{key}: {value}")
                detail_label.setFont(QFont("Consolas", 8))
                detail_label.setStyleSheet("color: #666666;")
                details_layout.addWidget(detail_label)
        
        if details_layout.count() > 0:
            self.content_layout.addWidget(details_widget)
    
    def _create_action_buttons(self):
        """创建操作按钮"""
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 4, 0, 0)
        button_layout.setSpacing(8)
        
        # 复制按钮
        copy_button = QPushButton("复制")
        copy_button.setFixedSize(60, 24)
        copy_button.clicked.connect(self._copy_content)
        
        # 删除按钮
        remove_button = QPushButton("删除")
        remove_button.setFixedSize(60, 24)
        remove_button.clicked.connect(self._request_remove)
        
        # 右对齐按钮
        button_layout.addStretch()
        button_layout.addWidget(copy_button)
        button_layout.addWidget(remove_button)
        
        self.content_layout.addWidget(button_widget)
    
    def _get_status_indicator(self):
        """获取状态指示器"""
        status = self.message_data.get('status', 'info')
        indicator_map = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        return indicator_map.get(status, 'ℹ️')
    
    def _get_preview_text(self, max_length=80):
        """获取预览文本"""
        content = self.message_data.get('content', '')
        
        # 清理换行符和多余空格
        preview = content.replace('\n', ' ').replace('\r', ' ')
        preview = ' '.join(preview.split())  # 移除多余空格
        
        if len(preview) > max_length:
            preview = preview[:max_length] + "..."
        
        return preview
    
    def _apply_styles(self):
        """应用样式"""
        # 基础样式
        self.setStyleSheet("""
            PushMessageItem {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            PushMessageItem:hover {
                background-color: #e9ecef;
            }
        """)
        
        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #ffffff;
                border: 1px solid #ced4da;
                border-radius: 3px;
                padding: 2px 8px;
                font-size: 8pt;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """
        
        # 应用按钮样式
        if hasattr(self, 'toggle_button'):
            self.toggle_button.setStyleSheet(button_style)
    
    def toggle_expanded(self):
        """切换展开/折叠状态"""
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()
    
    def expand(self):
        """展开消息"""
        if self.is_expanded:
            return
        
        self.is_expanded = True
        self.toggle_button.setText("▲")
        
        # 显示内容区域
        self.content_widget.show()
        
        # 移除固定高度限制，让组件自适应内容
        self.setFixedHeight(16777215)
        self.setMaximumHeight(16777215)
        
        # 更新几何信息
        self.updateGeometry()
        self.adjustSize()
        
        logger.debug(f"展开推送消息项: {self.message_data.get('type', 'Unknown')}")
    
    def collapse(self):
        """折叠消息"""
        if not self.is_expanded:
            return
        
        self.is_expanded = False
        self.toggle_button.setText("▼")
        
        # 隐藏内容区域
        self.content_widget.hide()
        
        # 恢复紧凑高度
        self.setFixedHeight(60)
        
        logger.debug(f"折叠推送消息项: {self.message_data.get('type', 'Unknown')}")
    
    def _copy_content(self):
        """复制消息内容到剪贴板"""
        from PyQt5.QtWidgets import QApplication
        content = self.message_data.get('content', '')
        QApplication.clipboard().setText(content)
        logger.info("消息内容已复制到剪贴板")
    
    def _request_remove(self):
        """请求移除此消息"""
        message_id = self.message_data.get('id', '')
        if message_id:
            self.remove_requested.emit(message_id)
            logger.debug(f"请求移除消息: {message_id}")
    
    def get_message_id(self):
        """获取消息ID"""
        return self.message_data.get('id', '')
    
    def update_theme(self, is_dark_theme=False):
        """更新主题样式"""
        if is_dark_theme:
            self.setStyleSheet("""
                PushMessageItem {
                    background-color: #2d3142;
                    border: 1px solid #4f5666;
                    border-radius: 4px;
                    color: #ffffff;
                }
                PushMessageItem:hover {
                    background-color: #3d4252;
                }
            """)
        else:
            self._apply_styles()  # 使用默认亮色主题