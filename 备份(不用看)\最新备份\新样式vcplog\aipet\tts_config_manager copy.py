import os
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class TTSServiceInfo:
    """TTS服务信息数据类"""
    id: str
    name: str
    type: str
    config_file: str
    enabled: bool
    description: str = ""

class TTSConfigManager:
    """TTS服务配置管理器"""
    
    def __init__(self, config_base_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_base_path: 配置文件基础路径，如果为None则使用脚本所在目录
        """
        if config_base_path is None:
            # 获取当前脚本所在目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.config_base_path = script_dir
        else:
            self.config_base_path = config_base_path
            
        self.main_config_path = os.path.join(self.config_base_path, "configs", "tts_services_config.json")
        self.main_config: Dict[str, Any] = {}
        self.service_configs: Dict[str, Dict[str, Any]] = {}
        self.available_services: List[TTSServiceInfo] = []
        self.current_service_id: str = ""
        
        # 加载配置
        self.load_all_configs()
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        config_dir = os.path.join(self.config_base_path, "configs")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            print(f"创建配置目录: {config_dir}")
    
    def _resolve_config_path(self, relative_path: str) -> str:
        """解析配置文件路径为绝对路径"""
        if os.path.isabs(relative_path):
            return relative_path
        return os.path.join(self.config_base_path, relative_path)
    
    def load_main_config(self) -> bool:
        """加载主配置文件"""
        try:
            if not os.path.exists(self.main_config_path):
                print(f"错误: 主配置文件不存在: {self.main_config_path}")
                return False
            
            with open(self.main_config_path, 'r', encoding='utf-8') as f:
                self.main_config = json.load(f)
            
            # 验证必要字段
            required_fields = ["services", "current_service_id"]
            for field in required_fields:
                if field not in self.main_config:
                    print(f"错误: 主配置文件缺少必要字段: {field}")
                    return False
            
            # 解析服务列表
            self.available_services = []
            for service_data in self.main_config["services"]:
                try:
                    service_info = TTSServiceInfo(
                        id=service_data["id"],
                        name=service_data["name"],
                        type=service_data["type"],
                        config_file=service_data["config_file"],
                        enabled=service_data.get("enabled", True),
                        description=service_data.get("description", "")
                    )
                    self.available_services.append(service_info)
                except KeyError as e:
                    print(f"警告: 服务配置缺少必要字段: {e}")
                    continue
            
            self.current_service_id = self.main_config["current_service_id"]
            print(f"✓ 主配置加载成功，共 {len(self.available_services)} 个服务")
            return True
            
        except json.JSONDecodeError as e:
            print(f"错误: 主配置文件JSON格式错误: {e}")
            return False
        except Exception as e:
            print(f"错误: 加载主配置文件失败: {e}")
            return False
    
    def load_service_config(self, service_info: TTSServiceInfo) -> bool:
        """加载单个服务的配置文件"""
        try:
            config_path = self._resolve_config_path(service_info.config_file)
            
            if not os.path.exists(config_path):
                print(f"警告: 服务配置文件不存在: {config_path}")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证服务类型匹配
            if "service_type" in config_data and config_data["service_type"] != service_info.type:
                print(f"警告: 服务 {service_info.id} 的类型不匹配")
            
            self.service_configs[service_info.id] = config_data
            
            # 如果是 gpt_sovits 服务，则扫描模型
            if service_info.type == 'gpt_sovits':
                self._scan_and_update_gag_models(service_info.id, config_data)

            print(f"✓ 服务配置加载成功: {service_info.name}")
            return True
            
        except json.JSONDecodeError as e:
            print(f"错误: 服务配置文件JSON格式错误 ({service_info.id}): {e}")
            return False
        except Exception as e:
            print(f"错误: 加载服务配置失败 ({service_info.id}): {e}")
            return False
    
    def load_all_configs(self) -> bool:
        """加载所有配置文件"""
        print("加载TTS服务配置...")
        
        # 确保配置目录存在
        self._ensure_config_dir()
        
        # 加载主配置
        if not self.load_main_config():
            return False
        
        # 加载各服务配置
        success_count = 0
        for service_info in self.available_services:
            if service_info.enabled and self.load_service_config(service_info):
                success_count += 1
        
        print(f"✓ TTS配置加载完成: {success_count}/{len(self.available_services)} 个服务可用")
        
        # 验证当前服务是否可用
        if self.current_service_id not in self.service_configs:
            # 如果当前服务不可用，切换到第一个可用服务
            available_ids = list(self.service_configs.keys())
            if available_ids:
                old_service = self.current_service_id
                self.current_service_id = available_ids[0]
                print(f"警告: 当前服务 {old_service} 不可用，已切换到 {self.current_service_id}")
                self.save_current_service()
            else:
                print("错误: 没有可用的TTS服务")
                return False
        
        return success_count > 0
    
    def get_available_services(self) -> List[TTSServiceInfo]:
        """获取所有可用的TTS服务列表"""
        return [service for service in self.available_services 
                if service.enabled and service.id in self.service_configs]
    
    def get_current_service_info(self) -> Optional[TTSServiceInfo]:
        """获取当前服务信息"""
        for service in self.available_services:
            if service.id == self.current_service_id:
                return service
        return None
    
    def get_current_service_config(self) -> Optional[Dict[str, Any]]:
        """获取当前服务的详细配置"""
        return self.service_configs.get(self.current_service_id)
    
    def get_service_config(self, service_id: str) -> Optional[Dict[str, Any]]:
        """获取指定服务的配置"""
        return self.service_configs.get(service_id)

    def get_available_speakers_for_service(self, service_id: str) -> List[str]:
        """获取指定服务所有可用的音色/模型列表"""
        service_config = self.get_service_config(service_id)
        if not service_config:
            return []

        # 兼容新的 gpt_sovits 类型
        service_type = service_config.get("service_type")
        if service_type == 'gpt_sovits':
            models = service_config.get("models", [])
            return [model.get("name", "未知模型") for model in models]

        # 原有逻辑
        if "speakers" in service_config:
            return service_config["speakers"]
            
        return []

    def get_current_speaker_for_service(self, service_id: str) -> Optional[str]:
        """获取指定服务的当前选用音色/模型"""
        service_config = self.get_service_config(service_id)
        if service_config:
            # 兼容新的 gpt_sovits 类型
            service_type = service_config.get("service_type")
            if service_type == 'gpt_sovits':
                return service_config.get("current_model_name")

            # 优先返回 specific_speaker，其次是 default_speaker
            return service_config.get("current_speaker") or service_config.get("default_speaker")
        return None

    def set_current_speaker_for_service(self, service_id: str, speaker_or_model_name: str) -> bool:
        """为指定服务设置当前音色/模型并保存"""
        if service_id not in self.service_configs:
            print(f"错误: 服务 {service_id} 不存在，无法设置")
            return False
            
        try:
            # 兼容 gpt_sovits
            service_type = self.service_configs[service_id].get("service_type")
            if service_type == 'gpt_sovits':
                self.service_configs[service_id]["current_model_name"] = speaker_or_model_name
            else:
                self.service_configs[service_id]["current_speaker"] = speaker_or_model_name
            
            # 找到该服务的配置文件路径
            service_info = next((s for s in self.available_services if s.id == service_id), None)
            if not service_info:
                print(f"错误: 无法找到服务 {service_id} 的元信息")
                return False

            config_path = self._resolve_config_path(service_info.config_file)
            
            # 读取整个JSON，更新字段，然后写回
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.service_configs[service_id], f, ensure_ascii=False, indent=2)

            print(f"✓ 服务 {service_id} 的音色/模型已更新为: {speaker_or_model_name}")
            return True
        except Exception as e:
            print(f"错误: 保存服务 {service_id} 的音色/模型失败: {e}")
            return False

    def get_models_for_service(self, service_id: str) -> List[Dict[str, str]]:
        """获取 gpt_sovits 服务可用的模型列表"""
        service_config = self.get_service_config(service_id)
        if service_config and service_config.get("service_type") == 'gpt_sovits':
            return service_config.get("models", [])
        return []

    def get_current_model_for_service(self, service_id: str) -> Optional[Dict[str, str]]:
        """获取 gpt_sovits 服务当前选中的模型配置"""
        service_config = self.get_service_config(service_id)
        if not service_config or service_config.get("service_type") != 'gpt_sovits':
            return None
        
        current_model_name = service_config.get("current_model_name")
        if not current_model_name:
            return None
            
        models = service_config.get("models", [])
        for model in models:
            if model.get("name") == current_model_name:
                return model
        return None

    def set_gag_service_defaults(self, service_id: str, gag_settings: Dict[str, str]) -> bool:
        """设置并保存 GAG 服务的默认值（如参考音频路径等）"""
        if service_id not in self.service_configs:
            print(f"错误: GAG服务 {service_id} 不存在，无法设置")
            return False
        
        service_config = self.service_configs[service_id]
        if service_config.get("service_type") != 'gpt_sovits':
            print(f"错误: 服务 {service_id} 不是 gpt_sovits 类型")
            return False

        try:
            # 更新内存中的配置
            service_config["default_ref_audio_path"] = gag_settings.get("ref_audio_path", "")
            service_config["default_prompt_text"] = gag_settings.get("prompt_text", "")
            service_config["default_prompt_lang"] = gag_settings.get("prompt_lang", "zh")

            # 保存到文件
            service_info = next((s for s in self.available_services if s.id == service_id), None)
            if not service_info:
                print(f"错误: 无法找到服务 {service_id} 的元信息")
                return False
            
            config_path = self._resolve_config_path(service_info.config_file)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(service_config, f, ensure_ascii=False, indent=2)

            print(f"✓ GAG服务 {service_id} 的默认设置已更新")
            return True
        except Exception as e:
            print(f"错误: 保存GAG服务 {service_id} 的默认设置失败: {e}")
            return False

    def set_current_service(self, service_id: str) -> bool:
        """设置当前使用的TTS服务"""
        if service_id not in self.service_configs:
            print(f"错误: 服务 {service_id} 不存在或未加载")
            return False
        
        # 检查服务是否启用
        service_info = None
        for service in self.available_services:
            if service.id == service_id:
                service_info = service
                break
        
        if not service_info or not service_info.enabled:
            print(f"错误: 服务 {service_id} 未启用")
            return False
        
        old_service = self.current_service_id
        self.current_service_id = service_id
        
        # 保存到配置文件
        if self.save_current_service():
            print(f"✓ TTS服务已切换: {old_service} -> {service_id}")
            return True
        else:
            # 如果保存失败，回滚
            self.current_service_id = old_service
            print(f"错误: 切换TTS服务失败，已回滚到 {old_service}")
            return False
    
    def save_current_service(self) -> bool:
        """保存当前服务设置到配置文件"""
        try:
            self.main_config["current_service_id"] = self.current_service_id
            
            with open(self.main_config_path, 'w', encoding='utf-8') as f:
                json.dump(self.main_config, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"错误: 保存配置文件失败: {e}")
            return False
    
    def get_global_settings(self) -> Dict[str, Any]:
        """获取全局设置"""
        return self.main_config.get("global_settings", {})
    
    def get_tts_enabled(self) -> bool:
        """获取TTS功能的全局启用状态"""
        # 默认为 True，如果 "global_settings" 或 "tts_enabled" 不存在
        return self.get_global_settings().get("tts_enabled", True)
    
    def set_tts_enabled(self, enabled: bool) -> bool:
        """设置TTS功能的全局启用状态并保存"""
        try:
            if "global_settings" not in self.main_config:
                self.main_config["global_settings"] = {}
            
            if self.main_config["global_settings"].get("tts_enabled") == enabled:
                return True # 状态未改变，无需保存

            self.main_config["global_settings"]["tts_enabled"] = enabled
            
            with open(self.main_config_path, 'w', encoding='utf-8') as f:
                json.dump(self.main_config, f, ensure_ascii=False, indent=2)
            
            print(f"✓ TTS 全局状态已更新: {'启用' if enabled else '禁用'}")
            return True
        except Exception as e:
            print(f"错误: 保存 TTS 全局状态失败: {e}")
            return False
    
    def validate_service_config(self, service_id: str) -> bool:
        """验证服务配置的完整性"""
        config = self.get_service_config(service_id)
        if not config:
            return False
        
        service_info = None
        for service in self.available_services:
            if service.id == service_id:
                service_info = service
                break
        
        if not service_info:
            return False
        
        # 根据服务类型验证必要字段
        if service_info.type == "local_http_vits":
            required_fields = ["api_base_url", "default_speaker"]
        elif service_info.type == "huggingface_space_vits":
            required_fields = ["api_base_url", "generate_endpoint", "default_speaker"]
        elif service_info.type == "edge_tts_python_lib":
            required_fields = ["default_speaker", "audio_format"]
        elif service_info.type == "gpt_sovits":
            required_fields = ["api_base_url", "gpt_model_dir_v2", "sovits_model_dir_v2"]
        else:
            print(f"警告: 未知的服务类型: {service_info.type}")
            return False
        
        for field in required_fields:
            if field not in config:
                print(f"错误: 服务 {service_id} 配置缺少必要字段: {field}")
                return False
        
        return True
    
    def reload_configs(self) -> bool:
        """重新加载所有配置"""
        print("重新加载TTS配置...")
        self.service_configs.clear()
        self.available_services.clear()
        return self.load_all_configs()

    def _scan_and_update_gag_models(self, service_id: str, service_config: Dict[str, Any]):
        """
        扫描 GAG (GPT-SoVITS) 服务的模型，并将任何新的发现添加到配置文件中的现有列表中。
        它不会删除手动添加但未在磁盘上找到的模型。
        """
        print(f"扫描 GAG 服务 [{service_id}] 的模型...")
        project_root = os.path.dirname(self.config_base_path) # aipet/ -> project root
        
        # 1. 获取现有模型及其名称
        existing_models = service_config.get("models", [])
        existing_model_names = {model.get("name") for model in existing_models}
        
        model_versions = {
            "v2": {
                "gpt": service_config.get("gpt_model_dir_v2"),
                "sovits": service_config.get("sovits_model_dir_v2")
            },
            "v4": {
                "gpt": service_config.get("gpt_model_dir_v4"),
                "sovits": service_config.get("sovits_model_dir_v4")
            }
        }
        
        newly_discovered_count = 0
        for version, paths in model_versions.items():
            gpt_dir_rel = paths.get("gpt")
            sovits_dir_rel = paths.get("sovits")

            if not gpt_dir_rel or not sovits_dir_rel:
                continue

            gpt_dir_abs = os.path.join(project_root, gpt_dir_rel)
            sovits_dir_abs = os.path.join(project_root, sovits_dir_rel)

            if not os.path.isdir(gpt_dir_abs) or not os.path.isdir(sovits_dir_abs):
                print(f"警告: GAG 模型目录不存在 (版本: {version}): {gpt_dir_abs} 或 {sovits_dir_abs}")
                continue

            try:
                gpt_files_on_disk = os.listdir(gpt_dir_abs)
                sovits_files_on_disk = os.listdir(sovits_dir_abs)
            except Exception as e:
                print(f"ERROR: Could not list files in directory: {e}")
                continue

            gpt_files = {os.path.splitext(f)[0]: os.path.join(gpt_dir_abs, f) for f in gpt_files_on_disk if f.endswith('.ckpt')}
            sovits_files = {os.path.splitext(f)[0]: os.path.join(sovits_dir_abs, f) for f in sovits_files_on_disk if f.endswith('.pth')}
            
            for name, gpt_path in gpt_files.items():
                if name in sovits_files:
                    sovits_path = sovits_files[name]
                    model_name = f"{name} ({version})"

                    # 2. 如果发现的模型不在现有列表中，则添加它
                    if model_name not in existing_model_names:
                        model_entry = {
                            "name": model_name,
                            "version": version,
                            "gpt_path": gpt_path.replace('\\\\', '/').replace('\\', '/'),
                            "sovits_path": sovits_path.replace('\\\\', '/').replace('\\', '/'),
                        }
                        existing_models.append(model_entry)
                        existing_model_names.add(model_name) # 添加到集合以避免在同一次扫描中重复添加
                        newly_discovered_count += 1
        
        # 3. 使用合并后的列表更新配置
        service_config["models"] = existing_models
        
        if newly_discovered_count > 0:
            print(f"✓ 新增了 {newly_discovered_count} 个扫描发现的 GAG 模型。")
        else:
            print(f"✓ 模型扫描完成，未发现新的 GAG 模型。")