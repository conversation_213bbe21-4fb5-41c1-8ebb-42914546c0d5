好的，完全理解！我们聚焦于一件事，分步进行。

这是根据您的 main.py 和我们的重构计划，为您生成的完整、可直接使用的 aipet/core/tts_manager.py 文件。我已经将所有相关的TTS逻辑、方法和状态都迁移到了这个新类中。

文件: aipet/core/tts_manager.py

请在 aipet/core/ 目录下创建 tts_manager.py 文件，并将以下所有内容复制进去。

# aipet/core/tts_manager.py

import os
import queue
import re
import threading
import time
import io
import json
import traceback
from urllib.parse import urljoin, url_quote
from typing import Optional, List, Dict, Any, Tuple

import asyncio
import edge_tts
import requests

from PyQt5.QtCore import QObject, pyqtSignal, QUrl
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

# 安全导入pydub，如果失败则在需要时提示
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False


class TTSManager(QObject):
    """
    管理所有与文本转语音(TTS)相关的功能，包括：
    - 服务配置管理
    - 语音合成 (通过不同服务)
    - 音频播放与口型同步
    - 异步任务队列 (文本 -> 音频)
    """

    # 当有音频块合成完毕，准备播放时发出
    audio_chunk_ready_signal = pyqtSignal()
    # 当切换音色后，需要更新UI上的参考音频/文本时发出
    speaker_reference_updated_signal = pyqtSignal(str, str)

    def __init__(self, app_controller, parent=None):
        super().__init__(parent)
        self.app_controller = app_controller

        # 从主控制器获取依赖
        self.tts_config_manager = self.app_controller.tts_config_manager
        self.temp_file_manager = self.app_controller.temp_file_manager
        # 通过 app_controller 访问 media_player
        self.player = self.app_controller.media_player

        # --- TTS 流水线和状态 ---
        self.tts_cancellation_event = threading.Event()
        self.tts_sentence_queue = self.app_controller.tts_sentence_queue
        self.tts_audio_queue = self.app_controller.tts_audio_queue
        self.playback_lock = self.app_controller.playback_lock
        
        # 音频播放控制器现在由TTSManager直接管理
        self.audio_player = self.app_controller.AudioPlaybackController(self.player, self.app_controller)
        self.audio_player.playback_finished_signal.connect(self._audio_playback_manager)
        
        # --- TTS 服务特定状态 ---
        self._current_gag_model_loaded = {"gpt": None, "sovits": None}
        self._current_ref_audio_path: Optional[str] = None
        self._current_ref_text: Optional[str] = None
        
        # --- 网络请求配置 ---
        self.request_timeout = self.tts_config_manager.get_global_settings().get('request_timeout', 30)
        self.max_retries = self.tts_config_manager.get_global_settings().get('max_retries', 3)

        self._initialize_speaker_references()

    def start_worker_thread(self):
        """启动后台工作线程"""
        if not hasattr(self, 'tts_worker_thread') or not self.tts_worker_thread.is_alive():
            self.tts_worker_thread = threading.Thread(target=self._tts_worker, daemon=True)
            self.tts_worker_thread.start()
            print("TTSManager: Worker thread started.")

    def cleanup(self):
        """在应用退出时清理资源"""
        print("TTSManager: Cleaning up...")
        self.tts_cancellation_event.set()
        self.tts_sentence_queue.put(None) # 发送哨兵值以停止工作线程
        if self.player:
            self.player.stop()
        print("TTSManager: Cleanup complete.")
        
    def _initialize_speaker_references(self):
        """初始化当前选用音色的参考音频和文本"""
        current_service_info = self.tts_config_manager.get_current_service_info()
        if current_service_info and current_service_info.type == 'gpt_sovits':
            current_model_info = self.tts_config_manager.get_current_model_for_service(current_service_info.id)
            if current_model_info:
                self._current_ref_audio_path = current_model_info.get("reference_audio", "")
                self._current_ref_text = current_model_info.get("reference_text", "")
                print(f"TTSManager: Initialized speaker '{current_model_info['name']}' reference to: '{self._current_ref_audio_path}', '{self._current_ref_text}'")

    # --- 核心 TTS 流水线方法 ---

    def queue_text_for_speech(self, text: str):
        """
        接收完整文本，清理、切分后加入TTS流水线。这是外部调用的主入口。
        """
        if not text or not self.get_tts_enabled():
            # 如果TTS被禁用或文本为空，也要确保允许ASR继续
            if hasattr(self.app_controller, 'asr_continue_event'):
                self.app_controller.asr_continue_event.set()
            return

        # 1. 中断任何正在进行的TTS任务
        self.stop_playback(triggered_by_user_action=False)

        # 2. 清除"停止"信号旗，允许新的TTS任务开始
        self.tts_cancellation_event.clear()

        # 3. 清理和切分文本
        cleaned_text = self._clean_text_for_tts(text)
        if not cleaned_text:
            # 如果没有可读的文本，也要允许ASR继续
            if hasattr(self.app_controller, 'asr_continue_event'):
                self.app_controller.asr_continue_event.set()
            return

        sentences = re.split(r'([,.?!;。，、？！；\n])', cleaned_text)
        processed_sentences = []
        if len(sentences) > 1:
            processed_sentences.extend(["".join(i) for i in zip(sentences[0::2], sentences[1::2])])
            if len(sentences) % 2 == 1:
                last_sentence = sentences[-1].strip()
                if last_sentence:
                    processed_sentences.append(last_sentence)
        else:
            processed_sentences = [cleaned_text] if cleaned_text.strip() else []
            
        # 4. 将句子加入队列
        if not processed_sentences:
            # 如果处理后没有句子，也要允许ASR继续
            if hasattr(self.app_controller, 'asr_continue_event'):
                self.app_controller.asr_continue_event.set()
            return

        for sentence in processed_sentences:
            sentence = sentence.strip()
            if sentence:
                self.tts_sentence_queue.put(sentence)

    def _tts_worker(self):
        """
        后台工作线程(生产者)，从文本队列获取句子，合成音频后放入音频队列。
        """
        while True:
            if self.tts_cancellation_event.is_set():
                time.sleep(0.1)
                continue
            
            try:
                sentence = self.tts_sentence_queue.get(timeout=0.5)
                if sentence is None:
                    break

                if self.tts_cancellation_event.is_set():
                    self.tts_sentence_queue.task_done()
                    continue

                audio_data = self._synthesize_speech(sentence)
                if audio_data and not self.tts_cancellation_event.is_set():
                    self.tts_audio_queue.put(audio_data)
                    self.audio_chunk_ready_signal.emit()
                
                self.tts_sentence_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"TTS worker error: {e}")
                traceback.print_exc()
                if 'sentence' in locals() and self.tts_sentence_queue.unfinished_tasks > 0:
                    self.tts_sentence_queue.task_done()

    def _audio_playback_manager(self):
        """
        音频播放管理器(消费者)，当一句播放完毕或被手动触发时调用。
        """
        if not self.playback_lock.acquire(blocking=False):
            return

        try:
            if self.tts_cancellation_event.is_set():
                if self.playback_lock.locked():
                    self.playback_lock.release()
                return
            
            if not self.tts_audio_queue.empty():
                audio_data = self.tts_audio_queue.get_nowait()
                if self.tts_cancellation_event.is_set():
                    if self.playback_lock.locked():
                        self.playback_lock.release()
                    self.tts_audio_queue.task_done()
                    return

                # 成功获取音频，开始播放。锁将在播放结束后由 AudioPlaybackController 的回调函数释放。
                self.audio_player.play(audio_data)
                self.tts_audio_queue.task_done()
            else:
                self.playback_lock.release()
                # 当所有音频都播放完毕时，允许ASR继续
                if self.tts_sentence_queue.empty():
                     if hasattr(self.app_controller, 'asr_continue_event'):
                        print("TTSManager: All audio played, setting ASR continue event.")
                        self.app_controller.asr_continue_event.set()

        except queue.Empty:
            if self.playback_lock.locked():
                self.playback_lock.release()
        except Exception as e:
            print(f"Audio playback manager error: {e}")
            traceback.print_exc()
            if self.playback_lock.locked():
                self.playback_lock.release()

    def stop_playback(self, triggered_by_user_action: bool = True):
        """
        停止当前的TTS播放和相关的口型同步。
        """
        try:
            print("TTSManager: Attempting to stop TTS playback...")
            self.tts_cancellation_event.set()
            
            # 清空队列
            while not self.tts_sentence_queue.empty():
                self.tts_sentence_queue.get_nowait()
            while not self.tts_audio_queue.empty():
                self.tts_audio_queue.get_nowait()

            is_actually_playing = self.is_playing()

            if not is_actually_playing:
                if triggered_by_user_action and self.app_controller.chat_window and self.tts_config_manager.get_global_settings().get("show_stop_confirmation", False):
                    self.app_controller.display_error_message("当前没有语音在播放。", is_critical=False)
                return

            self.audio_player.stop() # 这将触发_on_media_status_changed来停止口型同步和释放锁
            
            if triggered_by_user_action and self.app_controller.chat_window and self.tts_config_manager.get_global_settings().get("show_stop_confirmation", False):
                 self.app_controller.display_error_message("语音播放已停止。", is_critical=False)
                 
        except Exception as e:
            print(f"TTSManager: Error occurred while stopping TTS playback: {e}")
            traceback.print_exc()
            
    def is_playing(self) -> bool:
        """检查音频是否正在播放"""
        return self.audio_player.is_playing() if self.audio_player else False

    # --- 音频处理与播放 ---
    
    def _play_audio_data(self, audio_data: bytes, start_lipsync_flag: bool = True) -> bool:
        """核心音频播放方法，处理格式检测、转换和口型同步启动。"""
        if not audio_data:
            print("错误: _play_audio_data 收到空音频数据。")
            return False

        detected_format = self._detect_audio_format(audio_data)
        if not detected_format:
            print("错误: 无法播放，因为无法检测到音频格式。")
            self.app_controller.display_error_message("无法播放，因为无法检测到音频格式。", False)
            return False

        temp_audio_path = self.temp_file_manager.create_temp_file(data=audio_data, suffix=f".{detected_format}")
        wav_file_path_for_lipsync = self._ensure_wav_format(temp_audio_path, audio_data, detected_format)
        
        media_content = QMediaContent(QUrl.fromLocalFile(temp_audio_path))
        
        if self.player:
            self.player.setMedia(media_content)
            self.player.play()
            if start_lipsync_flag and wav_file_path_for_lipsync:
                self._start_lipsync(wav_file_path_for_lipsync)
            return True
        
        print("错误: 播放器对象(self.player)未找到。")
        return False

    def _detect_audio_format(self, audio_data: bytes) -> str:
        """根据音频数据的'魔术数字'检测格式"""
        if not isinstance(audio_data, bytes) or len(audio_data) < 16: return ""
        if audio_data.startswith(b'RIFF') and audio_data[8:12] == b'WAVE': return "wav"
        if audio_data.startswith(b'\xff\xfb') or audio_data.startswith(b'ID3') or audio_data.startswith(b'\xff\xf3'): return "mp3"
        if audio_data.startswith(b'OggS'): return "ogg"
        if audio_data.startswith(b'fLaC'): return "flac"
        print("警告: 无法检测音频格式")
        return ""

    def _ensure_wav_format(self, original_path: str, audio_data: bytes, detected_format: str) -> Optional[str]:
        """确保音频文件是WAV格式，用于口型同步。如果不是，则进行转换。"""
        if detected_format == "wav":
            return original_path
        
        if not PYDUB_AVAILABLE:
            print("错误: pydub 库未安装，无法将音频转换为WAV格式进行口型同步。请运行 pip install pydub")
            return None
            
        if detected_format in ["mp3", "ogg", "flac"]:
            try:
                audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format=detected_format)
                # 使用唯一的临时文件名
                output_wav_path = self.temp_file_manager.create_temp_file(data=b'', suffix="_converted.wav")
                audio_segment.export(output_wav_path, format="wav")
                return output_wav_path
            except Exception as e:
                print(f"错误: 音频转换为WAV时出错: {e}")
                traceback.print_exc()
                return None
        return None

    # --- 口型同步 ---

    def _start_lipsync(self, wav_file_path: str):
        """启动口型同步"""
        if self.app_controller.live2d_widget:
            self.app_controller.live2d_widget.start_lipsync_from_file(wav_file_path)

    def _stop_lipsync(self):
        """停止口型同步"""
        if self.app_controller.live2d_widget:
            self.app_controller.live2d_widget.stop_lipsync()
            
    # --- 文本清理 ---
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Cleans text for TTS by removing various non-speakable elements."""
        if not text: return ""
        cleaned_text = text
        # 移除工具和日志块
        cleaned_text = re.sub(r"<<<.*?>>>[\s\S]*?<<<.*?>>>", "", cleaned_text, flags=re.DOTALL)
        # 移除HTML img标签
        cleaned_text = re.sub(r"<img[^>]*>", "", cleaned_text).strip()
        # 移除代码块
        cleaned_text = re.sub(r"```[\s\S]*?```", "", cleaned_text).strip()
        cleaned_text = re.sub(r"`[^`]*`", "", cleaned_text).strip()
        # 移除Markdown格式
        cleaned_text = re.sub(r"[\*_#`~]", "", cleaned_text)
        # 移除链接 [text](url) -> text
        cleaned_text = re.sub(r"\[([^\]]+)\]\([^)]+\)", r"\1", cleaned_text)
        # 合并空白
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        return cleaned_text

    # --- TTS 服务管理和调用 ---
    
    def get_tts_enabled(self) -> bool:
        return self.tts_config_manager.get_tts_enabled()

    def set_tts_enabled(self, enabled: bool) -> bool:
        return self.tts_config_manager.set_tts_enabled(enabled)

    def get_available_tts_services(self) -> List[Dict]:
        services = self.tts_config_manager.get_available_services()
        return [s.__dict__ for s in services]
        
    def get_service_config(self, service_id: str) -> Optional[Dict[str, Any]]:
        return self.tts_config_manager.get_service_config(service_id)

    def get_service_info(self, service_id: str) -> Optional[Any]:
        return self.tts_config_manager.get_service_info(service_id)

    def get_current_tts_service(self) -> Optional[Dict[str, str]]:
        current_service = self.tts_config_manager.get_current_service_info()
        return current_service.__dict__ if current_service else None

    def set_tts_service(self, service_id: str) -> bool:
        if self.tts_config_manager.set_current_service(service_id):
            print(f"✓ TTS服务切换成功: {service_id}")
            return True
        return False

    def get_tts_service_speakers(self, service_id: str):
        return self.tts_config_manager.get_available_speakers_for_service(service_id)

    def get_current_speaker_for_service(self, service_id: str) -> Optional[str]:
        return self.tts_config_manager.get_current_speaker_for_service(service_id)

    def set_current_speaker_for_service(self, service_id: str, speaker_name: str) -> bool:
        """设置音色并更新相关状态"""
        success = self.tts_config_manager.set_current_speaker_for_service(service_id, speaker_name)
        if not success:
            return False

        service_info = self.tts_config_manager.get_current_service_info()
        if service_info and service_info.id == service_id and service_info.type == 'gpt_sovits':
            model_info = self.tts_config_manager.get_current_model_for_service(service_id)
            if model_info:
                new_ref_audio = model_info.get("reference_audio", "")
                new_ref_text = model_info.get("reference_text", "")
                self._current_ref_audio_path = new_ref_audio
                self._current_ref_text = new_ref_text
                self.speaker_reference_updated_signal.emit(new_ref_audio, new_ref_text)
        return True

    def test_tts_service(self, service_id: str, text: str, speaker: Optional[str] = None, **kwargs) -> Tuple[bool, str]:
        """测试指定的TTS服务。"""
        if not text:
            return False, "测试文本不能为空。"
        try:
            audio_data = self._synthesize_speech(
                text_to_speak=text,
                service_id_override=service_id,
                speaker_override=speaker,
                **kwargs
            )
            if audio_data:
                # 为了不干扰主播放流程，我们将音频数据直接发送给主控制器的播放信号
                self.app_controller.tts_play_audio_signal.emit(audio_data)
                return True, "测试成功，已开始播放。"
            else:
                return False, "语音合成失败，请检查控制台错误日志。"
        except Exception as e:
            error_message = f"测试时发生意外错误: {e}"
            print(error_message)
            traceback.print_exc()
            return False, error_message
            
    def update_current_speaker_reference(self, audio_path: str, text: str):
        """由UI调用，更新并保存当前音色的参考设置"""
        current_service_info = self.tts_config_manager.get_current_service_info()
        if not (current_service_info and current_service_info.type == 'gpt_sovits'):
            return

        current_model_name = self.tts_config_manager.get_current_speaker_for_service(current_service_info.id)
        if not current_model_name:
            return

        self._current_ref_audio_path = audio_path
        self._current_ref_text = text

        self.tts_config_manager.update_gag_model_reference(
            service_id=current_service_info.id,
            model_name=current_model_name,
            ref_audio_path=audio_path,
            ref_text=text
        )

    # --- 语音合成核心调度 ---
    
    def _synthesize_speech(self, text_to_speak: str, service_id_override: Optional[str] = None, speaker_override: Optional[str] = None, **kwargs) -> Optional[bytes]:
        """根据当前或指定的TTS服务配置合成语音。"""
        if not self.get_tts_enabled(): return None

        service_id = service_id_override or self.tts_config_manager.current_service_id
        config = self.tts_config_manager.get_service_config(service_id)
        if not config:
            self.app_controller.display_error_message(f"未找到TTS服务配置: {service_id}", False)
            return None

        service_type = config.get("service_type")
        speaker = speaker_override or self.tts_config_manager.get_current_speaker_for_service(service_id)
        
        final_kwargs = kwargs.copy()
        final_kwargs['speaker'] = speaker

        if service_type == 'gpt_sovits':
            return self._synthesize_speech_gpt_sovits(config, text_to_speak, **final_kwargs)
        elif service_type == 'local_http_vits':
            return self._synthesize_speech_local_http(config, text_to_speak, **final_kwargs)
        elif service_type == 'huggingface_space_vits':
            return self._synthesize_speech_hf_space(config, text_to_speak, **final_kwargs)
        elif service_type == 'edge_tts_python_lib':
            return asyncio.run(self._synthesize_speech_edge_tts_online(config, text_to_speak, **final_kwargs))
        
        print(f"警告: 未知的TTS服务类型 '{service_type}'")
        return None

    # --- 各类TTS服务实现 ---

    def _synthesize_speech_gpt_sovits(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        """ GAG / GPT-SoVITS 服务合成 """
        # 获取 GAG 相关配置
        gpt_path = kwargs.get("gpt_path")
        sovits_path = kwargs.get("sovits_path")
        
        # 动态获取当前选定模型的路径
        current_model_config = self.tts_config_manager.get_current_model_for_service(config['id'])
        if not current_model_config:
            self.app_controller.display_error_message(f"GAG服务错误: 未找到当前模型的配置", False)
            return None
        gpt_path = current_model_config.get("gpt_path")
        sovits_path = current_model_config.get("sovits_path")

        try:
            # 切换模型
            if self._current_gag_model_loaded.get("gpt") != gpt_path:
                switch_gpt_url = urljoin(config.get("api_base_url"), f"/set_gpt_weights?weights_path={url_quote(gpt_path)}")
                response_gpt = self._make_request_with_retry("get", switch_gpt_url, max_retries=1, timeout=60)
                if response_gpt and response_gpt.status_code == 200 and '"success"' in response_gpt.text:
                    self._current_gag_model_loaded["gpt"] = gpt_path
                else:
                    raise Exception(f"GAG GPT模型切换失败: {response_gpt.text if response_gpt else '请求失败'}")

            if self._current_gag_model_loaded.get("sovits") != sovits_path:
                switch_sovits_url = urljoin(config.get("api_base_url"), f"/set_sovits_weights?weights_path={url_quote(sovits_path)}")
                response_sovits = self._make_request_with_retry("get", switch_sovits_url, max_retries=1, timeout=60)
                if response_sovits and response_sovits.status_code == 200 and '"success"' in response_sovits.text:
                    self._current_gag_model_loaded["sovits"] = sovits_path
                else:
                    raise Exception(f"GAG SoVITS模型切换失败: {response_sovits.text if response_sovits else '请求失败'}")
        except Exception as e:
            self.app_controller.display_error_message(str(e), False)
            return None

        # 准备合成请求
        tts_params = {
            "text": text,
            "text_lang": "zh",
            "ref_audio_path": self._current_ref_audio_path or config.get("default_ref_audio_path"),
            "prompt_text": self._current_ref_text or config.get("default_prompt_text"),
            "prompt_lang": "zh",
            "media_type": "wav"
        }
        tts_url = urljoin(config.get("api_base_url"), "/tts")
        
        response = self._make_request_with_retry("get", tts_url, params=tts_params, timeout=60, stream=True)
        if response and response.status_code == 200:
            return response.content
        else:
            self.app_controller.display_error_message(f"GAG语音合成失败: {response.text if response else '请求失败'}", False)
            return None

    def _synthesize_speech_local_http(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        """本地HTTP VITS服务合成"""
        api_url = f"{config.get('api_url', 'http://127.0.0.1:23456/voice/vits')}"
        payload = {
            "text": text,
            "speaker": kwargs.get('speaker', config.get('default_speaker', 'siyuan')),
            # 添加其他VITS特定参数
        }
        response = self._make_request_with_retry(method="GET", url=api_url, params=payload)
        return response.content if response and response.status_code == 200 else None

    def _synthesize_speech_hf_space(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        """Hugging Face Space VITS服务合成"""
        try:
            base_url = config["api_base_url"]
            generate_endpoint = config["generate_endpoint"]
            default_params = config.get("default_params", {})
            
            hf_data_list = [
                text,
                kwargs.get('speaker', default_params.get("speaker", "default")),
                kwargs.get('language', default_params.get("language", "ZH")),
                float(kwargs.get('noise_scale', default_params.get("noise_scale", 0.6))),
                float(kwargs.get('noise_scale_w', default_params.get("noise_scale_w", 0.668))),
                float(kwargs.get('length_scale', default_params.get("length_scale", 1.0))),
                float(kwargs.get('sdp_ratio', default_params.get("sdp_ratio", 0.2))),
                kwargs.get('format', default_params.get("format", "wav"))
            ]
            final_request_json = {"data": hf_data_list}
            generate_url = urljoin(base_url, generate_endpoint.lstrip('/'))
            
            response = self._make_request_with_retry(method="POST", url=generate_url, json=final_request_json)
            
            if not response or response.status_code != 200: return None
            
            response_data = response.json()
            filename = response_data.get("data", [None, {}])[1].get("name")
            if not filename: return None
            
            file_endpoint_template = config["file_endpoint_template"]
            download_url = urljoin(base_url, file_endpoint_template.format(filename=filename).lstrip('/'))
            download_response = self._make_request_with_retry(method="GET", url=download_url)
            
            return download_response.content if download_response and download_response.status_code == 200 else None
        except Exception as e:
            print(f"HF Space TTS合成异常: {e}")
            return None

    async def _synthesize_speech_edge_tts_online(self, config: Dict, text: str, **kwargs) -> Optional[bytes]:
        """Edge TTS服务合成"""
        try:
            voice = kwargs.get('speaker') or config.get("default_speaker")
            rate = kwargs.get('rate', config.get("default_rate", "+0%"))
            pitch = kwargs.get('pitch', config.get("default_pitch", "+0Hz"))
            volume = kwargs.get('volume', config.get("default_volume", "+0%"))

            communicate = edge_tts.Communicate(text, voice, rate=str(rate), pitch=str(pitch), volume=str(volume))
            
            audio_bytes_io = io.BytesIO()
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_bytes_io.write(chunk["data"])

            return audio_bytes_io.getvalue()
        except Exception as e:
            print(f"Edge TTS 合成异常: {e}")
            return None

    # --- 网络请求 ---
    
    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> Optional[requests.Response]:
        """带重试机制的网络请求"""
        kwargs.setdefault('timeout', self.request_timeout)
        retries = self.max_retries
        
        for attempt in range(retries + 1):
            try:
                response = requests.request(method.upper(), url, **kwargs)
                if response.status_code == 200:
                    return response
                if response.status_code in [500, 502, 503, 504] and attempt < retries:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    print(f"请求失败，状态码: {response.status_code}")
                    return response
            except (requests.Timeout, requests.ConnectionError) as e:
                if attempt < retries:
                    time.sleep(2 ** attempt)
                else:
                    print(f"网络请求最终失败: {e}")
                    return None
            except Exception as e:
                print(f"请求时发生未知异常: {e}")
                return None
        return None

如何使用这个新文件

接下来，您需要修改您的 main.py 文件来使用这个 TTSManager。这通常包括：

导入：在 main.py 顶部添加 from core.tts_manager import TTSManager。

实例化：在 AppController 的 __init__ 方法中，创建 TTSManager 的实例：

self.tts_manager = TTSManager(self)
self.tts_manager.start_worker_thread() # 启动工作线程
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

删除旧代码：从 AppController 中删除所有您刚刚在 tts_manager.py 中实现的方法。

更新调用：将 AppController 中对TTS方法的调用（如 self.queue_text_for_speech(...)）改为 self.tts_manager.queue_text_for_speech(...)。

当您准备好进行下一步时，请将您修改后的 main.py 文件发给我，我会帮您检查并确保一切都已正确连接。