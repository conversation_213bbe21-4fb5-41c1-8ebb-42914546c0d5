import os
import sys
import tempfile
from typing import Optional, List

# 这是一个示例，实际的 AudioSegment 可能来自 pydub 或其他库
# 为了代码静态检查和可读性，我们先定义一个虚拟的类
try:
    from pydub import AudioSegment
except ImportError:
    print("警告: pydub 未安装，configure_ffmpeg_path 功能将受限。")
    # 定义一个虚拟类以避免 NameError
    class AudioSegment:
        converter = None
        ffmpeg = None
        ffprobe = None

# +++ 新增：定义固定的图片缓存路径 +++
# 为了跨平台兼容性和避免转义问题，最好使用原始字符串(r"...")或正斜杠
FIXED_IMAGE_CACHE_DIR = r"E:\live2d\aipet\aipet_image_cache"
# --- 结束新增 ---
IMAGE_CACHE_DIR = "aipet_image_cache"  # 保留原有常量以兼容
TRAY_ICON_PATH_FROM_ROOT = "aipet/assets/tray_icon.png"
MODEL_CONFIG_PATH_FROM_ROOT = "linghu/芊芊.model3.json"
USER_DATA_FILE_FROM_ROOT = "aipet_user_data.json"
LIVE2D_API_PORT = 26666

API_BASE_URL = "https://vcp.012255.xyz/v1" 
DEFAULT_API_MODEL_NAME = "deepseek-reasoner" 
API_KEY ="123456"

# 步骤 3.2 添加临时文件管理器类
class TempFileManager:
    """临时文件管理器"""
    
    def __init__(self, max_files: int = 10, temp_dir: Optional[str] = None):
        self.max_files = max_files
        self.temp_files: List[str] = []
        
        if temp_dir and os.path.isdir(os.path.dirname(temp_dir)):
            self.temp_dir = temp_dir
        else:
            # 如果未提供有效目录，则默认在 aipet 文件夹下创建 temp 目录
            aipet_dir = os.path.dirname(os.path.abspath(__file__))
            self.temp_dir = os.path.join(aipet_dir, "temp")
            
        # 确保目录存在
        if not os.path.exists(self.temp_dir):
            try:
                os.makedirs(self.temp_dir)
                print(f"✓ 创建临时文件目录: {self.temp_dir}")
            except OSError as e:
                print(f"错误: 创建临时目录 {self.temp_dir} 失败: {e}")
                # Fallback to default system temp dir if custom one fails
                self.temp_dir = tempfile.gettempdir()

    def create_temp_file(self, data: bytes, suffix: str = '.wav') -> str:
        """创建临时文件并管理文件数量"""
        self._cleanup_old_files()
        
        # 使用指定的目录创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix, dir=self.temp_dir) as temp_file_obj:
            temp_file_obj.write(data)
            temp_path = temp_file_obj.name
        
        self.temp_files.append(temp_path)
        return temp_path
    
    def _cleanup_old_files(self):
        """清理旧的临时文件"""
        while len(self.temp_files) >= self.max_files:
            old_file = self.temp_files.pop(0)
            try:
                if os.path.exists(old_file):
                    os.remove(old_file)
            except OSError as e:
                print(f"警告: 无法删除临时文件 {old_file}: {e}")
    
    def cleanup_all(self):
        """清理所有临时文件"""
        for temp_file_path_item in self.temp_files:
            try:
                if os.path.exists(temp_file_path_item):
                    os.remove(temp_file_path_item)
            except OSError as e:
                print(f"警告: 无法删除临时文件 {temp_file_path_item}: {e}")
        self.temp_files.clear()

def resource_path(relative_path_from_project_root: str) -> str:
    """
    获取资源的绝对路径，对开发环境和PyInstaller打包环境均有效。
    'relative_path_from_project_root' 是从项目根目录指定的路径。
    例如: 'linghu/芊芊.model3.json'
    """
    if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    else:
        # 在开发环境中，我们假定此文件位于 aipet/core/ 目录下
        # 项目根目录是 aipet/core/ 的上两级目录
        core_dir = os.path.dirname(os.path.abspath(__file__))
        aipet_dir = os.path.dirname(core_dir)
        project_root = os.path.dirname(aipet_dir)
        base_path = project_root
        
    return os.path.join(base_path, relative_path_from_project_root)

# 配置 FFmpeg 路径用于 pydub
def configure_ffmpeg_path():
    """配置 pydub 使用的 FFmpeg 路径"""
    try:
        # 获取打包后的 ffmpeg 可执行文件路径
        ffmpeg_path = resource_path("ffmpeg-7.1.1-essentials_build/bin/ffmpeg.exe")
        ffprobe_path = resource_path("ffmpeg-7.1.1-essentials_build/bin/ffprobe.exe")
        
        # 检查文件是否存在
        if os.path.exists(ffmpeg_path) and os.path.exists(ffprobe_path):
            # 配置 pydub 使用我们打包的 ffmpeg
            from pydub.utils import which
            AudioSegment.converter = ffmpeg_path
            AudioSegment.ffmpeg = ffmpeg_path
            AudioSegment.ffprobe = ffprobe_path
            print(f"✓ FFmpeg 路径配置成功: {ffmpeg_path}")
            return True
        else:
            print(f"警告: FFmpeg 文件未找到: {ffmpeg_path}")
            # 作为后备，尝试在系统PATH中查找
            if which("ffmpeg"):
                print("✓ 使用系统PATH中的FFmpeg。")
                return True
            print("警告: 系统PATH中也未找到ffmpeg，pydub功能可能受限。")
            return False
    except ImportError:
        print("警告: pydub 未安装，跳过 FFmpeg 配置")
        return False
    except Exception as e:
        print(f"配置 FFmpeg 路径时出错: {e}")
        return False