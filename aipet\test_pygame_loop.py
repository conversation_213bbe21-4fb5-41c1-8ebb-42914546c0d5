#!/usr/bin/env python3
"""
测试使用pygame内置循环的单曲循环功能
"""

from plugin_manager import PluginManager
import time

def main():
    print("🎵 测试pygame内置循环功能")
    print("=" * 50)
    
    pm = PluginManager()
    pm.discover_and_load_plugins()
    
    print("\n🎵 先播放一首歌（正常模式）:")
    result = pm.execute_tool('play_music', '{"music_name": "赤伶"}')
    print(result)
    
    time.sleep(3)
    
    print("\n🔄 切换到单曲循环模式:")
    result = pm.execute_tool('set_play_mode', '{"mode": "repeat_one"}')
    print(result)
    
    print("\n📊 检查播放状态:")
    result = pm.execute_tool('get_player_status', '{}')
    print(result)
    
    print("\n⏰ 等待20秒，观察是否使用了pygame内置循环...")
    for i in range(20):
        time.sleep(1)
        if i % 5 == 0:
            print(f"⏰ 已等待 {i+1} 秒")
    
    print("\n📊 最终状态检查:")
    result = pm.execute_tool('get_player_status', '{}')
    print(result)
    
    print("\n⏹️ 停止播放:")
    result = pm.execute_tool('stop_music', '{}')
    print(result)
    
    print("\n🔄 恢复正常模式:")
    result = pm.execute_tool('set_play_mode', '{"mode": "normal"}')
    print(result)
    
    print("\n🎉 测试完成！")
    print("如果看到'🔄 使用pygame内置循环播放'消息，说明新方法正在工作。")

if __name__ == "__main__":
    main()
