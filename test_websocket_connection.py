#!/usr/bin/env python3
"""
WebSocket 连接测试脚本
用于测试和诊断 VCPLog WebSocket 连接问题
"""

import sys
import os
import time
import json
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer, pyqtSlot

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv('aipet/config.env')

from aipet.websocket_client import VCPLogClient

class WebSocketTestWindow(QWidget):
    """WebSocket 连接测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.vcplog_client = None
        self.connection_start_time = None
        self.message_count = 0
        self.setup_ui()
        self.setup_websocket()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("WebSocket 连接测试")
        self.setGeometry(100, 100, 700, 600)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("VCPLog WebSocket 连接测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 连接信息
        self.info_label = QLabel()
        self.update_connection_info()
        self.info_label.setStyleSheet("background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 10px;")
        layout.addWidget(self.info_label)
        
        # 控制按钮
        button_layout = QVBoxLayout()
        
        self.connect_btn = QPushButton("连接 WebSocket")
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.connect_btn)
        
        self.test_btn = QPushButton("发送测试消息")
        self.test_btn.clicked.connect(self.send_test_message)
        self.test_btn.setEnabled(False)
        self.test_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        button_layout.addWidget(self.test_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        log_label = QLabel("连接日志:")
        log_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #444;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 状态显示
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("color: #666; margin: 10px; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 定时更新连接信息
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_connection_info)
        self.update_timer.start(1000)  # 每秒更新
    
    def setup_websocket(self):
        """设置WebSocket客户端"""
        try:
            # 从环境变量读取配置
            server_url = os.getenv('VCPLOG_SERVER_URL', 'ws://103.189.141.240:6005')
            vcp_key = os.getenv('VCP_Key', '123456')
            
            self.vcplog_client = VCPLogClient(server_url, vcp_key, self)
            
            # 连接信号
            self.vcplog_client.connection_status_changed.connect(self.on_connection_status_changed)
            self.vcplog_client.error_occurred.connect(self.on_error_occurred)
            self.vcplog_client.vcp_log_received.connect(self.on_vcp_log_received)
            self.vcplog_client.agent_message_received.connect(self.on_agent_message_received)
            
            self.log(f"WebSocket 客户端已初始化")
            self.log(f"服务器地址: {server_url}")
            self.log(f"VCP密钥: {vcp_key}")
            self.log(f"完整URL: {self.vcplog_client.url}")
            
        except Exception as e:
            self.log(f"初始化WebSocket客户端失败: {e}")
    
    def update_connection_info(self):
        """更新连接信息"""
        if not self.vcplog_client:
            return
        
        status = self.vcplog_client.get_connection_status()
        
        info_text = f"""
        连接状态: {'已连接' if status['is_connected'] else '未连接'}
        重连次数: {status['reconnect_attempts']}/{status['max_reconnect_attempts']}
        服务器地址: {status['url']}
        最后心跳: {time.strftime('%H:%M:%S', time.localtime(status['last_heartbeat']))}
        """
        
        if self.connection_start_time and status['is_connected']:
            uptime = time.time() - self.connection_start_time
            info_text += f"\n连接时长: {uptime:.0f}秒"
        
        info_text += f"\n收到消息数: {self.message_count}"
        
        self.info_label.setText(info_text.strip())
    
    @pyqtSlot()
    def toggle_connection(self):
        """切换连接状态"""
        if not self.vcplog_client:
            return
        
        if self.vcplog_client.is_connected:
            self.vcplog_client.disconnect()
            self.connect_btn.setText("连接 WebSocket")
            self.test_btn.setEnabled(False)
            self.log("手动断开连接")
        else:
            self.vcplog_client.connect()
            self.connect_btn.setText("断开 WebSocket")
            self.log("开始连接...")
    
    @pyqtSlot()
    def send_test_message(self):
        """发送测试消息"""
        if self.vcplog_client and self.vcplog_client.is_connected and self.vcplog_client.ws:
            try:
                test_msg = {
                    "type": "test",
                    "message": "这是一条测试消息",
                    "timestamp": int(time.time())
                }
                self.vcplog_client.ws.send(json.dumps(test_msg))
                self.log("已发送测试消息")
            except Exception as e:
                self.log(f"发送测试消息失败: {e}")
    
    @pyqtSlot(bool)
    def on_connection_status_changed(self, is_connected):
        """连接状态变化处理"""
        if is_connected:
            self.connection_start_time = time.time()
            self.status_label.setText("状态: 已连接")
            self.status_label.setStyleSheet("color: #4CAF50; margin: 10px; font-size: 12px;")
            self.connect_btn.setText("断开 WebSocket")
            self.test_btn.setEnabled(True)
            self.log("✅ WebSocket 连接成功")
        else:
            self.connection_start_time = None
            self.status_label.setText("状态: 未连接")
            self.status_label.setStyleSheet("color: #f44336; margin: 10px; font-size: 12px;")
            self.connect_btn.setText("连接 WebSocket")
            self.test_btn.setEnabled(False)
            self.log("❌ WebSocket 连接断开")
    
    @pyqtSlot(str)
    def on_error_occurred(self, error_message):
        """错误处理"""
        self.log(f"🚨 错误: {error_message}")
    
    @pyqtSlot(dict)
    def on_vcp_log_received(self, data):
        """VCP日志消息处理"""
        self.message_count += 1
        vcp_data = data.get('data', {})
        tool_name = vcp_data.get('tool_name', 'Unknown')
        status = vcp_data.get('status', 'unknown')
        self.log(f"📝 收到VCP日志: {tool_name} [{status}]")
    
    @pyqtSlot(dict)
    def on_agent_message_received(self, data):
        """Agent消息处理"""
        self.message_count += 1
        message_content = data.get('message', '')
        recipient = data.get('recipient', '')
        self.log(f"💬 收到Agent消息: {recipient} - {message_content[:50]}...")
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        print(log_entry)  # 同时输出到控制台
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.vcplog_client:
            self.vcplog_client.disconnect()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = WebSocketTestWindow()
    window.show()
    
    print("=" * 50)
    print("WebSocket 连接测试程序")
    print("=" * 50)
    print("功能说明：")
    print("1. 测试 VCPLog WebSocket 连接")
    print("2. 监控连接状态和消息")
    print("3. 诊断连接问题")
    print("=" * 50)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
