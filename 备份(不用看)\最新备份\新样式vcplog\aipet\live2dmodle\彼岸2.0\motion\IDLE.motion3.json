{"Version": 3, "Meta": {"Duration": 34.033, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 22, "TotalSegmentCount": 332, "TotalPointCount": 950, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param341", "Segments": [0, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Param342", "Segments": [0, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Param343", "Segments": [0, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Param344", "Segments": [0, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Param345", "Segments": [0, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Param348", "Segments": [0, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 34.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 34.033, 1]}, {"Target": "Parameter", "Id": "Ani4", "Segments": [0, 0, 1, 1.244, 0, 2.489, 0.329, 3.733, 0.329, 1, 5, 0.329, 6.267, 1, 7.533, 1, 1, 8.522, 1, 9.511, 0.5, 10.5, 0.5, 1, 11.667, 0.5, 12.833, 0, 14, 0, 1, 15.256, 0, 16.511, 0.329, 17.767, 0.329, 1, 19.044, 0.329, 20.322, 1, 21.6, 1, 1, 22.589, 1, 23.578, 0.5, 24.567, 0.5, 1, 25.722, 0.5, 26.878, 0, 28.033, 0, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Ani6", "Segments": [0, 0, 0, 30.267, 1, 0, 34.033, 1]}, {"Target": "Parameter", "Id": "Ani1", "Segments": [0, 0, 1, 0.078, 0, 0.156, 1.845, 0.233, 5.537, 1, 0.311, 9.229, 0.389, 11.075, 0.467, 11.075, 1, 0.544, 11.075, 0.622, 5.876, 0.7, 5.596, 1, 0.822, 5.156, 0.944, 9.56, 1.067, 9.56, 1, 1.289, 9.56, 1.511, 8.452, 1.733, 8.452, 1, 1.922, 8.452, 2.111, 7.053, 2.3, 7.053, 1, 2.911, 7.053, 3.522, 40, 4.133, 40, 1, 4.278, 40, 4.422, 45, 4.567, 45, 1, 4.956, 45, 5.344, 45, 5.733, 45, 1, 5.767, 45, 5.8, 45, 5.833, 45, 1, 5.867, 45, 5.9, 45, 5.933, 45, 1, 5.944, 45, 5.956, 35.382, 5.967, 27.22, 1, 5.989, 10.896, 6.011, 6, 6.033, 6, 1, 6.189, 6, 6.344, 6.228, 6.5, 9, 1, 6.778, 13.95, 7.056, 18.595, 7.333, 18.595, 1, 7.889, 18.595, 8.444, 13.86, 9, 11.075, 1, 9.344, 11.075, 9.689, 9.56, 10.033, 9.56, 1, 10.311, 9.56, 10.589, 15.563, 10.867, 15.563, 1, 11.044, 15.563, 11.222, 8.452, 11.4, 8.452, 1, 11.722, 8.452, 12.044, 7.053, 12.367, 7.053, 1, 12.867, 7.053, 13.367, 22.966, 13.867, 22.966, 1, 14.667, 22.966, 15.467, 40, 16.267, 40, 1, 16.733, 40, 17.2, 45, 17.667, 45, 1, 17.911, 45, 18.156, 45, 18.4, 45, 1, 18.411, 45, 18.422, 0, 18.433, 0, 1, 18.511, 0, 18.589, 1.589, 18.667, 5.537, 1, 18.733, 8.921, 18.8, 11.075, 18.867, 11.075, 1, 18.922, 11.075, 18.978, 5.596, 19.033, 5.596, 1, 19.344, 5.596, 19.656, 9.56, 19.967, 9.56, 1, 20.233, 9.56, 20.5, 8.452, 20.767, 8.452, 1, 20.922, 8.452, 21.078, 7.053, 21.233, 7.053, 1, 21.756, 7.053, 22.278, 40, 22.8, 40, 1, 22.911, 40, 23.022, 45, 23.133, 45, 1, 23.467, 45, 23.8, 45, 24.133, 45, 1, 24.156, 45, 24.178, 45, 24.2, 45, 1, 24.233, 45, 24.267, 45, 24.3, 45, 1, 24.311, 45, 24.322, 35.382, 24.333, 27.22, 1, 24.356, 10.896, 24.378, 6, 24.4, 6, 1, 24.522, 6, 24.644, 6.298, 24.767, 9, 1, 25, 14.158, 25.233, 18.595, 25.467, 18.595, 1, 25.867, 18.595, 26.267, 16.117, 26.667, 11.075, 1, 26.744, 11.075, 26.822, 9.56, 26.9, 9.56, 1, 27.144, 9.56, 27.389, 15.563, 27.633, 15.563, 1, 27.778, 15.563, 27.922, 8.452, 28.067, 8.452, 1, 28.356, 8.452, 28.644, 7.053, 28.933, 7.053, 1, 29.344, 7.053, 29.756, 22.966, 30.167, 22.966, 1, 30.844, 22.966, 31.522, 40, 32.2, 40, 1, 32.589, 40, 32.978, 45, 33.367, 45, 1, 33.578, 45, 33.789, 45, 34, 45, 1, 34.011, 45, 34.022, 45, 34.033, 45]}, {"Target": "Parameter", "Id": "Ani2", "Segments": [0, 0, 1, 1.311, 0, 2.622, 0, 3.933, 0, 1, 4.478, 0, 5.022, 0.145, 5.567, 0.6, 1, 5.689, 0.702, 5.811, 1, 5.933, 1, 1, 5.956, 1, 5.978, 0, 6, 0, 1, 6.1, 0, 6.2, 0, 6.3, 0, 1, 9.822, 0, 13.344, 0, 16.867, 0, 1, 17.378, 0, 17.889, 1, 18.4, 1, 1, 18.411, 1, 18.422, 0, 18.433, 0, 1, 19.822, 0, 21.211, 0, 22.6, 0, 1, 23.067, 0, 23.533, 0.146, 24, 0.6, 1, 24.1, 0.697, 24.2, 1, 24.3, 1, 1, 24.322, 1, 24.344, 0, 24.367, 0, 1, 24.444, 0, 24.522, 0, 24.6, 0, 1, 27.311, 0, 30.022, 0, 32.733, 0, 1, 33.156, 0, 33.578, 1, 34, 1, 1, 34.011, 1, 34.022, 1, 34.033, 1]}, {"Target": "Parameter", "Id": "Ani3", "Segments": [0, 0, 0, 2.533, 0, 1, 3.356, 0, 4.178, 16.5, 5, 30, 1, 6.833, 30, 8.667, 30, 10.5, 30, 1, 11.478, 30, 12.456, 46.5, 13.433, 60, 0, 18.867, 60, 0, 22, 90, 0, 29.867, 90, 0, 33.633, 120, 0, 34.033, 120]}, {"Target": "Parameter", "Id": "Ani7", "Segments": [0, 10.07, 1, 0.133, 9.791, 0.267, 9.56, 0.4, 9.56, 1, 0.678, 9.56, 0.956, 15.563, 1.233, 15.563, 1, 1.411, 15.563, 1.589, 8.452, 1.767, 8.452, 1, 2.089, 8.452, 2.411, 7.053, 2.733, 7.053, 1, 3.233, 7.053, 3.733, 22.966, 4.233, 22.966, 1, 5.144, 22.966, 6.056, 40, 6.967, 40, 1, 7.433, 40, 7.9, 45, 8.367, 45, 1, 8.611, 45, 8.856, 45, 9.1, 45, 1, 9.133, 45, 9.167, 0, 9.2, 0, 1, 9.256, 0, 9.311, 2.213, 9.367, 5.537, 1, 9.433, 9.526, 9.5, 11.075, 9.567, 11.075, 1, 9.622, 11.075, 9.678, 5.596, 9.733, 5.596, 1, 9.844, 5.596, 9.956, 9.56, 10.067, 9.56, 1, 10.256, 9.56, 10.444, 8.452, 10.633, 8.452, 1, 10.789, 8.452, 10.944, 7.053, 11.1, 7.053, 1, 11.622, 7.053, 12.144, 40, 12.667, 40, 1, 12.778, 40, 12.889, 45, 13, 45, 1, 13.333, 45, 13.667, 45, 14, 45, 1, 14.022, 45, 14.044, 45, 14.067, 45, 1, 14.1, 45, 14.133, 45, 14.167, 45, 1, 14.178, 45, 14.189, 35.382, 14.2, 27.22, 1, 14.222, 10.896, 14.244, 6, 14.267, 6, 1, 14.389, 6, 14.511, 6.298, 14.633, 9, 1, 14.867, 14.158, 15.1, 18.595, 15.333, 18.595, 1, 15.7, 18.595, 16.067, 13.35, 16.433, 11.075, 1, 16.711, 11.075, 16.989, 9.56, 17.267, 9.56, 1, 17.511, 9.56, 17.756, 15.563, 18, 15.563, 1, 18.144, 15.563, 18.289, 8.452, 18.433, 8.452, 1, 18.722, 8.452, 19.011, 7.053, 19.3, 7.053, 1, 19.511, 7.053, 19.722, 22.966, 19.933, 22.966, 1, 20.611, 22.966, 21.289, 40, 21.967, 40, 1, 22.356, 40, 22.744, 45, 23.133, 45, 1, 23.144, 45, 23.156, 30.208, 23.167, 30, 1, 23.211, 29.129, 23.256, 17.359, 23.3, 15, 1, 23.467, 6.176, 23.633, 0, 23.8, 0, 1, 23.878, 0, 23.956, 1.845, 24.033, 5.537, 1, 24.111, 9.229, 24.189, 11.075, 24.267, 11.075, 1, 24.344, 11.075, 24.422, 5.876, 24.5, 5.596, 1, 24.622, 5.156, 24.744, 9.56, 24.867, 9.56, 1, 25.289, 9.56, 25.711, 8.452, 26.133, 8.452, 1, 26.322, 8.452, 26.511, 7.053, 26.7, 7.053, 1, 27.533, 7.053, 28.367, 40, 29.2, 40, 1, 29.378, 40, 29.556, 45, 29.733, 45, 1, 30.2, 45, 30.667, 45, 31.133, 45, 1, 31.167, 45, 31.2, 45, 31.233, 45, 1, 31.244, 45, 31.256, 45, 31.267, 45, 1, 31.278, 45, 31.289, 0, 31.3, 0, 1, 31.333, 0, 31.367, 27.22, 31.4, 27.22, 1, 31.422, 27.22, 31.444, 6, 31.467, 6, 1, 31.656, 6, 31.844, 6.213, 32.033, 9, 1, 32.367, 13.918, 32.7, 18.595, 33.033, 18.595, 1, 33.156, 18.595, 33.278, 11.372, 33.4, 11.075, 1, 33.611, 11.075, 33.822, 10.51, 34.033, 10.07]}, {"Target": "Parameter", "Id": "Ani8", "Segments": [0, 0, 1, 2.522, 0, 5.044, 0, 7.567, 0, 1, 8.078, 0, 8.589, 1, 9.1, 1, 1, 9.133, 1, 9.167, 1, 9.2, 1, 1, 9.211, 1, 9.222, 0, 9.233, 0, 1, 10.311, 0, 11.389, 0, 12.467, 0, 1, 12.933, 0, 13.4, 0.162, 13.867, 0.6, 1, 13.922, 0.652, 13.978, 1, 14.033, 1, 1, 14.078, 1, 14.122, 1, 14.167, 1, 1, 14.189, 1, 14.211, 0, 14.233, 0, 1, 14.311, 0, 14.389, 0, 14.467, 0, 1, 17.144, 0, 19.822, 0, 22.5, 0, 1, 22.711, 0, 22.922, 1, 23.133, 1, 1, 23.178, 1, 23.222, 1, 23.267, 1, 1, 23.278, 1, 23.289, 0, 23.3, 0, 1, 23.467, 0, 23.633, 0, 23.8, 0, 1, 25.511, 0, 27.222, 0, 28.933, 0, 1, 29.578, 0, 30.222, 0.145, 30.867, 0.6, 1, 31.011, 0.702, 31.156, 1, 31.3, 1, 1, 31.311, 1, 31.322, 0, 31.333, 0, 1, 31.467, 0, 31.6, 0, 31.733, 0, 1, 32.5, 0, 33.267, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Ani9", "Segments": [0, 0, 0, 1.233, 0, 0, 3.667, 30, 0, 7.333, 30, 0, 9.1, 60, 0, 15.833, 60, 0, 20.533, 90, 0, 29.867, 90, 0, 33.633, 120, 0, 34.033, 120]}, {"Target": "Parameter", "Id": "Ani10", "Segments": [0, 0, 1, 0.078, 0, 0.156, 1.845, 0.233, 5.537, 1, 0.311, 9.229, 0.389, 11.075, 0.467, 11.075, 1, 0.544, 11.075, 0.622, 5.876, 0.7, 5.596, 1, 0.822, 5.156, 0.944, 9.56, 1.067, 9.56, 1, 1.289, 9.56, 1.511, 8.452, 1.733, 8.452, 1, 1.922, 8.452, 2.111, 7.053, 2.3, 7.053, 1, 2.911, 7.053, 3.522, 40, 4.133, 40, 1, 4.278, 40, 4.422, 45, 4.567, 45, 1, 4.956, 45, 5.344, 45, 5.733, 45, 1, 5.767, 45, 5.8, 45, 5.833, 45, 1, 5.867, 45, 5.9, 45, 5.933, 45, 1, 5.944, 45, 5.956, 35.382, 5.967, 27.22, 1, 5.989, 10.896, 6.011, 6, 6.033, 6, 1, 6.189, 6, 6.344, 6.228, 6.5, 9, 1, 6.778, 13.95, 7.056, 18.595, 7.333, 18.595, 1, 7.889, 18.595, 8.444, 13.86, 9, 11.075, 1, 9.344, 11.075, 9.689, 9.56, 10.033, 9.56, 1, 10.311, 9.56, 10.589, 15.563, 10.867, 15.563, 1, 11.044, 15.563, 11.222, 8.452, 11.4, 8.452, 1, 11.667, 8.452, 11.933, 7.053, 12.2, 7.053, 1, 12.622, 7.053, 13.044, 22.966, 13.467, 22.966, 1, 14.267, 22.966, 15.067, 40, 15.867, 40, 1, 16.333, 40, 16.8, 45, 17.267, 45, 1, 17.511, 45, 17.756, 45, 18, 45, 1, 18.011, 45, 18.022, 0, 18.033, 0, 1, 18.111, 0, 18.189, 1.589, 18.267, 5.537, 1, 18.333, 8.921, 18.4, 11.075, 18.467, 11.075, 1, 18.522, 11.075, 18.578, 5.596, 18.633, 5.596, 1, 18.744, 5.596, 18.856, 9.56, 18.967, 9.56, 1, 19.211, 9.56, 19.456, 8.452, 19.7, 8.452, 1, 19.933, 8.452, 20.167, 7.053, 20.4, 7.053, 1, 20.922, 7.053, 21.444, 40, 21.967, 40, 1, 22.078, 40, 22.189, 45, 22.3, 45, 1, 22.633, 45, 22.967, 45, 23.3, 45, 1, 23.322, 45, 23.344, 45, 23.367, 45, 1, 23.4, 45, 23.433, 45, 23.467, 45, 1, 23.478, 45, 23.489, 35.382, 23.5, 27.22, 1, 23.522, 10.896, 23.544, 6, 23.567, 6, 1, 23.689, 6, 23.811, 6.298, 23.933, 9, 1, 24.167, 14.158, 24.4, 18.595, 24.633, 18.595, 1, 25.111, 18.595, 25.589, 14.024, 26.067, 11.075, 1, 26.344, 11.075, 26.622, 9.56, 26.9, 9.56, 1, 27.144, 9.56, 27.389, 15.563, 27.633, 15.563, 1, 27.778, 15.563, 27.922, 8.452, 28.067, 8.452, 1, 28.356, 8.452, 28.644, 7.053, 28.933, 7.053, 1, 29.344, 7.053, 29.756, 22.966, 30.167, 22.966, 1, 30.844, 22.966, 31.522, 40, 32.2, 40, 1, 32.589, 40, 32.978, 45, 33.367, 45, 1, 33.578, 45, 33.789, 45, 34, 45, 1, 34.011, 45, 34.022, 45, 34.033, 45]}, {"Target": "Parameter", "Id": "Ani11", "Segments": [0, 0, 1, 1.311, 0, 2.622, 0, 3.933, 0, 1, 4.322, 0, 4.711, 0.143, 5.1, 0.6, 1, 5.2, 0.718, 5.3, 1, 5.4, 1, 1, 5.6, 1, 5.8, 1, 6, 1, 1, 6.011, 1, 6.022, 0, 6.033, 0, 1, 6.144, 0, 6.256, 0, 6.367, 0, 1, 6.467, 0, 6.567, 0, 6.667, 0, 1, 9.933, 0, 13.2, 0, 16.467, 0, 1, 16.978, 0, 17.489, 1, 18, 1, 1, 18.011, 1, 18.022, 0, 18.033, 0, 1, 19.278, 0, 20.522, 0, 21.767, 0, 1, 22.233, 0, 22.7, 0.146, 23.167, 0.6, 1, 23.267, 0.697, 23.367, 1, 23.467, 1, 1, 23.489, 1, 23.511, 0, 23.533, 0, 1, 23.611, 0, 23.689, 0, 23.767, 0, 1, 25.911, 0, 28.056, 0, 30.2, 0, 1, 30.9, 0, 31.6, 1, 32.3, 1, 1, 32.322, 1, 32.344, 1, 32.367, 1, 0, 34.033, 1]}, {"Target": "Parameter", "Id": "Ani12", "Segments": [0, 0, 1, 0.6, 0, 1.2, 0, 1.8, 0, 1, 2.289, 0, 2.778, 30.1, 3.267, 30.1, 0, 5.467, 30.1, 0, 9.667, 60, 0, 20.8, 60, 0, 25.2, 90, 0, 34.033, 90]}, {"Target": "Parameter", "Id": "Ani13", "Segments": [0, 10.07, 1, 0.133, 9.791, 0.267, 9.56, 0.4, 9.56, 1, 0.678, 9.56, 0.956, 15.563, 1.233, 15.563, 1, 1.411, 15.563, 1.589, 8.452, 1.767, 8.452, 1, 2.089, 8.452, 2.411, 7.053, 2.733, 7.053, 1, 3.189, 7.053, 3.644, 22.966, 4.1, 22.966, 1, 4.489, 22.966, 4.878, 40, 5.267, 40, 1, 5.7, 40, 6.133, 45, 6.567, 45, 1, 6.811, 45, 7.056, 45, 7.3, 45, 1, 7.333, 45, 7.367, 0, 7.4, 0, 1, 7.456, 0, 7.511, 2.213, 7.567, 5.537, 1, 7.633, 9.526, 7.7, 11.075, 7.767, 11.075, 1, 7.822, 11.075, 7.878, 5.596, 7.933, 5.596, 1, 8.044, 5.596, 8.156, 9.56, 8.267, 9.56, 1, 8.456, 9.56, 8.644, 8.452, 8.833, 8.452, 1, 9.022, 8.452, 9.211, 7.053, 9.4, 7.053, 1, 10.378, 7.053, 11.356, 40, 12.333, 40, 1, 12.444, 40, 12.556, 45, 12.667, 45, 1, 13, 45, 13.333, 45, 13.667, 45, 1, 13.689, 45, 13.711, 45, 13.733, 45, 1, 13.767, 45, 13.8, 45, 13.833, 45, 1, 13.844, 45, 13.856, 35.382, 13.867, 27.22, 1, 13.889, 10.896, 13.911, 6, 13.933, 6, 1, 14.056, 6, 14.178, 6.298, 14.3, 9, 1, 14.533, 14.158, 14.767, 18.595, 15, 18.595, 1, 15.478, 18.595, 15.956, 14.024, 16.433, 11.075, 1, 16.711, 11.075, 16.989, 9.56, 17.267, 9.56, 1, 17.511, 9.56, 17.756, 15.563, 18, 15.563, 1, 18.144, 15.563, 18.289, 8.452, 18.433, 8.452, 1, 18.722, 8.452, 19.011, 7.053, 19.3, 7.053, 1, 19.711, 7.053, 20.122, 22.966, 20.533, 22.966, 1, 21.211, 22.966, 21.889, 40, 22.567, 40, 1, 22.956, 40, 23.344, 45, 23.733, 45, 1, 23.744, 45, 23.756, 30.208, 23.767, 30, 1, 23.811, 29.129, 23.856, 17.359, 23.9, 15, 1, 24.067, 6.176, 24.233, 0, 24.4, 0, 1, 24.478, 0, 24.556, 1.845, 24.633, 5.537, 1, 24.711, 9.229, 24.789, 11.075, 24.867, 11.075, 1, 24.944, 11.075, 25.022, 5.876, 25.1, 5.596, 1, 25.222, 5.156, 25.344, 9.56, 25.467, 9.56, 1, 25.689, 9.56, 25.911, 8.452, 26.133, 8.452, 1, 26.322, 8.452, 26.511, 7.053, 26.7, 7.053, 1, 27.311, 7.053, 27.922, 40, 28.533, 40, 1, 28.678, 40, 28.822, 45, 28.967, 45, 1, 29.356, 45, 29.744, 45, 30.133, 45, 1, 30.167, 45, 30.2, 45, 30.233, 45, 1, 30.267, 45, 30.3, 45, 30.333, 45, 1, 30.344, 45, 30.356, 35.382, 30.367, 27.22, 1, 30.389, 10.896, 30.411, 6, 30.433, 6, 1, 30.589, 6, 30.744, 6.228, 30.9, 9, 1, 31.178, 13.95, 31.456, 18.595, 31.733, 18.595, 1, 32.289, 18.595, 32.844, 13.254, 33.4, 11.075, 1, 33.611, 11.075, 33.822, 10.51, 34.033, 10.07]}, {"Target": "Parameter", "Id": "Ani14", "Segments": [0, 0, 1, 1.922, 0, 3.844, 0, 5.767, 0, 1, 6.278, 0, 6.789, 1, 7.3, 1, 1, 7.333, 1, 7.367, 1, 7.4, 1, 1, 7.411, 1, 7.422, 0, 7.433, 0, 1, 9, 0, 10.567, 0, 12.133, 0, 1, 12.644, 0, 13.156, 0.159, 13.667, 0.6, 1, 13.733, 0.658, 13.8, 1, 13.867, 1, 1, 13.878, 1, 13.889, 0, 13.9, 0, 1, 13.978, 0, 14.056, 0, 14.133, 0, 1, 17.122, 0, 20.111, 0, 23.1, 0, 1, 23.311, 0, 23.522, 1, 23.733, 1, 1, 23.778, 1, 23.822, 1, 23.867, 1, 1, 23.878, 1, 23.889, 0, 23.9, 0, 1, 24.067, 0, 24.233, 0, 24.4, 0, 1, 25.711, 0, 27.022, 0, 28.333, 0, 1, 28.878, 0, 29.422, 0.252, 29.967, 0.6, 1, 30.444, 0.906, 30.922, 1, 31.4, 1, 1, 31.422, 1, 31.444, 0.001, 31.467, 0.001, 1, 31.567, 0.001, 31.667, 0.001, 31.767, 0.001, 1, 32.522, 0.001, 33.278, 0, 34.033, 0]}, {"Target": "Parameter", "Id": "Ani15", "Segments": [0, 0, 1, 2.511, 0, 5.022, 0, 7.533, 0, 1, 9.067, 0, 10.6, 30.1, 12.133, 30.1, 1, 13.133, 30.1, 14.133, 30.1, 15.133, 30.1, 1, 15.878, 30.1, 16.622, 60, 17.367, 60, 1, 20.311, 60, 23.256, 60, 26.2, 60, 0, 29.533, 90, 0, 34.033, 90]}]}