﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.18.1" />
      <PackageReference Include="NAudio" Version="2.2.1" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="resources\" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="resources\**">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

</Project>
