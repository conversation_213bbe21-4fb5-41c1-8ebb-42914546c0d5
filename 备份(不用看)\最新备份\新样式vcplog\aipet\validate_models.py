#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Live2D模型验证脚本
检查所有模型文件的完整性
"""

import os
import sys
import json
import glob

# 添加aipet目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from model_manager import ModelManager

def validate_all_models():
    """验证所有模型"""
    print("🔍 开始验证Live2D模型...")
    
    # 创建模型管理器
    manager = ModelManager()
    
    # 获取所有模型
    models = manager.get_all_models()
    
    if not models:
        print("❌ 没有找到任何模型")
        return False
    
    print(f"📋 找到 {len(models)} 个模型，开始验证...\n")
    
    valid_models = 0
    invalid_models = 0
    
    for model_id, model_info in models.items():
        print(f"🔧 验证模型: {model_info.name}")
        print(f"   ID: {model_id}")
        print(f"   路径: {model_info.path}")
        
        # 转换为绝对路径
        if not os.path.isabs(model_info.path):
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            full_path = os.path.join(project_root, model_info.path)
        else:
            full_path = model_info.path
        
        print(f"   完整路径: {full_path}")
        
        # 验证文件
        is_valid = manager._validate_model_file(full_path)
        
        if is_valid:
            print(f"   ✅ 验证通过")
            valid_models += 1
            
            # 额外检查模型文件内容
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                version = config.get('Version', 'Unknown')
                file_refs = config.get('FileReferences', {})
                groups = config.get('Groups', {})
                
                print(f"      版本: {version}")
                print(f"      Moc文件: {file_refs.get('Moc', 'None')}")
                print(f"      纹理数量: {len(file_refs.get('Textures', []))}")
                print(f"      动作组数量: {len(groups)}")
                
                # 检查表情
                expressions = []
                if 'Expressions' in file_refs:
                    expressions = file_refs['Expressions']
                print(f"      表情数量: {len(expressions)}")
                
            except Exception as e:
                print(f"      ⚠️ 读取模型配置详情失败: {e}")
        else:
            print(f"   ❌ 验证失败")
            invalid_models += 1
        
        print()  # 空行分隔
    
    # 输出总结
    print("=" * 50)
    print(f"📊 验证完成:")
    print(f"   ✅ 有效模型: {valid_models}")
    print(f"   ❌ 无效模型: {invalid_models}")
    print(f"   📈 成功率: {valid_models / len(models) * 100:.1f}%")
    
    return invalid_models == 0

def scan_for_model_files():
    """扫描并列出所有找到的模型文件"""
    print("\n🔍 扫描模型文件...")
    
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(f"扫描目录: {project_root}")
    
    # 搜索模式
    patterns = [
        os.path.join(project_root, "*", "*.model3.json"),
        os.path.join(project_root, "*", "*.model.json"),
        os.path.join(project_root, "*", "*", "*.model3.json"),
        os.path.join(project_root, "*", "*", "*.model.json"),
    ]
    
    all_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        all_files.extend(files)
    
    # 去重
    all_files = list(set(all_files))
    
    print(f"📁 找到 {len(all_files)} 个模型文件:")
    for i, file_path in enumerate(all_files):
        rel_path = os.path.relpath(file_path, project_root)
        size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        print(f"   {i+1:2d}. {rel_path} ({size:,} bytes)")
    
    return all_files

def main():
    print("=" * 60)
    print("Live2D 模型验证工具")
    print("=" * 60)
    
    # 扫描文件
    scan_for_model_files()
    
    # 验证模型
    success = validate_all_models()
    
    if success:
        print("\n🎉 所有模型验证通过！")
        return 0
    else:
        print("\n⚠️ 部分模型验证失败，请检查文件完整性")
        return 1

if __name__ == "__main__":
    sys.exit(main())